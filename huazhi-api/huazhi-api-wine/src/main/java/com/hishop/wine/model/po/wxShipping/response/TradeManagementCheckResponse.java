package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description: 响应结果模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeManagementCheckResponse {
    @SerializedName("errcode")
    private int errCode;

    @SerializedName("errmsg")
    private String errMsg;

    @SerializedName("completed")
    private boolean isCompleted; // 是否已完成结算管理确认
}
