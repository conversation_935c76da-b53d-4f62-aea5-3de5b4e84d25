package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Collection;

/**
 * 素材库 修改资源分组入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialChangeCategoryPO", description = "素材库修改资源分组入参对象")
public class MaterialChangeCategoryPO {

    @ApiModelProperty("新分组id")
    @NotNull(message = "新分组id不能为空")
    private Long newMaterialCategoryId;

    @ApiModelProperty("待变更的资源id集合")
    @NotNull(message = "待变更的资源id集合不能为空")
    @Size(min = 1, message = "请至少选择一个待变更的资源id")
    private Collection<Long> ids;
}
