package com.hishop.wine.model.po.transaction;

import com.hishop.wine.enums.TransactionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 发起退款入参
 *
 * <AUTHOR>
 * @date : 2023/8/4
 */
@Data
@ApiModel(value = "TransactionRefundPO", description = "发起退款入参")
public class TransactionRefundPO {

    @ApiModelProperty("原交易流水id")
    @NotNull(message = "原交易流水id不能为空")
    private Long orgTransactionId;

    @ApiModelProperty("退款金额")
    @NotNull(message = "退款金额不能为空")
    private BigDecimal amount;

    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull(message = "请指定业务类型")
    private TransactionEnum.BizTypeEnum bizType;

    @ApiModelProperty(value = "业务端业务编码(传订单编码)", required = true)
    @NotBlank(message = "业务类型编码不能为空")
    private String bizCode;

    @ApiModelProperty("描述")
    private String description;

}
