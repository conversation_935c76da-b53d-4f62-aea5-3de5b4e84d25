package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:物流信息项
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingItem {
    @SerializedName("tracking_no")
    private String tracking_no; // 物流单号（快递必填）

    @SerializedName("express_company")
    private String express_company; // 物流公司编码（快递必填）

    @SerializedName("item_desc")
    private String item_desc; // 商品描述（必填，≤120字符）

    @SerializedName("contact")
    private Contact contact; // 联系方式（顺丰必填）
}
