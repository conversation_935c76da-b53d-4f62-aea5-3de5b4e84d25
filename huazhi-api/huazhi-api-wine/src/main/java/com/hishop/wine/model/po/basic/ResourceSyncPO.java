package com.hishop.wine.model.po.basic;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 同步资源参数
 *
 * <AUTHOR>
 * @date : 2023/7/20
 */
@NoArgsConstructor
@Data
public class ResourceSyncPO {

    private String defaultPagePath;
    private List<ProductListPO> productList;
    private List<MenuListPO> menuList;

    @NoArgsConstructor
    @Data
    public static class ProductListPO {
        private String type;
        private String name;
        private Long id;
    }

    @NoArgsConstructor
    @Data
    public static class MenuListPO {
        private String appName;
        private Integer menuLayout;
        private DefaultIndexPO defaultIndex;
        private List<MenuDataPO> menuData;

        @NoArgsConstructor
        @Data
        public static class DefaultIndexPO {
            private String path;
            private Long id;
        }

        @NoArgsConstructor
        @Data
        public static class MenuDataPO {
            private String name;
            private String path;
            private Long id;
            private String icon;
            @JsonProperty("isShow")
            private Boolean izShow;
            @JsonProperty("isTab")
            private Boolean izTab;
            @JsonProperty("isGroup")
            private Boolean izGroup;
            private List<MenuDataPO> menus;
        }
    }
}
