package com.hishop.wine.model.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 拉新汇总vo
 *
 * <AUTHOR>
 * @date : 2023/8/28
 */
@Data
@ApiModel(value = "PullNewSummaryVO", description = "拉新汇总vo")
public class PullNewSummaryVO {

    @ApiModelProperty(value = "注册渠道")
    @NotNull(message = "注册渠道不能为空")
    private String registerChannel;

    @ApiModelProperty(value = "邀请人id")
    private Long inviterUserId;

    @ApiModelProperty("注册业务码")
    private String bizCode;

    @ApiModelProperty("拉新数量")
    private Integer number;

}
