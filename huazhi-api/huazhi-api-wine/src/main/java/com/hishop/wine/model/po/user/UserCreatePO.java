package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 用户入参
 *
 * <AUTHOR>
 * @date : 2025/7/22
 */
@Data
@ApiModel(value = "UserCreatePO", description = "用户入参")
public class UserCreatePO {

    @ApiModelProperty(value = "用户电话")
    @NotBlank(message = "用户电话不能为空")
    private String userPhone;

    @ApiModelProperty(value = "用户名称")
    @NotBlank(message = "用户名称不能为空")
    private String userName;

    @ApiModelProperty(value = "B端客户编码")
    private String bizUserCode;

    @ApiModelProperty(value = "B端客户名称")
    private String bizUserName;

    @ApiModelProperty(value = "B端客户手机号")
    private String bizPhone;
}
