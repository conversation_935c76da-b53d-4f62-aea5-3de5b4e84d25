package com.hishop.wine.api;

import cn.binarywang.wx.miniapp.bean.WxMaRunStepInfo;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.minUser.*;
import com.hishop.wine.model.po.miniApp.MiniEncryptDataPO;
import com.hishop.wine.model.vo.basic.MiniUserDetailVO;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 小程序用户表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */

@FeignClient(name = "basic-system",
        contextId = "hishop-hishop-wine-miniUser",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/user")
public interface MiniUserFeign {

    /**
     * 根据userId获取小程序用户信息
     *
     * <AUTHOR>
     * @date 2023/6/29
     */
    @PostMapping({"/mini/getMiniUser"})
    ResponseBean<List<MiniUserVO>> getMiniUser(@RequestBody List<Long> userIdList);

    /**
     * 获取小程序用户信息
     *
     * @param miniUserId 小程序用户id
     * @return 小程序用户信息
     */
    @GetMapping({"/mini/getMiniUserById"})
    ResponseBean<MiniUserVO> getMiniUserById(@RequestParam("miniUserId") Long miniUserId);

    /**
     * 获取小程序用户信息
     * @param userId 用户id
     * @param moduleCode 模块code
     * @return 小程序用户信息
     */
    @GetMapping({"/mini/getMiniUserByUserIdAndModuleCode"})
    ResponseBean<MiniUserVO> getMiniUserByUserIdAndModuleCode(@RequestParam("userId") Long userId, @RequestParam("moduleCode") String moduleCode);

    /**
     * 获取微信步数
     * <AUTHOR>
     * @date 2023/7/25
     */
    @GetMapping({"/mini/getRunStep"})
    ResponseBean<List<WxMaRunStepInfo>> getRunStep(@RequestBody MiniEncryptDataPO encryptDataPo);

    /**
     * 分页查询
     * @param miniUserQueryPo 入参
     * @return
     */
    @PostMapping({"/mini/pageList"})
    ResponseBean<PageResult<MiniUserVO>> pageList(@RequestBody MiniUserQueryPo miniUserQueryPo);
    /**
     * 用户查询详情
     * @param id 入参
     * @return
     */
    @GetMapping({"/mini/getUserDetail"})
    ResponseBean<MiniUserDetailVO> getUserDetail(@RequestParam("id") Long id);

    /**
     * 备注名
     * @param miniUserRemarkNamePo 入参
     * @return
     */
    @PostMapping({"/mini/remarkName"})
    ResponseBean<Void> remarkName(@RequestBody MiniUserRemarkNamePo miniUserRemarkNamePo);

    /**
     * 【PC-批量处理黑名单】
     * @param miniUserBlacklistPo 入参
     * @return
     */
    @PostMapping({"/mini/blackList"})
    ResponseBean<Void> blackList(@RequestBody MiniUserBlacklistPo miniUserBlacklistPo);

    /**
     * 【PC-批量打标】
     * @param miniUserTagsPo 入参
     * @return
     */
    @PostMapping({"/mini/tags"})
    ResponseBean<Void> tags(@RequestBody MiniUserTagsPo miniUserTagsPo);

    /**
     * 【PC-黑名单分页】
     * @param miniUserBlacklistQueryPo 入参
     * @return
     */
    @PostMapping({"/mini/blacklistPage"})
    ResponseBean<PageResult<MiniUserBlacklistVo>> blacklistPage(@RequestBody MiniUserBlacklistQueryPo miniUserBlacklistQueryPo);

    /**
     * 【PC-删除标签】
     * @param minUserDelTagPo 入参
     * @return
     */
    @PostMapping({"/mini/delTag"})
    ResponseBean<Void> delTag(@RequestBody MinUserDelTagPo minUserDelTagPo);
}