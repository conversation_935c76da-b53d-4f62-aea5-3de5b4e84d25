package com.hishop.wine.model.vo.transaction;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;


/**
 * 发起支付返回值
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionPayVO", description = "发起支付返回值")
public class TransactionPayVO {

    @ApiModelProperty("内部交易id")
    private Long transactionId;

    @ApiModelProperty("内部交易流水号")
    private String transactionNo;

    @ApiModelProperty("第三方支付返回值(直接返回给前端)")
    private Map<String, Object> thirdPayResult;

    public static TransactionPayVO of(Long transactionId, String transactionNo, Map<String, Object> thirdPayResult) {
        TransactionPayVO vo = new TransactionPayVO();
        vo.setTransactionId(transactionId);
        vo.setTransactionNo(transactionNo);
        vo.setThirdPayResult(thirdPayResult);
        return vo;
    }

}
