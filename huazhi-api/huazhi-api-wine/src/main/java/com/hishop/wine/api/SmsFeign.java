package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.sms.SendSmsPO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 短信 微服务接口
 *
 * @author: chenzw
 * @date: 2024-01-20
 */

@FeignClient(name = "basic-system", contextId = "hishop-wine-sms", url = "${feign.url.basic-system:basic-system}", path = "/wine/sms")
public interface SmsFeign {

    /**
     * 发送短信
     * @param sendSmsPO 发送短信参数
     */
    @PostMapping({"/send"})
    ResponseBean<Void> sendSms(@RequestBody @Valid SendSmsPO sendSmsPO);
}