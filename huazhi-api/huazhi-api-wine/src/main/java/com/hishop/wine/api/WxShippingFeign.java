package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.po.wechat.WechatSavePO;
import com.hishop.wine.model.po.wxShipping.request.GetOrderRequest;
import com.hishop.wine.model.po.wxShipping.request.UploadShippingRequest;
import com.hishop.wine.model.po.wxShipping.response.DeliveryListResponse;
import com.hishop.wine.model.vo.wechat.WechatCodeVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @date 2025/4/1
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-wxShipping",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/wxshipping")
public interface WxShippingFeign {

    @ApiOperation(value = "查询小程序是否已开通发货信息管理服务", httpMethod = "GET")
    @GetMapping("/isTradeManaged")
    ResponseBean<Boolean> isTradeManaged(@RequestParam("appId") String appId);

    @ApiOperation(value = "发货信息录入接口", httpMethod = "POST")
    @PostMapping("/uploadShipping")
    ResponseBean<Boolean> uploadShipping(@RequestBody @Validated UploadShippingRequest request);

    @ApiOperation(value = "查询订单发货状态", httpMethod = "POST")
    @PostMapping("/getOrder")
    ResponseBean<Boolean> getOrder(@RequestBody @Validated GetOrderRequest request);

    @ApiOperation(value = "获取运力id列表", httpMethod = "GET")
    @GetMapping("/getDeliveryList")
    ResponseBean<DeliveryListResponse> getDeliveryList(@RequestParam("appId") String appId);

    @ApiOperation(value = "根据appId进行发货信息录入接口", httpMethod = "POST")
    @PostMapping("/uploadShippingByAppId")
    ResponseBean<Boolean> uploadShippingByAppId(@RequestBody @Validated UploadShippingRequest request);
}
