package com.hishop.wine.model.po.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.wine.model.po.user.UserPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 积分清零入参
 *
 * <AUTHOR>
 * @date : 2023/9/7
 */
@Data
@ApiModel(value = "ClearExpirePointsPO", description = "积分清零入参")
public class ClearExpirePointsPO {

    @ApiModelProperty(value = "失效时间")
    @NotNull(message = "失效时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadlineTime;

    @ApiModelProperty(value = "需要清理的用户, 如果为空则清理所有用户")
    private List<UserPO> userList;
}
