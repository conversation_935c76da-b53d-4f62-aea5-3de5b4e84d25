package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/02/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryList {

    @SerializedName("delivery_id")
    private String deliveryId;

    @SerializedName("delivery_name")
    private String deliveryName;
}
