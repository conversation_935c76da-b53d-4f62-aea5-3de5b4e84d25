package com.hishop.wine.model.po.transaction;

import java.util.Date;

import com.hishop.wine.enums.TransactionEnum;

import cn.hutool.core.date.DateTime;
import lombok.Data;

/**
 * 交易成功通知业务系统
 *
 * <AUTHOR>
 * @date : 2023/8/4
 */
@Data
public class TransactionSuccessNoticePO {

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 内部交易id
     */
    private Long transactionId;

    /**
     * 第三方交易流水
     */
    private String thirdTransactionNo;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 业务编码
     */
    private String bizCode;

    /**
     * 成交时间
     */
    private Date finishTime;

    /**
     * 交易是否成功
     */
    private Boolean success;

    /**
     * 交易状态 {@link com.hishop.wine.enums.TransactionEnum.Status}
     */
    private Integer status;

    /**
     * 构建交易成功通知
     *
     * @param moduleCode    模块编码
     * @param bizType       业务类型
     * @param bizCode       业务编码
     * @return {@link TransactionSuccessNoticePO}
     */
    public static TransactionSuccessNoticePO buildFeeZeroTransactionSuccessNotice(String moduleCode, String bizType, String bizCode) {
        TransactionSuccessNoticePO transactionSuccessNoticePO = new TransactionSuccessNoticePO();
        transactionSuccessNoticePO.setModuleCode(moduleCode);
        transactionSuccessNoticePO.setBizType(bizType);
        transactionSuccessNoticePO.setBizCode(bizCode);
        transactionSuccessNoticePO.setFinishTime(DateTime.now());
        transactionSuccessNoticePO.setSuccess(Boolean.TRUE);
        transactionSuccessNoticePO.setStatus(TransactionEnum.Status.SUCCESS.getStatus());
        return transactionSuccessNoticePO;
    }
}
