package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 设置消息跳转路径
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SetMsgJumpPathRequest extends BaseRequest {
    @SerializedName("path")
    private String path; // 自定义跳转路径（需包含transaction_id等参数）
}
