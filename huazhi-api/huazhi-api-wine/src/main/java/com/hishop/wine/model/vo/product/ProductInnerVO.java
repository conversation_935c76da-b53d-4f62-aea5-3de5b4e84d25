package com.hishop.wine.model.vo.product;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 产品详情 返回对象（用于内部数据同步）
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductInnerVO", description = "产品详情")
public class ProductInnerVO {

    /**
     * 主键id
     */
    private Long id;

    /**
     * 产品类型 1-商品 2-礼品
     */
    private Integer productType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品首图
     */
    private String productImg;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品单位
     */
    private String productUnit;

    /**
     * 产品分类id
     */
    private Long productCategoryId;

    /**
     * 市场价格
     */
    private BigDecimal marketPrice;

    /**
     * 状态 0：禁用  1：正常 (暂未使用)
     */
    private Boolean status;

    /**
     * 逻辑删除 0-未删除 其余为已删除
     */
    private Long izDelete;

    /**
     * 创建者ID
     */
    private Long createBy;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新者ID
     */
    private Long updateBy;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "产品图片列表")
    private List<String> productImgList;

    @ApiModelProperty(value = "产品视频列表")
    private List<String> mainVideoList;

    @ApiModelProperty(value = "产品详情图列表")
    private List<String> productDetailImgList;

}
