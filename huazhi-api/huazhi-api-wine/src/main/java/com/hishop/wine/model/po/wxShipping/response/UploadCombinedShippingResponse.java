package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 合单发货响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadCombinedShippingResponse {
    @SerializedName("errcode")
    private int errCode;

    @SerializedName("errmsg")
    private String errMsg;
}
