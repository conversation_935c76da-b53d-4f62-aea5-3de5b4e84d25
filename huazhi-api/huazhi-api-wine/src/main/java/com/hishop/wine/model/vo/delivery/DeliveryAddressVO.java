package com.hishop.wine.model.vo.delivery;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 收货地址表 返回对象
 * @author: HuBiao
 * @date: 2023-06-29
 */

@Data
@ApiModel(value = "DeliveryAddressVO", description = "收货地址表返回对象")
public class DeliveryAddressVO {

    @ApiModelProperty("收货地址id")
    private Long id;

    @ApiModelProperty(value = "收货人")
    private String consignee;

    @ApiModelProperty(value = "收货人手机号")
    private String consigneePhone;

    @ApiModelProperty(value = "省ID")
    private Integer provinceId;

    @ApiModelProperty("省份名称")
    private String province;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty("城市名称")
    private String city;

    @ApiModelProperty(value = "区ID")
    private Integer areaId;

    @ApiModelProperty("区名称")
    private String area;

    @ApiModelProperty("街道id")
    private Integer streetId;

    @ApiModelProperty("街道名称")
    private String street;

    @ApiModelProperty(value = "详细省市区")
    private String district;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "是否是默认地址 true-是 false 不是")
    private Boolean izDefault;

    public String getDistrict() {
        return StrUtil.emptyIfNull(province) + StrUtil.emptyIfNull(city) + StrUtil.emptyIfNull(area) + StrUtil.emptyIfNull(street);
    }

    public DeliveryAddressSimpleVO simpleBuilder() {
        return BeanUtil.copyProperties(this, DeliveryAddressSimpleVO.class);
    }
}
