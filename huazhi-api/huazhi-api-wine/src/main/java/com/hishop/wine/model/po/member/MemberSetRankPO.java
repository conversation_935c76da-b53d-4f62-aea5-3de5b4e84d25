package com.hishop.wine.model.po.member;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
/**
 * 会员设置头衔入参
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "MemberSetRankPO", description = "会员设置头衔入参")
public class MemberSetRankPO {

    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty(value = "会员头衔id (如果不传会移除头衔)")
    private Long rankId;

}
