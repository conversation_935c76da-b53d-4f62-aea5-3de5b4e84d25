package com.hishop.wine.model.po.product;

import com.hishop.common.pojo.IdBatchPO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.math.BigDecimal;

/**
 * 修改价格参数
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Data
@ApiModel(value = "ProductUpdatePricePO", description = "产品修改价格参数")
public class ProductUpdatePricePO extends IdBatchPO<Long> {

    @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

}
