package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小程序主页和底部导航下拉
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "HomePageAndStoreBarSelectVO", description = "小程序主页和底部导航下拉")
public class HomePageAndStoreBarSelectVO {

    @ApiModelProperty("模块编码")
    private String code;

    @ApiModelProperty("下拉显示的名称")
    private String name;

}
