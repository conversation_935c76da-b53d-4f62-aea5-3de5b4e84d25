package com.hishop.wine.model.po.micropage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@ApiModel(value = "MicropageUpdatePO", description = "微页面更新对象")
public class MicropageUpdatePO {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "主键不能为空")
    private Long id;

    @ApiModelProperty(value = "名称", required = true)
    @NotNull(message = "名称不能为空")
    private String name;

    @ApiModelProperty(value = "分组id")
    private Long categoryId;

    @ApiModelProperty(value = "发布状态 1-草稿 2-上架")
    @Min(value = 1, message = "发布状态不正确")
    @Max(value = 2, message = "发布状态不正确")
    private Integer status;

    @ApiModelProperty(value = "微页面配置json")
    @NotBlank(message = "微页面配置json不能为空")
    private String settingJson;

}
