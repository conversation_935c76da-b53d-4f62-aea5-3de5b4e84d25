package com.hishop.wine.model.vo.bill;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 收入账单统计出参
 * <AUTHOR>
 * @since 2024-01-30 15:17:19
 */
@Data
@ApiModel(value = "BillCountVo", description = "收入账单统计出参")
public class BillCountVo {

    @ApiModelProperty(value = "总收入(元)")
    private BigDecimal totalFee;

    @ApiModelProperty(value = "总收入(笔)")
    private Long totalNum;

    @ApiModelProperty(value = "时间区间开始")
    private String timeIntervalStart;

    @ApiModelProperty(value = "时间区间结束")
    private String timeIntervalEnd;

}
