package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:订单键值基础类
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderKey {
    @SerializedName("order_number_type")
    private int order_number_type; // 1-商户单号, 2-微信支付单号

    @SerializedName("transaction_id")
    private String transaction_id; // 微信支付单号（orderNumberType=2时必填）

    @SerializedName("mchid")
    private String mchid; // 商户号（orderNumberType=1时必填）

    @SerializedName("out_trade_no")
    private String out_trade_no; // 商户内部订单号（orderNumberType=1时必填）
}
