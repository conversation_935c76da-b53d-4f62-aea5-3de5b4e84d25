package com.hishop.wine.model.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信支付配置
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "PaymentSettingWxVO", description = "微信支付设置返回值")
public class PaymentSettingWxVO {

    @ApiModelProperty(value = "商户号")
    private String mchId;

    @ApiModelProperty(value = "apiV3支付密钥")
    private String apiV3Key;

    @ApiModelProperty(value = "证书id")
    private Long certificateId;

    @ApiModelProperty(value = "是否支持服务商模式")
    private Boolean isSupportSec;

    @ApiModelProperty(value = "服务商商户id")
    private String spMchId;

    @ApiModelProperty(value = "服务商小程序id")
    private String spAppId;

}
