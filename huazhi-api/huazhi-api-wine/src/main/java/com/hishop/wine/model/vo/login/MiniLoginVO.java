package com.hishop.wine.model.vo.login;

import com.hishop.common.pojo.login.LoginResult;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 小程序登录返回值
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "MiniLoginVO", description = "小程序登录信息")
public class MiniLoginVO extends LoginResult {

    @ApiModelProperty("用户信息")
    private MiniLoginUserVO userInfo;

    @ApiModelProperty("是否终端首次注册")
    private Boolean isTerminalFirstRegister;

    @ApiModelProperty("终端首次注册的欢迎词")
    private String terminalFirstRegisterStr;

    @ApiModelProperty("邀请人")
    private Long inviterUserId;

    @ApiModelProperty("是否首次登录")
    private Boolean isFirstLogin;
}
