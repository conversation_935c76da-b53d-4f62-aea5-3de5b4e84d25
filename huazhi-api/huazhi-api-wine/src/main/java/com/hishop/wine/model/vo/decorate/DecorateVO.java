package com.hishop.wine.model.vo.decorate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 商城装修表 返回对象
 * @author: LiGuoQiang
 * @date: 2023-06-26
 */

@Data
@ApiModel(value = "MallDecorateVO", description = "商城装修表返回对象")
public class DecorateVO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "小程序appId")
    private String appId;

    @ApiModelProperty(value = "系统类型(扫码营销：scan_marketing,粉丝俱乐部：fans_club,封坛酒：fengtan_wine)")
    private String moduleCode;

    @ApiModelProperty(value = "装修类型(积分商城首页装修：homePage，主题配色：theme，个人中心：userCenter，底部导航：navigation)")
	private String decorateType;

    @ApiModelProperty(value = "装修设置json串")
	private String settingJson;

    @ApiModelProperty(value = "是否小程序默认配置")
    private Boolean izDefault;


}
