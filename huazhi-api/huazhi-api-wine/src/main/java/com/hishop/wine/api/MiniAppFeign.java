package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.miniApp.uploadShippingInfo.UploadShippingInfoPo;
import com.hishop.wine.model.vo.miniApp.MinAppTokenVo;
import com.hishop.wine.model.vo.payment.PaymentSettingOfflineVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 模块表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */

@FeignClient(name = "basic-system", contextId = "hishop-wine-miniApp", url = "${feign.url.basic-system:basic-system}", path = "/wine/miniApp")
public interface MiniAppFeign {

    /**
     * 获取线下支付方式列表
     *
     * @return 线下支付方式列表
     */
    @GetMapping("/listForOfflineSelect")
    ResponseBean<List<PaymentSettingOfflineVO>> listForOfflineSelect();

    @GetMapping("/getAccessToken")
    ResponseBean<MinAppTokenVo> getAccessToken(@RequestParam String appId);

    @PostMapping("/uploadShippingInfo")
    ResponseBean<Void> uploadShippingInfo(@RequestBody @Valid UploadShippingInfoPo uploadShippingInfoPo);

    @GetMapping("/listForOfflineSelectBySpecialCode")
    ResponseBean<List<PaymentSettingOfflineVO>> listForOfflineSelectBySpecialCode(@RequestParam String specialCode);

    /**
     * 获取线下支付方式列表
     *
     * @return 线下支付方式列表
     */
    @GetMapping("/listForOfflineSelectAll")
    ResponseBean<List<PaymentSettingOfflineVO>> listForOfflineSelectAll();

}