package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码参数
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "MobileSendPO", description = "发送短信验证码参数")
public class MobileSendPO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "请输入手机号")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty("身份类型 1-消费者 2-管理员")
    private Integer identityType;

}
