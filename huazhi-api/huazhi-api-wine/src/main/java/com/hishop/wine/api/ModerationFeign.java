package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.moderation.model.*;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;

/**
 * 内容审核服务，基础系统提供，配置也在基础系统
 * <AUTHOR>
 * @date 2023/8/3
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-moderation",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/moderation")
public interface ModerationFeign {

    /**
     * 发起审核
     *
     * @param contentModerationCreatePO 发起审核参数
     * @return 审核id
     */
    @ApiOperation(value = "发起审核", httpMethod = "POST")
    @PostMapping("/check")
    ResponseBean<Long> check(@Valid @RequestBody ContentModerationCreatePO contentModerationCreatePO);

    /**
     * 文本内容审核
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/text")
    ResponseBean<ModerationResult> checkText(@RequestBody TextData textData);

    /**
     * 图像内容审核
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/image")
    ResponseBean<ModerationResult> checkImage(@RequestBody ImageData imageData);

    /**
     * 视频内容审核
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/video")
    ResponseBean<ModerationResult> checkVideo(@RequestBody VideoData videoData);

    /**
     * 音频内容审核
     * <AUTHOR>
     * @date 2023/8/3
     */
    @PostMapping("/audio")
    ResponseBean<ModerationResult> checkAudio(@RequestBody AudioData audioData);

}
