package com.hishop.wine.enums;

/**
 * 装修类型枚举 与数据库中的hishop_decorate表保持一致
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
public enum DecorateTypeEnums {

    /**
     * 底部导航
     */
    NAVIGATION("navigation"),
    /**
     * 首页装修
     */
    HOME_PAGE("homePage"),
    /**
     * 主题色
     */
    THEME("theme"),
    /**
     * 个人中心配置
     */
    USER_CENTER("userCenter"),
    /**
     * 积分池装修
     */
    POINTS_POOL("pointsPool");

    private final String type;

    DecorateTypeEnums(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

}
