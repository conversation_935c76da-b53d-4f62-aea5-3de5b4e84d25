package com.hishop.wine.model.vo.wechat;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/26
 */
@Data
@ApiModel(value = "微信二维码对象")
public class WechatCodeVO {


    /**
     * 主键id
     */
    private Long id;

    /**
     * 二维码类型。1：小程序码；2：公众号二维码
     */
    private Integer codeType;

    /**
     * 二维码来源。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部
     */
    private String codeFrom;

    /**
     * 二维码唯一标识，通过该字段匹配获取
     */
    private String codeKey;

    /**
     * 二维码描述
     */
    private String codeDesc;

    /**
     * 二维码地址。云服务器地址，存相对路径
     */
    private String codeUrl;

    /**
     * 是否删除
     */
    private Boolean izDelete;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人ID
     */
    private Long createBy;

    /**
     * 修改时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Long updateBy;

}
