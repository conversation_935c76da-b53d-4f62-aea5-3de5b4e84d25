package com.hishop.wine.model.vo.member;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hishop.common.util.serializer.ObjectToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 会员信息
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "MemberVO", description = "会员vo")
public class MemberVO {

    @ApiModelProperty("会员id")
    @ExcelIgnore
    private Long id;

    @ApiModelProperty("头像")
    @ExcelIgnore
    private String icon;

    @ApiModelProperty("昵称")
    @ExcelProperty("昵称")
    private String nickName;

    @ApiModelProperty("手机号")
    @ExcelProperty("手机号")
    private String mobile;

    @ApiModelProperty("头衔")
    @ExcelProperty("头衔")
    private String rankName;

    @ApiModelProperty("注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @ExcelProperty("注册时间")
    private Date registerTime;

    @ApiModelProperty("头衔id")
    @JsonSerialize(using = ObjectToStringSerializer.class)
    @ExcelIgnore
    private Long rankId;

}
