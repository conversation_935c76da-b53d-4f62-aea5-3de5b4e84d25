package com.hishop.wine.model.po.rank;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**   
 * 头衔表 更新入参对象
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Data
@ApiModel(value = "RankUpdatePO", description = "头衔表更新入参对象")
public class RankUpdatePO {

    @ApiModelProperty(value = "主键id", required = true)
    @NotNull(message = "主键id不能为空")
    private Long id;

    @ApiModelProperty(value = "头衔名称", required = true)
    @NotBlank(message = "头衔名称不能为空")
    private String rankName;

}
