package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.decorate.DecorateQueryPO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * 装修 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-basic-decorate", url = "${feign.url.basic-system:basic-system}", path = "/wine/decorate")
public interface DecorateFeign {

    /**
     * 查询装修详情
     *
     * @param queryPO
     * @return
     */
    @PostMapping("/pc/detail")
    ResponseBean<DecorateVO> detail(@Validated @RequestBody DecorateQueryPO queryPO);
}