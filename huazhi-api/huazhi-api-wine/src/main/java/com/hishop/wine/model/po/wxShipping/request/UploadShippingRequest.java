package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.OrderKey;
import com.hishop.wine.model.po.wxShipping.Payer;
import com.hishop.wine.model.po.wxShipping.ShippingItem;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:统一发货请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadShippingRequest extends BaseRequest {

    @SerializedName("order_key")
    private OrderKey order_key;

    @SerializedName("delivery_mode")
    private int delivery_mode; // 1-统一发货, 2-分拆发货

    @SerializedName("logistics_type")
    private int logistics_type; // 物流类型（1-快递, 2-同城, 3-虚拟, 4-自提）

    @SerializedName("shipping_list")
    private List<ShippingItem> shipping_list; // 物流列表

    @SerializedName("is_all_delivered")
    private Boolean is_all_delivered; // 分拆发货必填

    @SerializedName("upload_time")
    private String upload_time; // 上传时间戳（RFC3339格式）

    @SerializedName("payer")
    private Payer payer; // 支付者信息
}
