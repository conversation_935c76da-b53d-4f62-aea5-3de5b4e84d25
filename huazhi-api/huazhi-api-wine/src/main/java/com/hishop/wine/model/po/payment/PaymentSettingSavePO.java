package com.hishop.wine.model.po.payment;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 保存支付设置入参
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
@Data
@ApiModel(value = "PaymentSettingSavePO", description = "保存支付设置入参")
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PaymentSettingSavePO {

    @ApiModelProperty("支付设置id, 编辑时必填")
    private Long id;

    @ApiModelProperty(value = "支付类型 WX_PAY-微信支付 OFFLINE-线下支付", required = true)
    @NotBlank(message = "支付类型不能为空")
    private String paymentType;

    @ApiModelProperty(value = "支付方式名称", required = true)
    @NotBlank(message = "支付方式名称不能为空")
    @Size(max = 20, message = "支付方式名称长度不能超过20")
    private String paymentName;

    @ApiModelProperty("场景值")
    private String sceneValue;

    @ApiModelProperty("微信支付参数 当支付类型为WX_PAY时必填")
    private PaymentSettingWxPO wxPay;

    @ApiModelProperty(value = "线下支付渠道 WECHAT_TRANSFER-微信转账 ALI_TRANSFER-支付宝转账 BANK_CARD_TRANSFER-银行卡转账 当支付类型为OFFLINE时必填")
    private String offlineChannel;

    @ApiModelProperty("线下支付参数 当支付类型为OFFLINE时必填")
    private PaymentSettingOfflinePO offlinePay;

    @ApiModelProperty("是否特殊收款公司")
    private Boolean izSpecial;

    @ApiModelProperty("收款公司编码")
    private String specialCode;
}
