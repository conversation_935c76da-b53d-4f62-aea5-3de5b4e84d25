package com.hishop.wine.model.po.task;

import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/8/3
 */
@Data
public class FansClubTaskPO {

    /**
     * 用户Id
     */
    private Long userId;

    /**
     * 子任务类型
     */
    private String subTaskType;

    public static FansClubTaskPO of(Long userId, String subTaskType) {
        FansClubTaskPO fansClubTaskPO = new FansClubTaskPO();
        fansClubTaskPO.setUserId(userId);
        fansClubTaskPO.setSubTaskType(subTaskType);
        return fansClubTaskPO;
    }
}
