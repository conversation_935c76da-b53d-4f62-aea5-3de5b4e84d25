package com.hishop.wine.model.vo.sms;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**   
 * 短信发送记录表 返回对象
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Data
@ApiModel(value = "SmsRecordVO", description = "短信发送记录表返回对象")
public class SmsRecordVO {

    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "短信类型 0-通知短信 1-营销短信 2-验证码短信")
	private Integer channel;

    @ApiModelProperty(value = "短信内容")
	private String content;

    @ApiModelProperty("手机号")
    @Desensitized(type = SensitiveTypeEnum.MOBILE)
    private String mobiles;

    @ApiModelProperty(value = "发送时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
	private Date sendTime;

    @ApiModelProperty(value = "发送状态 0-未发送 1-发送成功 2-发送失败")
    private Integer status;

    @ApiModelProperty("发送状态名称")
    private String statusName;

}
