package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BaseRequest {

    @SerializedName("access_token")
    private String accessToken;

    @SerializedName("appId")
    private String appId;

    @SerializedName("userId")
    private Long userId;

}
