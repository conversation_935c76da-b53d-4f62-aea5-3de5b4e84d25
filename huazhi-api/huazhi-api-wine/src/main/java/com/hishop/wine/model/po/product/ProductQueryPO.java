package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hishop.common.pojo.page.PageParam;

import java.util.List;

/**
 * 产品表 查询入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductQueryPO", description = "产品表查询入参对象")
public class ProductQueryPO extends PageParam {

    @ApiModelProperty(value = "筛选值")
    private String searchValue;

    @ApiModelProperty(value = "产品类型 1-商品 2-礼品")
    private Integer productType;

    @ApiModelProperty(value = "产品分类id")
    private Long productCategoryId;

    @ApiModelProperty(value = "产品ID列表")
    private List<Long> idList;

    @ApiModelProperty(value = "产品编码列表")
    private List<String> productCodeList;

    @ApiModelProperty(value = "是否过滤被删除了的数据 默认过滤被删除的")
    private Boolean filterDelete;

}
