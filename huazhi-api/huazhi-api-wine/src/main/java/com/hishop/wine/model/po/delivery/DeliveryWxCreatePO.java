package com.hishop.wine.model.po.delivery;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.checkerframework.checker.units.qual.A;

import javax.validation.constraints.NotBlank;

/**
 * 保存微信收货地址
 *
 * <AUTHOR>
 * @date : 2023/7/3
 */
@Data
@ApiModel(value = "DeliveryAddressCreatePO", description = "收货地址表新增入参对象")
public class DeliveryWxCreatePO {

    @NotBlank(message = "请填写收货人名称")
    @ApiModelProperty(value = "收货人名称", required = true)
    private String userName;

    @NotBlank(message = "省份不能为空")
    @ApiModelProperty(value = "省份", required = true)
    private String provinceName;

    @NotBlank(message = "城市不能为空")
    @ApiModelProperty(value = "城市", required = true)
    private String cityName;

    @NotBlank(message = "区县不能为空")
    @ApiModelProperty(value = "区县", required = true)
    private String countyName;

    @ApiModelProperty(value = "街道")
    private String streetName;

    @NotBlank(message = "详细地址不能为空")
    @ApiModelProperty(value = "详细地址", required = true)
    private String detailInfoNew;

    @NotBlank(message = "收货人手机号不能为空")
    @ApiModelProperty(value = "收货人手机号", required = true)
    private String telNumber;

}
