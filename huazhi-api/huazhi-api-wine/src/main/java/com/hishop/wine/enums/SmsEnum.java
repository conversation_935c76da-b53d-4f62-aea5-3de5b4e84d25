package com.hishop.wine.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

import java.util.Arrays;

/**
 * 短信枚举
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
public class SmsEnum {

    public enum Channel {

        /**
         * 通知短信
         */
        NOTICE(0),

        /**
         * 营销短信
         */
        MARKETING(1),

        /**
         * 验证码短信
         */
        VERIFICATION(2);

        @Getter
        private Integer value;

        Channel(Integer value) {
            this.value = value;
        }
    }

    public enum Status {

        /**
         * 未发送
         */
        WAITING(0, "未发送"),

        /**
         * 发送成功
         */
        SUCCESS(1, "发送成功"),

        /**
         * 发送事变
         */
        FAIL(2, "发送失败");

        @Getter
        private Integer status;

        @Getter
        private String name;

        Status(Integer status, String name) {
            this.status = status;
            this.name = name;
        }

        public static String getName(Integer status) {
            return Arrays.asList(SmsEnum.Status.values()).stream()
                    .filter(item -> item.getStatus().equals(status)).findFirst().map(Status::getName).orElse(StrUtil.EMPTY);
        }
    }
}
