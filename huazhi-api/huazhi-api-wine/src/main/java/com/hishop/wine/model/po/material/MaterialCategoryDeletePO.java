package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.Data;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 素材库分组 删除入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCategoryDeletePO", description = "素材库分组删除入参对象")
public class MaterialCategoryDeletePO {

    @ApiModelProperty(value = "资源分组id", required = true)
    @NotNull(message = "资源分组id不能为空")
    private Long topId;

    @ApiModelProperty(value = "是否删除分组下所有文件", required = true)
    @NotNull(message = "是否删除分组下所有文件不能为空")
    private Boolean deleteFiles;
}
