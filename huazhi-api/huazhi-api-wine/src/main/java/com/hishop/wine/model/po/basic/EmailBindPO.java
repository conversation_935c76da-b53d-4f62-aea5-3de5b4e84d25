package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 绑定邮箱参数
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "EmailBindPO", description = "绑定邮箱参数")
public class EmailBindPO {

    @ApiModelProperty(value = "邮件", required = true)
    @NotBlank(message = "请输入邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

    @ApiModelProperty(value = "验证码", required = true)
    @NotBlank(message = "请输入验证码")
    private String code;

    @ApiModelProperty("登录密码, 修改邮箱时必填")
    private String password;

}
