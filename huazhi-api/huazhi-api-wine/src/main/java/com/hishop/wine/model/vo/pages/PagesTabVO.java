package com.hishop.wine.model.vo.pages;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "PagesTabVO", description = "页面tab配置表返回对象")
public class PagesTabVO {

    @ApiModelProperty("tab类型 1-功能页面 2-活动 3-自定义")
    private Integer type;

    @ApiModelProperty("tab名称")
    private String name;

    @ApiModelProperty("页面集合")
    private List<PagesVO> pages;

    public static PagesTabVO of(Integer type, String name, List<PagesVO> pages) {
        PagesTabVO pagesTabVO = new PagesTabVO();
        pagesTabVO.setType(type);
        pagesTabVO.setName(name);
        pagesTabVO.setPages(pages);
        return pagesTabVO;
    }

}
