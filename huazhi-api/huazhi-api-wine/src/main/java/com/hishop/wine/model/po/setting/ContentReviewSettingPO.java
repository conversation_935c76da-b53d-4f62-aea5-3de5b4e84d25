package com.hishop.wine.model.po.setting;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date : 2023/8/2
 */
@Data
@ApiModel(value = "ContentReviewSettingPO", description = "内容审核配置")
public class ContentReviewSettingPO {

    @ApiModelProperty(value = "是否启用内容审核 true:启用 false:不启用", required = true)
    @NotNull(message = "请选择是否启用内容审核")
    private Boolean enable;

    @ApiModelProperty(value = "appKey")
    private String appKey;

    @ApiModelProperty(value = "appSecret")
    private String appSecret;

    public void check() {
        if (enable) {
            Assert.isTrue(StrUtil.isNotEmpty(appKey) && StrUtil.isNotEmpty(appSecret), "appKey或appSecret不能为空");
        }
    }

}
