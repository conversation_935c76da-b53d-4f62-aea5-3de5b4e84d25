package com.hishop.wine.model.dto;

import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/8/18
 */
@Data
public class ModuleDrawWinRecordDTO {

    /**
     * 模块编码，用于区分不同的业务模块
     */
    private String moduleCode;
    /**
     * 主键id，子模块通过雪花算法生成
     */
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 用户身份类型。理论上只有消费者能参与抽奖，即该字段是默认值，不排除以后其他身份能抽奖
     */
    private Integer identityType;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 用户参与抽奖的活动ID。对应子系统的活动ID
     */
    private Long raffleId;

    /**
     * 抽奖活动名称，子系统活动名称
     */
    private String raffleName;

    /**
     * 创建时间=用户抽奖时间
     */
    private Date createTime;

    /**
     * 抽奖结果。0：未中奖；1：中奖
     */
    private Integer drawResult;

    /**
     * 奖品类型。0：未中奖；1：积分；2：礼品
     */
    private Integer prizeType;

    /**
     * 奖励数量。prize_type=1时代表中奖时奖励多少积分；prize_type=2时默认数量为1
     */
    private Integer awardNum;

    /**
     * 奖品ID
     */
    private Long prizeId;
    /**
     * 奖品名称，冗余回溯
     */
    private String prizeName;
    /**
     * 领取状态。0：未领取；1：已领取。积分默认已领取，礼品需要领取生成礼品订单才算
     */
    private Integer receiveStatus;

    /**
     * 礼品ID，中奖了并且奖品是礼品时值有意义
     */
    private Long productId;


    /**
     * 礼品编码，冗余
     */
    private String productCode;

    /**
     * 礼品名称，冗余
     */
    private String productName;

    /**
     * 参与奖ID，根据业务开启了参与奖时值有意义
     */
    private Long participateId;

    /**
     * 是否仅未中奖
     */
    private Boolean onlyNoPrize;

    /**
     * 参与奖类型。1：积分；2：礼品
     */
    private Integer giveType;

    /**
     * 奖励数量。give_type=1时代表中奖时奖励多少积分；prize_type=2时对应礼品ID
     */
    private Integer giveValue;

    /**
     * 发送时间
     */
    private Date sendTime;

}
