package com.hishop.wine.model.vo.pages;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 页面配置表 返回对象
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Data
@ApiModel(value = "PagesVO", description = "页面配置表返回对象")
public class PagesVO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;
    
    @ApiModelProperty(value = "名称")
	private String name;
    
    @ApiModelProperty(value = "页面地址")
	private String path;

}
