package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 检测权限po
 *
 * <AUTHOR>
 * @date : 2023/8/11
 */
@Data
@ApiModel(value = "CheckRolePO", description = "检测权限po")
public class CheckRolePO {

    @ApiModelProperty(value = "用户id", required = true)
    @NotNull(message = "用户id不能为空")
    private Long userId;

    @ApiModelProperty("身份id")
    private Long identityId;

    @ApiModelProperty(value = "角色id", required = true)
    @NotNull(message = "角色id不能为空")
    private Long roleId;

}
