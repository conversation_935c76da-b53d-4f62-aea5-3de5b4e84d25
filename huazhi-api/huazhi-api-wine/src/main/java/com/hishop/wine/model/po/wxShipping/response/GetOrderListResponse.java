package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单列表响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderListResponse {
    @SerializedName("errcode")
    private int errCode;

    @SerializedName("errmsg")
    private String errMsg;

    @SerializedName("order_list")
    private List<OrderInfo> orderList;

    @SerializedName("has_more")
    private boolean hasMore;

    @SerializedName("last_index")
    private String lastIndex;
}