package com.hishop.wine.enums.points;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
public class MemberPointsEnum {

    // 变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分/积分发放
    public enum ModifiedType {

        INCREASE(1, "积分发放"),
        DECREASE(-1, "积分消耗"),
        CLEAR(0, "积分清零"),
        ;

        private final Integer code;
        private final String desc;

        ModifiedType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static ModifiedType valueOf(Integer code) {
            return Arrays.stream(values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .orElse(null);
        }

        public static String getDesc(Integer code) {
            return Arrays.stream(values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .map(ModifiedType::getDesc)
                    .orElse("");
        }

        public static boolean isLegal(Integer code) {
            return Arrays.stream(values())
                    .anyMatch(e -> e.getCode().equals(code));
        }
    }

}
