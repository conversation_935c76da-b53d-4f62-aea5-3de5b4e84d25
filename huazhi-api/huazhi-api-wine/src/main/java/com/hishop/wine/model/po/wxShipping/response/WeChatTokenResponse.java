package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WeChatTokenResponse {
    @SerializedName("access_token")
    private String accessToken;

    @SerializedName("expires_in")
    private int expiresIn;

    @SerializedName("errcode")
    private int errcode;

    @SerializedName("errmsg")
    private String errmsg;
}
