package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description: 请求参数模型
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradeManagementCheckRequest extends BaseRequest {
    @SerializedName("appid")
    private String appId; // 待查询小程序的AppID
}
