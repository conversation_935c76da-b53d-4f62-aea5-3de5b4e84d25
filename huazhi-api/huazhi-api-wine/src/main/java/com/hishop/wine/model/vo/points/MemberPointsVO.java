package com.hishop.wine.model.vo.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**   
 * 会员积分表 返回对象
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Data
@ApiModel(value = "MemberPointsVO", description = "会员积分表返回对象")
public class MemberPointsVO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    @ApiModelProperty(value = "用户ID")
	private Long userId;
    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端
     */
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private Integer identityType;
    @ApiModelProperty(value = "身份类型 1-管理员 2-消费者 3-经销商 4-终端")
    private String identityTypeDesc;
    @ApiModelProperty(value = "用户昵称")
    private String nickName;
    @ApiModelProperty(value = "用户手机号码")
	private String userPhone;
    @ApiModelProperty("用户头像")
    private String icon;
    @ApiModelProperty(value = "用户注册时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;
    @ApiModelProperty(value = "累计总积分")
	private Integer totalPoints;
    @ApiModelProperty(value = "可用积分=当前剩余总积分")
	private Integer availablePoints;
    @ApiModelProperty(value = "已消耗积分=用户正常使用掉的")
	private Integer consumedPoints;
    @ApiModelProperty(value = "过期清零的积分")
	private Integer expiredPoints;
    @ApiModelProperty(value = "冻结积分", hidden = true)
    private Integer frozenPoints;
    

}
