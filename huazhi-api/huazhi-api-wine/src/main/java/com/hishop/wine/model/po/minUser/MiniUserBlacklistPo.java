package com.hishop.wine.model.po.minUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 小程序用户表黑名单入参
 * <AUTHOR>
 * @since 2024-01-26 17:06:07
 */
@Data
@ApiModel(value = "MiniUserBlacklistPo", description = "小程序用户表黑名单入参")
public class MiniUserBlacklistPo {

    @ApiModelProperty(value = "是否黑名单 false：否 true：是")
    @NotNull(message = "是否黑名单不能为空")
    private Boolean izBlacklist;

    @ApiModelProperty(value = "小程序用户id列表")
    @NotNull(message = "小程序用户id列表不能为空")
    @Size(min = 1, message = "请至少选择1个用户")
    private List<Long> idList;
}
