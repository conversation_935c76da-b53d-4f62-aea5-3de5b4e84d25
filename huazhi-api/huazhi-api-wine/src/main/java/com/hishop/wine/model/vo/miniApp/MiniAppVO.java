package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信小程序信息vo
 *
 * <AUTHOR>
 * @date : 2023/6/25
 */
@Data
@ApiModel(value = "MiniAppVO", description = "小程序信息")
public class MiniAppVO {

    @ApiModelProperty("主键")
    private Long id;

    @ApiModelProperty("appId")
    private String appId;

    @ApiModelProperty("appName")
    private String appName;

    @ApiModelProperty("模块名称")
    private String moduleNames;

    @ApiModelProperty("装修使用的系统模块编码")
    private String settingModuleCode;

    @ApiModelProperty("要打开的小程序版本。正式版为 \"release\"，体验版为 \"trial\"，开发版为 \"develop\"。默认是正式版。")
    private String envVersion;

}
