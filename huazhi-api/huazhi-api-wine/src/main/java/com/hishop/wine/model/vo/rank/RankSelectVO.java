package com.hishop.wine.model.vo.rank;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.hishop.common.util.serializer.ObjectToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
@ApiModel(value = "RankSelectVO", description = "头衔下拉")
public class RankSelectVO {

    @JsonSerialize(using = ObjectToStringSerializer.class)
    @ApiModelProperty("头衔id")
    private Long id;

    @ApiModelProperty(value = "头衔名称")
    private String rankName;

}
