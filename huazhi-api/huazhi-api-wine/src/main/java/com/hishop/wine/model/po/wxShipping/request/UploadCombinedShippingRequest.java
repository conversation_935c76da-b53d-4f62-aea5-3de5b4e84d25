package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.OrderKey;
import com.hishop.wine.model.po.wxShipping.SubOrder;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 合单发货请求参数
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadCombinedShippingRequest extends BaseRequest  {
    @SerializedName("order_key")
    private OrderKey orderKey;

    @SerializedName("sub_orders")
    private List<SubOrder> subOrders; // 子单列表
}
