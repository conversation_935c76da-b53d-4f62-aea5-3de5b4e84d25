package com.hishop.wine.enums;

import cn.hutool.core.util.StrUtil;
import com.hishop.wine.constants.RocketMqConstants;
import lombok.Getter;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2023/6/27
 */
public class UserEnum {

    public enum IdentityType {

        MANAGER(1, "管理员"),
        CONSUMER(2, "消费者"),
        MERCHANT(3, "经销商"),
        TERMINAL(4, "终端"),
        ;

        private final Integer code;
        private final String desc;

        IdentityType(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }

        public static String getDesc(Integer code) {
            return Arrays.stream(values())
                    .filter(v -> v.getCode().equals(code))
                    .findAny()
                    .map(IdentityType::getDesc)
                    .orElse("");
        }
    }

    /**
     * 注册渠道枚举
     */
    @Getter
    public enum RegisterChannel {

        /**
         * 粉丝俱乐部-会员任务
         */
        FANS_USER_TASK("会员任务", RocketMqConstants.FANS_USER_TASK_REGISTER_CALL_BACK),

        /**
         * 扫码营销-活动
         */
        SCAN_CODE_ACTIVITY("扫码活动", StrUtil.EMPTY);

        private final String desc;

        private final String callBackTopic;

        RegisterChannel(String desc, String callBackTopic) {
            this.desc = desc;
            this.callBackTopic = callBackTopic;
        }

        public String getDesc() {
            return desc;
        }

        public static String getCallBackTopic(String name) {
            return Arrays.stream(values())
                    .filter(v -> v.name().equals(name))
                    .findAny()
                    .map(RegisterChannel::getCallBackTopic)
                    .orElse(StrUtil.EMPTY);
        }
    }

    /**
     * 拉新合计维度 合计维度 1-按邀请人 2-按业务码 3-综合
     */
    @Getter
    public enum TotalDimension {

        /**
         * 按邀请人
         */
        INVITER_USER(1, "按邀请人"),

        /**
         * 按业务码
         */
        BIZ_CODE(2, "按业务码"),

        /**
         * 综合
         */
        COMPREHENSIVE(3, "综合");

        private final Integer code;

        private final String desc;

        TotalDimension(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static String getDesc(Integer code) {
            return Arrays.stream(values())
                    .filter(v -> v.getCode().equals(code))
                    .findAny()
                    .map(TotalDimension::getDesc)
                    .orElse("");
        }
    }

}
