package com.hishop.wine.enums.points;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 扫码营销涉及积分业务的业务类型
 * <AUTHOR>
 * @date 2023/8/17
 */
public enum ScanMarketingBizType implements BizType {

    SCAN(11, "扫码开奖", MemberPointsEnum.ModifiedType.INCREASE)
    ;

    private final Integer code;
    private final String desc;
    private final MemberPointsEnum.ModifiedType parentType;

    ScanMarketingBizType(Integer code, String desc, MemberPointsEnum.ModifiedType parentType) {
        this.code = code;
        this.desc = desc;
        this.parentType = parentType;
    }

    @Override
    public String moduleCode() {
        return "scan_marketing";
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public MemberPointsEnum.ModifiedType getParentType() {
        return parentType;
    }

    @Override
    public String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(BizType::getDesc)
                .orElse("");
    }

    public static boolean isLegal(Integer code) {
        return Arrays.stream(values())
                .anyMatch(e -> e.getCode().equals(code));
    }

    public static List<ScanMarketingBizType> getBizType(MemberPointsEnum.ModifiedType parentType) {
        return Optional.ofNullable(parentType)
                .map(type -> Arrays.stream(ScanMarketingBizType.values())
                        .filter(e -> e.getParentType().equals(type))
                        .collect(Collectors.toList()))
                .orElse(Arrays.asList(ScanMarketingBizType.values()));
    }

}
