package com.hishop.wine.model.vo.payment;

import com.hishop.wine.enums.OfflineChannel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 线下支付返回值
 *
 * <AUTHOR>
 * @date : 2024/01/08
 */
@Data
@ApiModel(value = "PaymentSettingOfflineVO", description = "线下支付返回值")
public class PaymentSettingOfflineVO {

    /**
     * 支付设置ID
     */
    @ApiModelProperty(value = "支付设置ID")
    private Long paymentId;

    /**
     * 线下支付方式
     */
    @ApiModelProperty(value = "线下支付方式")
    private OfflineChannel offlineChannel;

    /**
     * 收款人姓名
     */
    @ApiModelProperty(value = "收款人姓名")
    private String beneficiaryName;

    /**
     * 收款人账号
     */
    @ApiModelProperty(value = "收款人账号")
    private String beneficiaryAccount;

    /**
     * 开户银行名称
     */
    @ApiModelProperty(value = "开户银行名称")
    private String bankName;

    /**
     * 二维码信息
     */
    @ApiModelProperty(value = "二维码信息")
    private String qrCode;

    @ApiModelProperty(value = "是否特殊")
    private Boolean izSpecial;

    @ApiModelProperty(value = "收款公司编码")
    private String specialCode;

}
