package com.hishop.wine.model.po.points;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@Data
@ApiModel(value = "ChangePointsPO", description = "变更积分数据对象")
public class ChangePointsPO {

    @ApiModelProperty(value = "用户ID")
    private Long userId;
    @ApiModelProperty(value = "用户身份类型。积分根据身份类型隔离")
    private Integer identityType;
    @ApiModelProperty(value = "变更类型。-1：减积分/积分消耗；0：积分清零；1：加积分/积分发放。方便区分")
    private Integer modifiedType;
    @ApiModelProperty(value = "变更的积分。通过正负值表示增减，方便统计。积分清零也算减少，传负数")
    private Integer modifiedPoints;
    @ApiModelProperty(value = "模块编码。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部")
    private String moduleCode;
    @ApiModelProperty(value = "具体的业务类型，枚举与来源系统保持一致")
    private Integer bizType;
    @ApiModelProperty(value = "修改说明/原因")
    private String modifiedRemark;
    @ApiModelProperty(value = "关联的业务编码，方便回溯")
    private String bizCode;
    @ApiModelProperty(value = "跟踪号，比如消费消息的唯一编码")
    private String traceNo;
    @ApiModelProperty(value = "积分过期时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;
    @ApiModelProperty(value = "创建人。如果是手工变更的，记录变更人。0代表系统变更")
    private Long createBy;

}
