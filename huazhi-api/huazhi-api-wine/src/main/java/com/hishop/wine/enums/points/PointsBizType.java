package com.hishop.wine.enums.points;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.json.JSONUtil;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/8/17
 */
public class PointsBizType {

    private static final Map<String, BizType> BIZ_TYPE_MAP = new HashMap<>(8);

    static {
        List<Class<? extends Enum>> implementations = getEnumImplementations("com.hishop.wine.enums.points", BizType.class);
        implementations.forEach(clazz -> {
            try {
                Enum[] enumConstants = clazz.getEnumConstants();
                for (Enum enumConstant : enumConstants) {
                    BizType bizType = (BizType) enumConstant;
                    BIZ_TYPE_MAP.put(bizType.moduleCode(), bizType);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
    }

    public static BizType getBizType(String moduleCode) {
        return BIZ_TYPE_MAP.get(moduleCode);
    }

    public static List<Class<? extends Enum>> getEnumImplementations(String packageName, Class<?> interfaceClass) {
        List<Class<? extends Enum>> implementations = new ArrayList<>();

        Set<Class<?>> clzSet = ClassUtil.scanPackageBySuper("com.hishop.wine.enums.points", BizType.class);

        for (Class<?> clazz : clzSet) {
            if (Enum.class.isAssignableFrom(clazz) && interfaceClass.isAssignableFrom(clazz)) {
                implementations.add((Class<? extends Enum>) clazz);
            }
        }

        return implementations;
    }

    public static void main(String[] args) {
        System.out.println(getEnumImplementations("com.hishop.wine.enums.points", BizType.class));
    }

}
