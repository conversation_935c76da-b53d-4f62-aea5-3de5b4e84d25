package com.hishop.wine.model.po.setting;

import cn.hutool.core.lang.Assert;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @Date 2024/07/11/ $
 * @description:
 */
@Data
@ApiModel(value = "FeastSettingPo", description = "宴席酒配置参数")
public class FeastSettingPo {
    @ApiModelProperty(value = "appId", required = true)
    @NotBlank(message = "appId不能为空")
    private String appId;

    public void check() {
        Assert.isTrue(!appId.contains("*"), "appId不能包含*号");
    }

}
