package com.hishop.wine.model.po.minUser;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 小程序用户表(MiniUser)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-25 11:06:07
 */
@Data
@ApiModel(value = "MiniUserPo", description = "小程序用户表")
public class MiniUserPo {
    
    @ApiModelProperty(value = "小程序用户id")
    private Long id;
    
    @ApiModelProperty(value = "用户id")
    private Long userId;
    
    @ApiModelProperty(value = "昵称")
    private String nickName;
    
    @ApiModelProperty(value = "微信头像")
    private String avatarUrl;
    
    @ApiModelProperty(value = "小程序app_id")
    private String appId;
    
    @ApiModelProperty(value = "注册模块编码")
    private String registerModuleCode;
    
    @ApiModelProperty(value = "unionid")
    private String unionId;
    
    @ApiModelProperty(value = "openid")
    private String openId;
    
    @ApiModelProperty(value = "最后一次登录的sessionKey")
    private String sessionKey;
    
    @ApiModelProperty(value = "是否黑名单 0：否 1：是")
    private Boolean izBlacklist;
    
    @ApiModelProperty(value = "拉黑时间")
    private Date blacklistDate;
    
    @ApiModelProperty(value = "拉黑操作人")
    private Long blacklistOperateId;
    
    @ApiModelProperty(value = "拉黑操作人姓名")
    private String blacklistOperateName;
    
    @ApiModelProperty(value = "拉黑操作人电话")
    private String blacklistOperatePhone;

    @ApiModelProperty(value = "客户编号")
    private String userNo;
    
    
    
    
}
