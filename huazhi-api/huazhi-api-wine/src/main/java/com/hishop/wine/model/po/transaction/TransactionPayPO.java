package com.hishop.wine.model.po.transaction;

import java.math.BigDecimal;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

import com.hishop.wine.enums.TransactionEnum;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 发起支付入参
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Data
@ApiModel(value = "TransactionCreatePO", description = "发起支付入参")
public class TransactionPayPO {

    @ApiModelProperty(value = "交易方式 WX_PAY-微信支付")
    private TransactionEnum.MethodEnum transactionMethod = TransactionEnum.MethodEnum.WX_PAY;

    @ApiModelProperty("交易类型 APP-APP支付 JSAPI-小程序支付 NATIVE-Native支付 H5-H5支付")
    private TransactionEnum.TradeTypeEnum tradeType = TransactionEnum.TradeTypeEnum.JSAPI;

    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull(message = "请指定业务类型")
    private TransactionEnum.BizTypeEnum bizType;

    @ApiModelProperty(value = "业务端业务编码(传订单编码)", required = true)
    @NotBlank(message = "业务类型编码不能为空")
    private String bizCode;

    @ApiModelProperty(value = "交易金额", required = true)
    @NotNull(message = "交易金额不能为空")
    @Min(value = 0, message = "交易金额不能小于0")
    private BigDecimal amount;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "商品描述", required = true)
    @NotBlank(message = "商品描述不能为空")
    private String description;

    /**
     * 构建支付参数
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param userId 用户ID
     * @param amount 交易金额
     * @param description 商品描述
     * @return TransactionPayPO
     */
    public static TransactionPayPO buildTransactionPay(TransactionEnum.BizTypeEnum bizType, String bizCode, Long userId, BigDecimal amount, String description) {
        TransactionPayPO transactionPayPO = new TransactionPayPO();
        transactionPayPO.setBizType(bizType);
        transactionPayPO.setBizCode(bizCode);
        transactionPayPO.setUserId(userId);
        transactionPayPO.setAmount(amount);
        transactionPayPO.setDescription(description);
        return transactionPayPO;
    }

}
