package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.OrderInfo;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单响应
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderResponse {
    @SerializedName("errcode")
    private int errCode;

    @SerializedName("errmsg")
    private String errMsg;

    @SerializedName("order")
    private OrderInfo order;
}
