package com.hishop.wine.model.po.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 小程序创建vo
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "MiniAppCreatePO", description = "小程序创建入参")
public class MiniAppCreatePO {

    @ApiModelProperty(value = "小程序id", required = true)
    @NotBlank(message = "请输入appId")
    private String appId;

    @ApiModelProperty(value = "appSecret", required = true)
    @NotBlank(message = "请输入appSecret")
    private String appSecret;

    @ApiModelProperty("小程序原始id")
    private String originalId;

    @ApiModelProperty(value = "小程序名称", required = true)
    @NotBlank(message = "请输入小程序名称")
    private String appName;

    @ApiModelProperty(value = "模块编码的集合")
    private List<String> moduleCodes;

    @ApiModelProperty(value = "主页和底部导航", required = true)
    private String settingModuleCode;

    @ApiModelProperty(value = "支付设置id(目前是单选)")
    private List<Long> paymentIds;

    @ApiModelProperty(value = "线下支付设置id(目前是多选)")
    private List<Long> offlineIds;
}
