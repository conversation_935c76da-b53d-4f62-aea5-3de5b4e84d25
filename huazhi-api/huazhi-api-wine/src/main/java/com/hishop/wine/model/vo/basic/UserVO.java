package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**   
 * 用户表 返回对象
 * @author: chenpeng
 * @date: 2023-06-28
 */

@Data
@ApiModel(value = "UserVO", description = "用户表返回对象")
public class UserVO {
	
    @ApiModelProperty(value = "用户id")
    private Long id;

    @ApiModelProperty(value = "用户名")
    private String username;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "真实姓名")
    private String realName;

    @ApiModelProperty(value = "头像")
    private String icon;

    @ApiModelProperty(value = "邮箱")
    private String email;

    @ApiModelProperty("昵称")
    private String nickName;

    @ApiModelProperty("头衔id")
    private Long rankId;

    @ApiModelProperty("头衔状态 true-有效 false-无效")
    private Boolean rankStatus;

    @ApiModelProperty("注册时间 默认返回该手机号的首次注册时间")
    private Date createTime;


}
