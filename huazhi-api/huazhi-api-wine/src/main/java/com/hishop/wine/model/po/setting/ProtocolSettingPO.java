package com.hishop.wine.model.po.setting;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 协议配置
 *
 * <AUTHOR>
 * @date : 2023/8/23
 */
@Data
@ApiModel(value = "ProtocolSettingPO", description = "协议配置")
public class ProtocolSettingPO {

    @ApiModelProperty(value = "用户协议")
    @NotBlank(message = "用户协议不能为空")
    private String userAgreement;

    @ApiModelProperty(value = "隐私政策")
    @NotBlank(message = "隐私政策不能为空")
    private String privacyPolicy;

}
