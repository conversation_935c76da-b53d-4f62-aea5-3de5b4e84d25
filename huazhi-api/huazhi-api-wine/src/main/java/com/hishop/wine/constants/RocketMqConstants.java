package com.hishop.wine.constants;

/**
 * mq 常量
 *
 * <AUTHOR>
 * @date : 2023/6/29
 */
public interface RocketMqConstants {

    /**
     * 用户积分调整topic(事务消息)
     */
    String MEMBER_POINTS_CHANGE_TOPIC = "MEMBER_POINTS_CHANGE_TOPIC";

    /**
     * 检测积分订单扣减积分topic
     */
    String CHECK_POINTS_ORDER_TOPIC = "CHECK_POINTS_ORDER_TOPIC";

    /**
     * 同步配置setting
     */
    String SYNC_SETTING_TOPIC = "SYNC_SETTING_TOPIC";

    /**
     * 记录操作日志
     */
    String LOG_OPERATION_TOPIC = "LOG_OPERATION_TOPIC";

    /**
     * 注册成功topic(事务消息)
     */
    String REGISTER_SUCCESS_TOPIC = "REGISTER_SUCCESS_TOPIC";

    /**
     * 完成粉丝俱乐部任务topic
     */
    String COMPLETE_FANS_TASK_TOPIC = "COMPLETE_FANS_TASK_TOPIC";

    /**
     * 计算会员头衔
     */
    String CALCULATE_MEMBER_RANK = "CALCULATE_MEMBER_RANK";

    /**
     * 子模块之间中奖记录同步
     */
    String TOPIC_MODULE_DRAW_WIN_RECORD = "TOPIC_MODULE_DRAW_WIN_RECORD";

    /**
     * 子模块之间中奖记录回调(反向更新)
     */
    String TOPIC_MODULE_DRAW_WIN_RECORD_CALL_BACK = "TOPIC_MODULE_DRAW_WIN_RECORD_CALL_BACK";

    /**
     * 粉丝俱乐部会员任务注册成功回调
     */
    String FANS_USER_TASK_REGISTER_CALL_BACK = "FANS_USER_TASK_REGISTER_CALL_BACK";

    /**
     * 扫码营销收集用户定位
     */
    String SCAN_COLLECT_LOCATION_TOPIC = "SCAN_COLLECT_LOCATION_TOPIC";

    /**
     * 积分清零
     */
    String POINTS_CLEAR_TOPIC = "POINTS_CLEAR_TOPIC";

    /**
     * 积分清零通知
     */
    String POINTS_CLEAR_NOTICE_TOPIC = "POINTS_CLEAR_NOTICE_TOPIC";

    /**
     * 用户打标
     */
    String USER_MARK_TAG_TOPIC = "USER_MARK_TAG_TOPIC";

    /**
     * 发放红包
     */
    String GIVE_RED_PACKAGE_TOPIC = "GIVE_RED_PACKAGE_TOPIC";

    /**
     * 账单
     */
    String BILL_TOPIC = "BILL_TOPIC";

    /**
     * 扫码
     */
    String SCAN_CODE_TOPIC = "SCAN_CODE_TOPIC";
}
