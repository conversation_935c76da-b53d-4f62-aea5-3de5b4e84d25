package com.hishop.wine.api;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdStatusPo;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.rank.RankCreatePO;
import com.hishop.wine.model.po.rank.RankQueryPO;
import com.hishop.wine.model.po.rank.RankUpdatePO;
import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;
import com.hishop.wine.model.vo.rank.RankVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 头衔表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */

@FeignClient(name = "basic-system",
        contextId = "hishop-wine-basic-rank",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/rank")
public interface RankFeign {

    /**
     * 新增头衔
     *
     * @param createPO 新增头衔参数
     * @return 头衔id
     */
    @PostMapping({"/pc/create"})
    ResponseBean<Long> create(@Valid @RequestBody RankCreatePO createPO);

    /**
     * 编辑头衔
     *
     * @param updatePO 编辑头衔参数
     * @return 编辑结果
     */
    @PostMapping({"/pc/update"})
    ResponseBean<Void> update(@Valid @RequestBody RankUpdatePO updatePO);

    /**
     * 删除头衔
     *
     * @param idPO 头衔id的集合
     * @return 修改结果
     */
    @PostMapping({"/pc/delete"})
    ResponseBean<Void> delete(@RequestBody IdBatchPO<Long> idPO);

    /**
     * 修改头衔状态
     *
     * @param idPO 头衔id和状态
     * @return 修改结果
     */
    @PostMapping("/pc/status")
    ResponseBean<Void> changeStatus(@RequestBody IdStatusPo<Long> idPO);

    /**
     * 获取头衔详情
     *
     * @param id 头衔id
     * @return 头衔详情
     */
    @GetMapping("/pc/detail")
    ResponseBean<RankVO> detail(@RequestParam(name = "id") Long id);

    /**
     * 查询头衔列表(不分页)
     *
     * @param qryPO 筛选参数
     * @return 头衔列表
     */
    @PostMapping({"/pc/list"})
    ResponseBean<List<RankVO>> list(@RequestBody RankQueryPO qryPO);

    /**
     * 分页查询头衔列表
     *
     * @param pagePO 筛选参数
     * @return 分页结果
     */
    @PostMapping("/pc/pageList")
    ResponseBean<PageResult<RankVO>> pageList(@RequestBody RankQueryPO pagePO);
}