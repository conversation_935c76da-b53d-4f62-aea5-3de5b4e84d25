package com.hishop.wine.model.po.miniApp.uploadShippingInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Data
@ApiModel(value = "ShippingPo", description = "物流信息列表")
public class ShippingPo {

    @ApiModelProperty(value = "物流单号，物流快递发货时必填，示例值: 323244567777 字符字节限制: [1, 128]", required = false)
    private String tracking_no;

    @ApiModelProperty(value = "物流公司编码，快递公司ID，参见「查询物流公司编码列表」，物流快递发货时必填， 示例值: DHL 字符字节限制: [1, 128]", required = false)
    private String express_company;

    @ApiModelProperty(value = "商品信息，例如：微信红包抱枕*1个，限120个字以内", required = true)
    @NotEmpty(message = "商品信息不能为空")
    private String item_desc;

    @ApiModelProperty(value = "联系方式，当发货的物流公司为顺丰时，联系方式为必填，收件人或寄件人联系方式二选一", required = false)
    private ContactPo contact;


}
