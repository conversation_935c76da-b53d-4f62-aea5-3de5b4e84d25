package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IsTradeManagedResponse {
    @SerializedName("errcode")
    private int errCode;

    @SerializedName("errmsg")
    private String errMsg;

    @SerializedName("is_trade_managed")
    private boolean isTradeManaged; // 是否已开通服务
}
