package com.hishop.wine.model.po.miniApp.uploadShippingInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Data
@ApiModel(value = "PayerPo", description = "支付者，支付者信息")
public class PayerPo {

    @ApiModelProperty(value = "用户标识，用户在小程序appid下的唯一标识。 下单前需获取到用户的Openid 示例值: oUpF8uMuAJO_M2pxb1Q9zNjWeS6o 字符字节限制: [1, 128]", required = true)
    @NotEmpty(message = "用户标识不能为空")
    private String openid;
}
