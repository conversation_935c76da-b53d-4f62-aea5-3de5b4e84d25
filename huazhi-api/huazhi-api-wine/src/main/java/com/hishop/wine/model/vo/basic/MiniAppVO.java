package com.hishop.wine.model.vo.basic;

import lombok.Data;

import java.util.List;

/**
 * 微信小程序信息vo
 *
 * <AUTHOR>
 * @date : 2023/6/25
 */
@Data
public class MiniAppVO {

    /**
     * app_id
     */
    private String appId;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * app_secret
     */
    private String appSecret;

    /**
     * 微信小程序消息服务器配置的token
     */
    private String token;

    /**
     * 微信小程序消息服务器配置的EncodingAESKey
     */
    private String aesKey;

    /**
     * 消息格式，XML或者JSON
     */
    private String msgDataFormat;

    /**
     * 绑定的模块名称
     */
    private String moduleName;

    /**
     * 绑定的模块编码
     */
    private List<String> moduleCode;
    /**
     * 绑定的店铺导航id
     */
    private long storeBarId;

    /**
     * 是否为独立设置店铺导航
     */
    private boolean izSelf;


    /**
     * 使用哪个模块的底部导航
     */
    private String storeBarModuleCode;
}
