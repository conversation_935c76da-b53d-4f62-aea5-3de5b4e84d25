package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.tags.TagsVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import java.util.List;

/**
 * 标签
 *
 * @author: guoyufeng
 * @date: 2024-01-26
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-user", url = "${feign.url.basic-system:basic-system}", path = "/wine/tags")
public interface TagsFeign {

    @GetMapping("/pc/list")
    ResponseBean<List<TagsVO>> list();
}