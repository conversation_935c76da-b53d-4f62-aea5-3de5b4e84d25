package com.hishop.wine.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  最近交易类型
 */
@Getter
@AllArgsConstructor
public enum RecentTradeType {

    /**
     * 取酒
     */
    EXTRACTION("extractionWine", "取酒"),
    /**
     * 封坛酒
     */
    SEALINGWINE("sealingWine", "封坛酒"),
    ;

    @EnumValue
    private final String value;

    private final String desc;

}
