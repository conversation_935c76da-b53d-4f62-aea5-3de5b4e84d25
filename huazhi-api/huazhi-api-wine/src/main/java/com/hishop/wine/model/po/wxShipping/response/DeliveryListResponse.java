package com.hishop.wine.model.po.wxShipping.response;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.DeliveryList;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/04/02/ $
 * @description:
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DeliveryListResponse {

    @SerializedName("errcode")
    private int errcode;

    @SerializedName("errmsg")
    private String errmsg;

    @SerializedName("count")
    private int count;

    @SerializedName("delivery_list")
    private List<DeliveryList> deliveryList;
}
