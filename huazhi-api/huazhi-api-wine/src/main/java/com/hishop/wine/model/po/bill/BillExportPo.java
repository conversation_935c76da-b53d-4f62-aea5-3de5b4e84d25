package com.hishop.wine.model.po.bill;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.enums.order.PayMethod;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 账单导出入参
 */
@Data
@ApiModel(value = "BillExportPo", description = "账单导出入参")
public class BillExportPo extends PageParam  {

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "支付方式 online_pay:在线支付 offline_pay:线下支付")
    @NotNull(message = "支付方式不能为空")
    private String payMethod;

    @ApiModelProperty(value = "天")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date day;

    @ApiModelProperty(value = "月份")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date month;
}
