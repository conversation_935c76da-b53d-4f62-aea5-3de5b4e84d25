package com.hishop.wine.enums.points;

/**
 * 会员积分业务类型，各个子系统可以定义自己的类型，统一在这里维护
 * 最开始本来积分分系统，所以没有统一维护，现在合在一起了，有些类型可能会重复，所以各个子系统单独定义
 * <AUTHOR>
 * @date 2023/8/17
 */
public interface BizType {

    /**
     * 模块编码，用于区分不同的业务模块
     */
    String moduleCode();
    /**
     * 积分业务类型编码
     */
    Integer getCode();
    /**
     * 积分业务类型描述
     */
    String getDesc();
    /**
     * 根据业务编码获取业务描述
     */
    String getDesc(Integer code);
    /**
     * 积分业务对应的父级操作类型
     */
    MemberPointsEnum.ModifiedType getParentType();

}
