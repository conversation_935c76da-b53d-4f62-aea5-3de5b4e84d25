package com.hishop.wine.model.vo.payment;

import com.hishop.wine.model.po.payment.PaymentSettingOfflinePO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 支付设置 返回对象
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
@Data
@ApiModel(value = "PaymentSettingVO", description = "支付设置返回对象")
public class PaymentSettingDetailVO {

    @ApiModelProperty("支付设置id")
    private Long id;

    @ApiModelProperty(value = "支付类型 WX_PAY-微信支付 ALI_PAY-支付宝支付 OFFLINE-线下支付")
    @NotBlank(message = "支付类型不能为空")
    private String paymentType;

    @ApiModelProperty(value = "支付方式名称")
    private String paymentName;

    @ApiModelProperty("场景值")
    private String sceneValue;

    @ApiModelProperty(value = "线下支付渠道 WECHAT_TRANSFER-微信转账 ALI_TRANSFER-支付宝转账 BANK_CARD_TRANSFER-银行卡转账")
    private String offlineChannel;

    @ApiModelProperty("微信支付参数")
    private PaymentSettingWxVO wxPay;

    @ApiModelProperty("线下支付参数")
    private PaymentSettingOfflinePO offlinePay;

    @ApiModelProperty("是否特殊收款公司")
    private Boolean izSpecial;

    @ApiModelProperty("收款公司编码")
    private String specialCode;
}
