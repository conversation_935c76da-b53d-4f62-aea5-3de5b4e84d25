package com.hishop.wine.model.vo.basic;

import com.hishop.common.annotation.Desensitized;
import com.hishop.common.enums.SensitiveTypeEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 用户账号信息
 *
 * <AUTHOR>
 * @date : 2023/7/11
 */
@Data
@ApiModel(value = "PCAccountInfoVO", description = "用户账号信息返回对象")
public class PcAccountInfoVO {

    @ApiModelProperty("头像")
    private String icon;

    @ApiModelProperty("用户名")
    private String username;

    @ApiModelProperty("姓名")
    private String realName;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("角色名称")
    private String roleName;

    @ApiModelProperty("手机号")
    private String mobile;

    @ApiModelProperty("密码")
    @Desensitized(type = SensitiveTypeEnum.ALL)
    private String password;

    @ApiModelProperty("邮箱")
    @Desensitized(type = SensitiveTypeEnum.EMAIL)
    private String email;

    @ApiModelProperty("微信昵称")
    private String nickName;

    @ApiModelProperty("是否可以修改角色")
    private Boolean editRoleAble;

}
