package com.hishop.wine.model.po.micropage;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
@ApiModel(value = "MicropageUpdatePO", description = "微页面分组修改对象")
public class MicropageCategoryUpdatePO {
    @ApiModelProperty(value = "id")
    private int id;

    @ApiModelProperty(value = "分类名称")
    private String name;

    @ApiModelProperty(value = "上级分类id")
    private Long parentId;

    @ApiModelProperty(value = "级数 传0即可")
    private int level;

}
