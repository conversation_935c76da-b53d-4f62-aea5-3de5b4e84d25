package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:  确认收货提醒
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ConfirmReceiveRequest extends BaseRequest {
    @SerializedName("transaction_id")
    private String transactionId; // 微信支付订单号

    @SerializedName("merchant_id")
    private String merchantId; // 商户号

    @SerializedName("merchant_trade_no")
    private String merchantTradeNo; // 商户内部订单号

    @SerializedName("received_time")
    private long receivedTime; // 签收时间戳（毫秒）
}
