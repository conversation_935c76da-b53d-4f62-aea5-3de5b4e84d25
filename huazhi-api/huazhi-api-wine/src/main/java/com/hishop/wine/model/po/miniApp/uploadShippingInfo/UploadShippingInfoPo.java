package com.hishop.wine.model.po.miniApp.uploadShippingInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Data
@ApiModel(value = "UploadShippingInfoPo", description = "小程序发货信息录入入参")
public class UploadShippingInfoPo {

    @ApiModelProperty(value = "accessToken", required = true)
    @NotEmpty(message = "accessToken不能为空")
    private String accessToken;

    @ApiModelProperty(value = "订单，需要上传物流信息的订单", required = true)
    @NotNull(message = "订单，需要上传物流信息不能为空")
    private OrderKeyPo order_key;

    @ApiModelProperty(value = "物流模式，发货方式枚举值：1、实体物流配送采用快递公司进行实体物流配送形式 2、同城配送 3、虚拟商品，虚拟商品，例如话费充值，点卡等，无实体配送形式 4、用户自提", required = true)
    @NotNull(message = "物流模式不能为空")
    private Integer logistics_type;

    @ApiModelProperty(value = "发货模式，发货模式枚举值：1、UNIFIED_DELIVERY（统一发货）2、SPLIT_DELIVERY（分拆发货） 示例值: UNIFIED_DELIVERY", required = true)
    @NotNull(message = "发货模式不能为空")
    private Integer delivery_mode;

    @ApiModelProperty(value = "分拆发货模式时必填，用于标识分拆发货模式下是否已全部发货完成，只有全部发货完成的情况下才会向用户推送发货完成通知。示例值: true/false", required = false)
    private Boolean is_all_delivered;

    @ApiModelProperty(value = "物流信息列表，发货物流单列表，支持统一发货（单个物流单）和分拆发货（多个物流单）两种模式，多重性: [1, 10]", required = true)
    @NotNull(message = "物流信息列表不能为空")
    @Size(min = 1, message = "请至少上传1个物流信息")
    private List<ShippingPo> shipping_list;

    @ApiModelProperty(value = "上传时间，用于标识请求的先后顺序 示例值: `2022-12-15T13:29:35.120+08:00`", required = true)
    @NotEmpty(message = "上传时间不能为空")
    private String upload_time;

    @ApiModelProperty(value = "支付者，支付者信息", required = true)
    @NotNull(message = "支付者信息不能为空")
    private PayerPo payer;

}
