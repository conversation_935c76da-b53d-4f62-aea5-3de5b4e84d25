package com.hishop.wine.model.po.material;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

@Data
@ApiModel(value = "TranscodeInfoPO", description = "转码信息")
@JsonIgnoreProperties(ignoreUnknown = true)
public class TranscodeInfoPO {
    @JsonProperty("task_id")
    private String taskId;

    @JsonProperty("status")
    private String status;
}
