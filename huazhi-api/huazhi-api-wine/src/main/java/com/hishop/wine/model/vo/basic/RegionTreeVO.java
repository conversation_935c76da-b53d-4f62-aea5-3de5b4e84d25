package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/13
 */
@Data
@ApiModel(value = "RegionTreeVO", description = "大区树形结构")
public class RegionTreeVO {

    @ApiModelProperty("大区名称")
    private String regionName;

    @ApiModelProperty("下级地区")
    private List<DistrictTreeVO> childList;

}
