package com.hishop.wine.model.po.logisticsCode;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/07/23/ $
 * @description:
 */
@Data
@ApiModel("根据批次id获取物流码信息请求参数")
public class LogisticsCodeFeignPo {

    @ApiModelProperty(value = "物流单号")
    private String logisticsCode;

    @ApiModelProperty(value = "批次id集合")
    private List<Long> ids;

    @ApiModelProperty(value = "状态")
    private Integer status;
}
