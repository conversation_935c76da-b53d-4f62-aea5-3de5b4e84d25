package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 订单详情
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OrderInfo {
    @SerializedName("transaction_id")
    private String transactionId;

    @SerializedName("merchant_trade_no")
    private String merchantTradeNo;

    @SerializedName("order_state")
    private int orderState; // 1-待发货,2-已发货,3-确认收货,4-交易完成

    @SerializedName("shipping")
    private ShippingInfo shipping;
}
