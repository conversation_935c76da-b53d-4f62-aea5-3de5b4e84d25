package com.hishop.wine.model.po.product;

import com.hishop.common.pojo.media.VideoBean;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 产品表 新增入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductCreatePO", description = "产品表新增入参对象")
public class ProductCreatePO {

    @ApiModelProperty(value = "产品类型 1-商品 2-礼品", required = true)
    @NotNull(message = "产品类型不能为空")
    @Min(value = 1, message = "产品类型取值异常, 只能为1/2")
    @Max(value = 2, message = "产品类型取值异常, 只能为1/2")
    private Integer productType;

    @ApiModelProperty(value = "产品名称", required = true)
    @NotBlank(message = "产品名称不能为空")
    @Size(max = 60, message = "产品名称最大长度为60")
    private String productName;

    @ApiModelProperty(value = "产品编码", required = true)
    @NotBlank(message = "产品编码不能为空")
    @Size(max = 20, message = "产品编码最大长度为20")
    private String productCode;

    @ApiModelProperty(value = "产品单位")
    private String productUnit;

    @ApiModelProperty(value = "产品分类id", required = true)
    @NotNull(message = "请选择产品分类")
    private Long productCategoryId;

    @ApiModelProperty(value = "市场价格")
    private BigDecimal marketPrice;

    @ApiModelProperty(value = "产品图片列表", required = true)
    @NotNull(message = "请上传产品图片")
    @Size(min = 1, message = "请至少上传一张产品图片")
    @Size(max = 15, message = "产品图片最多上传15张")
    private List<String> productImgList;

    @ApiModelProperty("产品视频")
    private VideoBean mainVideo;

    @ApiModelProperty(value = "产品详情图列表", required = true)
    private List<String> productDetailImgList;

}
