package com.hishop.wine.model.vo.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 链接目标
 *
 * <AUTHOR>
 * @date : 2023/7/19
 */
@Data
@ApiModel(value = "LinkTargetVO", description = "链接目标")
public class LinkTargetVO {

    @ApiModelProperty("唯一值")
    private String id;

    @ApiModelProperty("小程序id, 只有targetType=1000时有值")
    private String appId;

    @ApiModelProperty("链接名称")
    private String linkName;

    @ApiModelProperty("链接目标类型 SYSTEM_MINI_APP-系统小程序 OTHER_MINI_APP-其他小程序 OFFICIAL_ACCOUNT-公众号文章 MICRO_PAGE-微页面")
    private String targetType;

    @ApiModelProperty("关联模块列表")
    private List<LinkModuleVO> moduleList;

}
