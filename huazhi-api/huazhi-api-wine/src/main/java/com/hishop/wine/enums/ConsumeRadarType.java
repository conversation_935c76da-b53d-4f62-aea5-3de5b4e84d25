package com.hishop.wine.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 *  交易雷达类型
 */
@Getter
@AllArgsConstructor
public enum ConsumeRadarType {

    /**
     * 取酒
     */
    EXTRACTION("EXTRACTIONWINE", "取酒"),
    /**
     * 封坛酒
     */
    SEALINGWINE("SEALINGWINE", "封坛酒"),
    ;

    @EnumValue
    private final String value;

    private final String desc;
}
