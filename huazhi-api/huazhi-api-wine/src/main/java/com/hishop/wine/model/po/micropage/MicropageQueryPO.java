package com.hishop.wine.model.po.micropage;

import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.enums.MicropageEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "MicropageQueryPO", description = "微页面分页查询对象")
public class MicropageQueryPO extends PageParam {

    /*@ApiModelProperty(value = "状态 0：全部 1：草稿 2：上架")
    private Integer status;*/

    @ApiModelProperty(value = "分组id 0-未分组 -1-全部 -2-草稿箱 大于0为分组id", required = true)
    private Long categoryId;

    @ApiModelProperty(value = "微页面名称")
    private String name;

}
