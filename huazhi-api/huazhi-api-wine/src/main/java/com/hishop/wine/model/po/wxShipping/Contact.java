package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:联系方式
 */

@Data
@NoArgsConstructor
@AllArgsConstructor
public class Contact {
    @SerializedName("consignor_contact")
    private String consignorContact; // 寄件人联系方式（顺丰必填）

    @SerializedName("receiver_contact")
    private String receiverContact; // 收件人联系方式（顺丰必填）
}
