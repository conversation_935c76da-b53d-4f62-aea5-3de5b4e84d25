package com.hishop.wine.model.po.material;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @description: 资源查询类
 * @author: tomliu
 * @create: 2022/06/25 13:20
 **/
@Data
@ApiModel("资源删除对象")
public class MaterialDeletePO extends PageParam {

    /**
     * 资源分组id
     */
    @ApiModelProperty(value = "资源id集合", required = true)
    @NotNull(message = "资源分组id不能为空")
    private List<Long> ids;

    @ApiModelProperty("是否删除文件")
    private boolean removeFiles;

}
