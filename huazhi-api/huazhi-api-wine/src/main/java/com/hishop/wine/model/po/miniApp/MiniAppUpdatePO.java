package com.hishop.wine.model.po.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 小程序创建vo
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "MiniAppUpdatePO", description = "小程序更新入参")
public class MiniAppUpdatePO {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "请输入id")
    private Long id;

    @ApiModelProperty(value = "小程序id", required = true)
    @NotBlank(message = "请输入appId")
    private String appId;

    @ApiModelProperty(value = "appSecret", required = true)
    @NotBlank(message = "请输入appSecret")
    private String appSecret;

    @ApiModelProperty("小程序原始id")
    private String originalId;

}
