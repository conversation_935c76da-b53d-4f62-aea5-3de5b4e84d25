package com.hishop.wine.model.po.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 小程序创建vo
 *
 * <AUTHOR>
 * @date : 2023/7/18
 */
@Data
@ApiModel(value = "MiniAppUpdateSecretPO", description = "小程序更新密钥入参")
public class MiniAppUpdateSecretPO {

    @ApiModelProperty(value = "主键", required = true)
    @NotNull(message = "请输入id")
    private Long id;

    @ApiModelProperty(value = "appSecret", required = true)
    @NotBlank(message = "请输入appSecret")
    private String appSecret;


}
