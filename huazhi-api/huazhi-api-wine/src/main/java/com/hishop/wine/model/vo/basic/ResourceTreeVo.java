package com.hishop.wine.model.vo.basic;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 资源信息
 *
 * <AUTHOR>
 * @date : 2023/6/17
 */
@Data
@ApiModel("资源树信息")
public class ResourceTreeVo implements Serializable {

    @ApiModelProperty("资源id")
    private Long id;

    @ApiModelProperty("上级资源id")
    private Long parentId;

    @ApiModelProperty("资源名称")
    private String name;

    @ApiModelProperty("前端路由")
    private String path;

    /**
     * 排序字段 不返回前端
     */
    @JsonIgnore
    private Integer orderNum;

    @ApiModelProperty("下级资源")
    private List<ResourceTreeVo> children;

    @JsonProperty("isGroup")
    private Boolean izGroup;

}
