package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import cn.hutool.setting.AbsSetting;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 协议配置
 *
 * <AUTHOR>
 * @date : 2023/8/23
 */
@Data
@ApiModel(value = "ProtocolSettingVO", description = "协议配置")
public class ProtocolSettingVO extends AbstractSetting {

    @ApiModelProperty(value = "用户协议")
    private String userAgreement;

    @ApiModelProperty(value = "隐私政策")
    private String privacyPolicy;

    @Override
    protected void initDefault() {
        this.setUserAgreement(StrUtil.EMPTY);
        this.setPrivacyPolicy(StrUtil.EMPTY);
    }
}
