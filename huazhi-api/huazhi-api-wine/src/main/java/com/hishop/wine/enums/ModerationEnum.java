package com.hishop.wine.enums;

import com.hishop.wine.constants.ModerationMqConstants;
import lombok.Getter;

/**
 * 内容审核枚举
 *
 * <AUTHOR>
 * @date : 2023/9/11
 */
public class ModerationEnum {

    /**
     * 业务类型枚举 FANS_CONTENT-粉丝俱乐部内容管理
     */
    @Getter
    public enum BizType {

        /**
         * 粉丝俱乐部内容审核
         */
        FANS_CONTENT(ModuleEnums.fans_club.name(), "粉丝俱乐部内容审核", ModerationMqConstants.FANS_CONTENT_MODERATION_CALLBACK_TOPIC);

        private String moduleCode;
        private String desc;
        private String callBackTopic;


        BizType(String moduleCode, String desc, String callBackTopic) {
            this.moduleCode = moduleCode;
            this.desc = desc;
            this.callBackTopic = callBackTopic;
        }

        public static BizType getEnum(String name) {
            for (BizType bizType : BizType.values()) {
                if (bizType.name().equalsIgnoreCase(name)) {
                    return bizType;
                }
            }
            return null;
        }
    }

    /**
     * 审核状态枚举 待审核-WAITING 审核中-AUDITING SUCCESS-审核通过 FAIL-审核失败
     */
    @Getter
    public enum Status {

        /**
         * 待审核
         */
        WAITING("待审核"),
        /**
         * 审核中
         */
        AUDITING("审核中"),
        /**
         * 审核通过
         */
        SUCCESS("审核通过"),
        /**
         * 审核失败
         */
        FAIL("审核失败");

        private String desc;

        Status(String desc) {
            this.desc = desc;
        }

        public static Status getEnum(String name) {
            for (Status status : Status.values()) {
                if (status.name().equalsIgnoreCase(name)) {
                    return status;
                }
            }
            return null;
        }
    }

    /**
     * 审核类型枚举 审核类型 TEXT-文本 IMAGE-图片 VIDEO-视频 AUDIO-音频
     */
    @Getter
    public enum Type {

        /**
         * 文本
         */
        TEXT("文本"),
        /**
         * 图片
         */
        IMAGE("图片"),
        /**
         * 视频
         */
        VIDEO("视频"),
        /**
         * 音频
         */
        AUDIO("音频");

        private String desc;

        Type(String desc) {
            this.desc = desc;
        }

        public static Type getEnum(String name) {
            for (Type type : Type.values()) {
                if (type.name().equalsIgnoreCase(name)) {
                    return type;
                }
            }
            return null;
        }
    }

}
