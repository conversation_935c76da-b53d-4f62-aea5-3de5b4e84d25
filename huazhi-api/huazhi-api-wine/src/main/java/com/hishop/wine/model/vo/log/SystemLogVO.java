package com.hishop.wine.model.vo.log;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**   
 * 操作日志表 返回对象
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Data
@ApiModel(value = "SystemLogVO", description = "操作日志表返回对象")
public class SystemLogVO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty("板块名称")
    private String moduleName;

    @ApiModelProperty(value = "操作名称")
	private String operationName;
    
    @ApiModelProperty(value = "业务键值")
	private String businessKey;

    @ApiModelProperty("业务描述")
    private String businessDesc;

    @ApiModelProperty("操作账号")
    private String operatorUsername;

    @ApiModelProperty("操作手机号")
    private String operatorMobile;

    @ApiModelProperty("操作时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;
    

}
