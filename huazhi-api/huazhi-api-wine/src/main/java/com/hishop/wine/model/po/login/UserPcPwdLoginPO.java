package com.hishop.wine.model.po.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 账号密登录参数
 *
 * <AUTHOR>
 * @date : 2023/6/16
 */
@Data
@ApiModel("PC端账号密码登录参数")
public class UserPcPwdLoginPO extends UserBaseLoginPO {

    @NotBlank(message = "账号不能为空")
    @ApiModelProperty(value = "用户名/手机号", required = true)
    private String account;

    @NotBlank(message = "密码不能为空")
    @ApiModelProperty(value = "密码", required = true)
    private String password;

    @ApiModelProperty(value = "记住我")
    private Boolean rememberMe;

}
