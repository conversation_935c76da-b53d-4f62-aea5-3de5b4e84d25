package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 修改密码入参
 *
 * <AUTHOR>
 * @date : 2023/7/11
 */
@Data
@ApiModel(value = "PasswordUpdatePO", description = "修改密码参数")
public class PasswordUpdatePO {

    @ApiModelProperty(value = "旧密码, 首次绑定无需传")
    private String oldPassword;

    @ApiModelProperty(value = "手机号验证码, 通过验证码修改密码时必传")
    private String code;

    @ApiModelProperty(value = "密码", required = true)
    @NotBlank(message = "请输入密码")
    @Size(min = 6, message = "密码长度为6-18位")
    @Size(max = 18, message = "密码长度为6-18位")
    private String password;

    @ApiModelProperty(value = "确认密码", required = true)
    @NotBlank(message = "请输入确认密码")
    @Size(min = 6, message = "确认密码长度为6-18位")
    @Size(max = 18, message = "确认密码长度为6-18位")
    private String passwordAgain;

}
