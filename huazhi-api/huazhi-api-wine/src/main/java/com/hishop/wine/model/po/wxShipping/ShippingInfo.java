package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 发货信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ShippingInfo {
    @SerializedName("delivery_mode")
    private int deliveryMode;

    @SerializedName("logistics_type")
    private int logisticsType;

    @SerializedName("finish_shipping")
    private boolean finishShipping;

    @SerializedName("shipping_list")
    private List<ShippingItem> shippingList;
}
