package com.hishop.wine.model.po.pages;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**   
 * 页面配置表 更新入参对象
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Data
@ApiModel(value = "PagesUpdatePO", description = "页面配置表更新入参对象")
public class PagesUpdatePO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty(value = "名称")
	private String name;

    @ApiModelProperty(value = "请求地址")
	private String path;

    @ApiModelProperty(value = "创建者ID")
	private Long createBy;

    @ApiModelProperty(value = "创建时间")
	private Date createTime;

    @ApiModelProperty(value = "更新者ID")
	private Long updateBy;

    @ApiModelProperty(value = "更新时间")
	private Date updateTime;


}
