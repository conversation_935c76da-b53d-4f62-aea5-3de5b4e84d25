package com.hishop.wine.model.po.pages;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hishop.common.pojo.page.PageParam;
import java.util.Date;

/**   
 * 页面配置表 查询入参对象
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Data
@ApiModel(value = "PagesQueryPO", description = "页面配置表查询入参对象")
public class PagesQueryPO extends PageParam {
	
    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "模块编码")
	private String moduleCode;

    @ApiModelProperty(value = "名称")
	private String name;

    @ApiModelProperty(value = "请求地址")
	private String path;

    @ApiModelProperty(value = "创建者ID")
	private Long createBy;

    @ApiModelProperty(value = "创建时间")
	private Date createTime;

    @ApiModelProperty(value = "更新者ID")
	private Long updateBy;

    @ApiModelProperty(value = "更新时间")
	private Date updateTime;


}
