package com.hishop.wine.model.po.product;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.hishop.common.pojo.page.PageParam;

/**
 * 产品分类表 查询入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@ApiModel(value = "ProductCategoryQueryPO", description = "产品分类表查询入参对象")
public class ProductCategoryQueryPO extends PageParam {

    @ApiModelProperty(value = "分类名称")
    private String categoryName;

}
