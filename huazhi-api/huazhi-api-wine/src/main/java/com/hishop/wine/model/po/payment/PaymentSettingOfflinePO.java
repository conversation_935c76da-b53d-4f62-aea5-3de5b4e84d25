package com.hishop.wine.model.po.payment;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 线下支付参数
 * @author: chenzw
 * @date: 2024/1/8 14:06
 */
@Data
@ApiModel(value = "PaymentSettingOfflinePO", description = "线下支付参数")
public class PaymentSettingOfflinePO {

    /**
     * 收款人姓名
     */
    @ApiModelProperty(value = "收款人姓名",required = true)
    private String beneficiaryName;

    /**
     * 收款人账号
     */
    @ApiModelProperty(value = "收款人账号",required = true)
    private String beneficiaryAccount;

    /**
     * 开户银行名称
     */
    @ApiModelProperty(value = "开户银行名称")
    private String bankName;

    /**
     * 二维码信息
     */
    @ApiModelProperty(value = "二维码信息")
    private String qrCode;

    @ApiModelProperty(value = "是否特殊")
    private Boolean izSpecial;

    @ApiModelProperty(value = "收款公司编码")
    private String specialCode;

    /**
     * 使用Assert 检测参数不为空
     */
    public void checkParam() {
        Assert.isTrue(StrUtil.isNotBlank(beneficiaryName), "收款人姓名不能为空");
        Assert.isTrue(StrUtil.isNotBlank(beneficiaryAccount), "收款人账号不能为空");
    }
}
