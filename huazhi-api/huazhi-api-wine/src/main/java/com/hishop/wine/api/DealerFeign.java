package com.hishop.wine.api;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.dealer.DealerDetailVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * @description: 经销商服务接口
 * @author: chenzw
 * @date: 2024/7/10 15:37
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-dealer", url = "${feign.url.basic-system:basic-system}", path = "/wine/dealer")
public interface DealerFeign {

    /**
     * 查询经销商详情
     *
     * @param id 经销商id
     * @return 经销商详情
     */
    @GetMapping("/detail")
    ResponseBean<DealerDetailVo> detail(@RequestParam Long id);

    /**
     * 查询经销商列表
     *
     * @param idBatchPo 请求参数
     * @return 经销商列表
     */
    @PostMapping("/queryListByCode")
    ResponseBean<List<DealerDetailVo>> queryListByCode(@RequestBody @Validated IdBatchPO<String> idBatchPo);
}
