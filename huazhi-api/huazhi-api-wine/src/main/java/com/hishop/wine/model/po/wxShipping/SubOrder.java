package com.hishop.wine.model.po.wxShipping;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 子单信息
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SubOrder {
    @SerializedName("order_key")
    private OrderKey orderKey;

    @SerializedName("delivery_mode")
    private int deliveryMode;

    @SerializedName("logistics_type")
    private int logisticsType;

    @SerializedName("shipping_list")
    private List<ShippingItem> shippingList;

    @SerializedName("is_all_delivered")
    private Boolean isAllDelivered;
}
