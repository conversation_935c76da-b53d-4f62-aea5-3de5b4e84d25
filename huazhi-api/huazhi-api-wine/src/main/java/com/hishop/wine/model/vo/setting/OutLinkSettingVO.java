package com.hishop.wine.model.vo.setting;

import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * 小程序外链配置
 *
 * <AUTHOR>
 * @date : 2023/9/11
 */
@Data
@ApiModel(value = "OutLinkSettingVO", description = "外链配置")
public class OutLinkSettingVO extends AbstractSetting {

    @ApiModelProperty(value = "外链配置")
    private List<Link> links;

    @Override
    protected void initDefault() {
        this.links = new ArrayList<>();
    }

    @Data
    public class Link {
        private String key;
        private String value;
    }
}
