package com.hishop.wine.model.po.moderation;

import com.hishop.common.exception.BusinessException;
import com.hishop.wine.enums.ModerationEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**   
 * 内容审核表 新增入参对象
 * @author: HuBiao
 * @date: 2023-09-11
 */

@Data
@ApiModel(value = "ContentModerationCreatePO", description = "内容审核表新增入参对象")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContentModerationCreatePO {

    @ApiModelProperty(value = "业务类型")
    @NotNull(message = "业务类型不能为空")
	private ModerationEnum.BizType bizType;

    @ApiModelProperty(value = "业务编码")
    @NotEmpty(message = "业务编码不能为空")
	private String bizCode;

    @ApiModelProperty(value = "业务描述")
	private String bizDesc;

    /**
     * 暂时不去指定一些审核的自定义配置, 统一使用默认的, 应该够用
     */
    @ApiModelProperty("需要审核的文本列表")
    private List<String> textList;

    @ApiModelProperty("需要审核的图片列表")
    private List<String> imageList;

    @ApiModelProperty("需要审核的视频列表")
    private List<String> videoList;

    @ApiModelProperty("需要审核的音频列表")
    private List<String> audioList;

    public void check() {
        // 文本、图片、视频、音频至少有一个不为空
        if (CollectionUtils.isEmpty(textList) && CollectionUtils.isEmpty(imageList) && CollectionUtils.isEmpty(videoList) && CollectionUtils.isEmpty(audioList)) {
            throw new BusinessException("文本、图片、视频、音频至少有一个不为空");
        }
    }
}
