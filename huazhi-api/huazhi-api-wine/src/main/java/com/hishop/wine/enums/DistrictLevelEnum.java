package com.hishop.wine.enums;

import cn.hutool.core.util.StrUtil;
import lombok.Getter;

/**
 * 地区等级枚举
 *
 * <AUTHOR>
 * @date : 2023/6/29
 */
@Getter
public enum DistrictLevelEnum {

    /**
     * 省
     */
    PROVINCE(1, "省"),

    /**
     * 市
     */
    CITY(2, "市"),

    /**
     * 区
     */
    AREA(3, "区"),

    /**
     * 街道
     */
    STREET(4, "街道");

    /**
     * 地区等级
     */
    private final Integer level;

    /**
     * 名称
     */
    private final String name;

    DistrictLevelEnum(Integer level, String name) {
        this.level = level;
        this.name = name;
    }

    public static String getNameByLevel(Integer level) {
        for (DistrictLevelEnum districtLevelEnum : DistrictLevelEnum.values()) {
            if (districtLevelEnum.getLevel().equals(level)) {
                return districtLevelEnum.getName();
            }
        }
        return StrUtil.EMPTY;
    }
}
