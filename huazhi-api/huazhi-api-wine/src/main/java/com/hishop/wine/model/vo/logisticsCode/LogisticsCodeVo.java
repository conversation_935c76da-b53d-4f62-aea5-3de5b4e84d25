package com.hishop.wine.model.vo.logisticsCode;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * 物流码管理表(LogisticsCode)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:56
 */
@Data
@ApiModel(value = "LogisticsCodeVo", description = "物流码管理表")
public class LogisticsCodeVo {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "一级物流编码")
    private String codeFirst;
    
    @ApiModelProperty(value = "二级物流编码")
    private String codeSecondary;
    
    @ApiModelProperty(value = "瓶内码")
    private String codeBottle;
    
    @ApiModelProperty(value = "产品编码")
    private String productCode;

    @ApiModelProperty(value = "产品id")
    private Long productId;

    @ApiModelProperty(value = "产品名称")
    private String productName;
    
    @ApiModelProperty(value = "物流码类型 0盒码 1箱码")
    private Integer codeType;

    @ApiModelProperty(value = "物流码类型 盒码 箱码")
    private String codeTypeShow;
    
    @ApiModelProperty(value = "状态 0未使用 1已使用")
    private Integer status;

    @ApiModelProperty(value = "状态 未使用 已使用")
    private String statusShow;

    @ApiModelProperty(value = "物流码类别 0物流码 1扫码营销物流码")
    private Integer codeCategory;

    @ApiModelProperty(value = "物流码类别 物流码 扫码营销物流码")
    private String codeCategoryShow;

    @ApiModelProperty(value = "批次id")
    private Long fileImportId;

    @ApiModelProperty(value = "批次名称")
    private String name;

    @ApiModelProperty(value = "盒码数量")
    private Integer num;

    @ApiModelProperty(value = "导入时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
    
    
}
