package com.hishop.wine.model.po.transaction;

import com.hishop.wine.enums.TransactionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 打款入参
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Data
@ApiModel(value = "TransactionEntPayPO", description = "发起打款入参")
public class TransactionEntPayPO {

    @ApiModelProperty(value = "交易方式 WX_PAY-微信支付")
    private TransactionEnum.MethodEnum transactionMethod = TransactionEnum.MethodEnum.WX_PAY;

    @ApiModelProperty(value = "业务类型", required = true)
    @NotNull(message = "请指定业务类型")
    private TransactionEnum.BizTypeEnum bizType;

    @ApiModelProperty(value = "业务端业务编码", required = true)
    @NotBlank(message = "业务类型编码不能为空")
    private String bizCode;

    @ApiModelProperty(value = "交易金额", required = true)
    @NotNull(message = "交易金额不能为空")
    @Min(value = 0, message = "交易金额不能小于0")
    private BigDecimal amount;

    @ApiModelProperty(value = "用户ID", hidden = true)
    private Long userId;

    @ApiModelProperty(value = "业务描述", required = true)
    @NotBlank(message = "业务描述")
    private String description;
}
