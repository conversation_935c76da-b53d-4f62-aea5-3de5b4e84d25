package com.hishop.wine.model.vo.refund;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**   
 * 退款原因配置表 返回对象
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Data
@ApiModel(value = "RefundReasonConfigVO", description = "退款原因配置表返回对象")
public class RefundReasonConfigVO {
	
    @ApiModelProperty(value = "退款原因id")
	private Long id;

    @ApiModelProperty(value = "售后类型（1：仅退款，2：退货退款，3：退款）,多个用逗号隔开")
    private List<Integer> refundTypes;

    @ApiModelProperty(value = "售后类型中文名")
    private List<String> refundTypeStrList;

    @ApiModelProperty(value = "退款原因")
	private String refundReason;

}
