package com.hishop.wine.model.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Date 2025/03/20/ $
 * @description:
 */
@Data
public class BizUserCodeByPhoneDto implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "B端客户编码")
    private String bizUserCode;

    @ApiModelProperty(value = "B端客户名称")
    private String bizUserName;

    @ApiModelProperty(value = "B端客户手机号")
    private String bizPhone;

    @ApiModelProperty(value = "C端客户手机号")
    private String phone;
}
