package com.hishop.wine.model.po.storebar;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;

@Data
@ApiModel(value = "StoreBarPO", description = "底部导航PO")
public class StoreBarPO {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "修改时间")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDate updateTime;

    @ApiModelProperty(value = "创建时间")
    @TableField(fill = FieldFill.INSERT)
    private LocalDate createTime;

    @ApiModelProperty(value = "创建者")
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    @ApiModelProperty(value = "修改者")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    @ApiModelProperty(value = "导航json")
    private String barJson;

    @ApiModelProperty(value = "背景色")
    private String bgColor;

    @ApiModelProperty(value = "字体色")
    private String fontColor;

    @ApiModelProperty(value = "选中色")
    private String selectColor;

    @ApiModelProperty(value = "模块编码")
    private String moduleCode;

    @ApiModelProperty(value = "小程序appId")
    private String appId;
}
