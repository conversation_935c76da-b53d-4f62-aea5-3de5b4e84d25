package com.hishop.wine.model.vo.setting;

import cn.hutool.core.util.StrUtil;
import com.hishop.setting.AbstractSetting;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 腾讯地图配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Data
@ApiModel(value = "SmsSettingVO", description = "腾讯地图配置")
public class TencentMapSettingVO extends AbstractSetting {

    @ApiModelProperty(value = "地图key")
    private String key;

    @ApiModelProperty(value = "appSecret")
    private Integer dataVersion;

    @Override
    protected void initDefault() {
        this.setKey(StrUtil.EMPTY);
        this.setDataVersion(0);
    }
}
