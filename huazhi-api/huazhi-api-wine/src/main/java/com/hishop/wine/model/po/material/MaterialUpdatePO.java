package com.hishop.wine.model.po.material;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 素材库 编辑入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-20
 */
@Data
@ApiModel(value = "MaterialCreatePO", description = "素材库编辑入参对象")
public class MaterialUpdatePO {

    @ApiModelProperty(value = "资源id", required = true)
    @NotNull(message = "资源id不能为空")
    private Long id;

    @ApiModelProperty(value = "资源分组id", required = true)
    @NotNull(message = "资源分组id不能为空")
    private Long materialCategoryId;

    @ApiModelProperty(value = "资源标题(带扩展名)", required = true)
    @NotBlank(message = "资源标题不能为空")
    private String title;
}
