package com.hishop.wine.model.po.sms;

import com.hishop.wine.enums.SmsEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.*;
import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Data
@ApiModel(value = "SendSmsPO", description = "发送短信参数")
public class SendSmsPO {

    /**
     * 短信类型 {@link SmsEnum.Channel}
     */
    @ApiModelProperty(value = "短信类型 0-通知短信 1-营销短信 2-验证码短信", required = true)
    @NotNull(message = "短信类型不能为空")
    @Min(value = 0, message = "短信类型不正确")
    @Max(value = 2, message = "短信类型不正确")
    private Integer channel;

    @ApiModelProperty(value = "短信内容", required = true)
    @NotBlank(message = "短信内容不能为空")
    private String content;

    @ApiModelProperty("手机号列表")
    @Size(min = 1, message = "手机号列表不能为空")
    @NotNull(message = "手机号列表不能为空")
    private List<String> mobileList;

    public static SendSmsPO of(Integer channel, String content, List<String> mobileList) {
        SendSmsPO sendSmsPO = new SendSmsPO();
        sendSmsPO.setChannel(channel);
        sendSmsPO.setContent(content);
        sendSmsPO.setMobileList(mobileList);
        return sendSmsPO;
    }

}
