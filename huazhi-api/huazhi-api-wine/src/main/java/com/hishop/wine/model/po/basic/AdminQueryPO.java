package com.hishop.wine.model.po.basic;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 管理员列表参数
 *
 * <AUTHOR>
 * @date : 2023/6/21
 */
@Data
@ApiModel(value = "AdminQueryPO", description = "管理员列表参数")
public class AdminQueryPO extends PageParam {

    @ApiModelProperty("用户名/手机号/邮箱")
    private String searchValue;

    @ApiModelProperty("角色id")
    private Long roleId;

    @ApiModelProperty("部门id")
    private Long departmentId;


    @ApiModelProperty("查询部门置顶负责人-首位")
    private Boolean headFlag;

    @ApiModelProperty(value = "部门ids",hidden = true)
    private List<Long> departmentIds;
}
