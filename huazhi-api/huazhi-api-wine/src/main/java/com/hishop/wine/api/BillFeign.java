package com.hishop.wine.api;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillGroupVo;
import com.hishop.wine.model.vo.bill.BillVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import javax.validation.Valid;
import java.util.List;

/**
 * 账单对账 微服务接口
 *
 * @author: guoyu<PERSON>
 * @date: 2024-01-31
 */

@FeignClient(name = "basic-system",
        contextId = "hishop-hishop-wine-bill",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/bill")
public interface BillFeign {
    /**
     * 明细分页查询
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/pageList")
    public ResponseBean<PageResult<BillVo>> pageList(@Validated @RequestBody BillQueryPo billQueryPo);
    /**
     * 明细总收入
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/pageListCount")
    public ResponseBean<BillCountVo> pageListCount(@Validated @RequestBody BillQueryPo billQueryPo);
    /**
     * 月统计分页查询
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/monthPageList")
    public ResponseBean<PageResult<BillGroupVo>> monthPageList(@Validated @RequestBody BillQueryPo billQueryPo);
    /**
     * 月统计总收入
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/monthPageListCount")
    public ResponseBean<BillCountVo> monthPageListCount(@Validated @RequestBody BillQueryPo billQueryPo);
    /**
     * 日统计分页查询
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/dayPageList")
    public ResponseBean<PageResult<BillGroupVo>> dayPageList(@Validated @RequestBody BillQueryPo billQueryPo);
    /**
     * 日统计总收入
     * @param billQueryPo 入参
     * @return
     */
    @PostMapping("/dayPageListCount")
    public ResponseBean<BillCountVo> dayPageListCount(@Validated @RequestBody BillQueryPo billQueryPo);

    /**
     * 导出查询
     * @param pagePO 入参
     * @return
     */
    @PostMapping("/exportList")
    public ResponseBean<List<BillVo>> exportList(@RequestBody @Valid BillExportPo pagePO);
}