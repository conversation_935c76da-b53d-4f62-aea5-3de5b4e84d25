package com.hishop.wine.model.po.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 账号密登录参数
 *
 * <AUTHOR>
 * @date : 2023/6/16
 */
@Data
@ApiModel("小程序静默登录参数")
public class UserMiniDefaultLoginPO extends UserBaseLoginPO {

    /**
     * 微信code
     */
    @ApiModelProperty(value = "微信code", required = true)
    @NotBlank(message = "请传入微信code")
    private String code;

}
