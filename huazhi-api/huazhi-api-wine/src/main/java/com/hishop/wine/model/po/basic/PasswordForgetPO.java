package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 忘记密码入参
 *
 * <AUTHOR>
 * @date : 2023/7/11
 */
@Data
@ApiModel(value = "PasswordForgetPO", description = "忘记密码入参")
public class PasswordForgetPO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "请输入手机号")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "手机号验证码", required = true)
    @NotBlank(message = "请输入验证码")
    private String code;

    @ApiModelProperty(value = "新密码", required = true)
    @NotBlank(message = "请输入新密码")
    @Size(min = 6, message = "新密码长度为6-18位")
    @Size(max = 18, message = "新密码长度为6-18位")
    private String password;

    @ApiModelProperty("身份类型 1-消费者 2-管理员")
    private Integer identityType;

}
