package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 保存小程序用户信息
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Data
@ApiModel(value = "MiniUserSavePO", description = "保存小程序用户信息")
public class MiniUserSavePO {

    @ApiModelProperty("昵称")
    @NotBlank(message = "昵称不能为空")
    @Size(max = 20, message = "昵称不能超过20个字符")
    private String nickName;

    @ApiModelProperty("微信头像")
    @NotBlank(message = "请上传微信头像")
    private String avatarUrl;


}
