package com.hishop.wine.model.po.miniApp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @date 2023/7/25
 */
@Data
@ApiModel(value = "MiniEncryptDataPO", description = "小程序解密参数")
public class MiniEncryptDataPO {

    private String code;
    @ApiModelProperty(value = "iv")
    @NotEmpty(message = "iv不能为空")
    private String iv;
    @ApiModelProperty(value = "encryptedData")
    @NotEmpty(message = "encryptedData不能为空")
    private String encryptedData;

}
