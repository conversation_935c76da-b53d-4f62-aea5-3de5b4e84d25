package com.hishop.wine.model.po.storebar;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * 底部导航查询PO
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "StoreBarQueryPO", description = "底部导航查询po")
public class StoreBarQueryPO {

    @ApiModelProperty(value = "系统类型(扫码营销：scan_marketing,粉丝俱乐部：fans_club)")
    @NotEmpty(message = "系统类型不能为空")
    private String moduleCode;

    @ApiModelProperty(value = "小程序appId")
    @NotEmpty(message = "小程序appId")
    private String appId;
}
