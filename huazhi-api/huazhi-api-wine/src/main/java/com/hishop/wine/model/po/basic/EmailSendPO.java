package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 发送邮件参数
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "SendEmailPO", description = "发送邮件参数")
public class EmailSendPO {

    @ApiModelProperty(value = "邮件", required = true)
    @NotBlank(message = "请输入邮箱")
    @Email(message = "邮箱格式不正确")
    private String email;

}
