package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.vo.address.BaseAddressVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 基础地址库
 *
 * <AUTHOR>
 * @date : 2023/8/16
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-baseAddress",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/baseAddress")
public interface BaseAddressFeign {

    /**
     * 获取地址详情
     *
     * @param id 地址id
     * @return 地址详情
     */
    @GetMapping("/pc/detail")
    ResponseBean<BaseAddressVO> detail(@RequestParam(name = "id") Long id);

}
