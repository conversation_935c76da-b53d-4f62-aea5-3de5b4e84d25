package com.hishop.wine.model.po.login;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 小程序授权登录参数
 *
 * <AUTHOR>
 * @date : 2023/6/16
 */
@Data
@ApiModel("小程序微信授权登录参数")
public class UserMiniAuthLoginPO extends UserBaseLoginPO {

    @ApiModelProperty(value = "微信code", required = true)
    @NotBlank(message = "请传入微信code")
    private String code;

    @ApiModelProperty(value = "微信mobileCode", required = true)
    @NotBlank(message = "请传入mobileCode")
    private String mobileCode;

    @ApiModelProperty("邀请人")
    private Long inviterUserId;

    @ApiModelProperty("注册渠道")
    private String registerChannel;

    @ApiModelProperty("注册业务码")
    private String registerBizCode;

    @ApiModelProperty(value = "testPhone", required = true)
    private String testPhone;

}
