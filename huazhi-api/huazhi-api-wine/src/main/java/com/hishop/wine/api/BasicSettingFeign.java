package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.setting.AbstractSetting;
import com.hishop.wine.enums.BasicSettingEnum;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * 公共设置表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */
@FeignClient(name = "basic-system",
        contextId = "hishop-wine-basicSetting",
        url = "${feign.url.basic-system:basic-system}",
        path = "/wine/basicSetting")
public interface BasicSettingFeign {

    /**
     * 获取公共配置
     *
     * @param settingEnum 配置枚举
     * @return 公共配置
     */
    @GetMapping("/getSetting")
    ResponseBean<Object> getSetting(@RequestParam BasicSettingEnum settingEnum);
}