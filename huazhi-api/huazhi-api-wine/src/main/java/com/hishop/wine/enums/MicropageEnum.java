package com.hishop.wine.enums;

import lombok.Getter;

import java.util.Arrays;

public class MicropageEnum {
    public enum MicropageStatus {

        all(0,"全部"),

        draft(1,"草稿"),

        release(2,"上线");
        private final Integer code;
        private final String desc;

        public Integer getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
        MicropageStatus(Integer code, String desc) {
            this.code = code;
            this.desc = desc;
        }
        public static String getDesc(Integer code) {
            return Arrays.stream(values())
                    .filter(e -> e.getCode().equals(code))
                    .findFirst()
                    .map(MicropageEnum.MicropageStatus::getDesc)
                    .orElse("");
        }
    }

    public enum Category {

        /**
         * 未分组
         */
        UNGROUPED(0L, "未分组"),

        /**
         * 全部
         */
        ALL(-1L, "全部"),

        /**
         * 草稿箱
         */
        DRAFT(-2L, "草稿箱");


        /**
         * 值
         */
        @Getter
        private final Long value;

        @Getter
        private final String name;

        Category(Long value, String name) {
            this.value = value;
            this.name = name;
        }
    }
}
