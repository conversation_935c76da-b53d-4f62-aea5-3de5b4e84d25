package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderRequest extends BaseRequest {
    @SerializedName("transaction_id")
    private String transactionId; // 微信支付单号

    @SerializedName("merchant_id")
    private String merchantId; // 商户号

    @SerializedName("sub_merchant_id")
    private String subMerchantId; // 二级商户号

    @SerializedName("merchant_trade_no")
    private String merchantTradeNo; // 商户内部订单号
}
