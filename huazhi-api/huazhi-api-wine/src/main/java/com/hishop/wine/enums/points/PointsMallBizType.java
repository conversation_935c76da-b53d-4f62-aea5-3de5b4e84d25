package com.hishop.wine.enums.points;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 积分商城涉及积分业务的业务类型
 * <AUTHOR>
 * @date 2023/8/17
 */
public enum PointsMallBizType implements BizType {

    // 异常这个是抽奖或其他业务扣减积分的时候，由于系统异常，前面的积分已经扣了，但是没有得到正常的响应结果，需要把扣减的归还
    EXCEPTION(10, "异常返还", MemberPointsEnum.ModifiedType.INCREASE),
    NEW_MEMBER(11, "成为新会员", MemberPointsEnum.ModifiedType.INCREASE),
    PLAT_INCREASE(12, "平台增加", MemberPointsEnum.ModifiedType.INCREASE),
    POINTS_EXCHANGE_RETURN(13, "积分兑换返还", MemberPointsEnum.ModifiedType.INCREASE),
    RAFFLE_REWARD(14, "抽奖活动奖励", MemberPointsEnum.ModifiedType.INCREASE),


    PLAT_DECREASE(-11, "平台减少", MemberPointsEnum.ModifiedType.DECREASE),
    POINTS_EXCHANGE(-12, "积分兑换", MemberPointsEnum.ModifiedType.DECREASE),
    RAFFLE(-13, "参与抽奖活动", MemberPointsEnum.ModifiedType.DECREASE),

    CLEAR(0, "过期清零", MemberPointsEnum.ModifiedType.CLEAR),
            ;

    private final Integer code;
    private final String desc;
    private final MemberPointsEnum.ModifiedType parentType;

    PointsMallBizType(Integer code, String desc, MemberPointsEnum.ModifiedType parentType) {
        this.code = code;
        this.desc = desc;
        this.parentType = parentType;
    }

    @Override
    public String moduleCode() {
        return "fans_club";
    }

    @Override
    public Integer getCode() {
        return code;
    }

    @Override
    public String getDesc() {
        return desc;
    }

    @Override
    public MemberPointsEnum.ModifiedType getParentType() {
        return parentType;
    }

    @Override
    public String getDesc(Integer code) {
        return Arrays.stream(values())
                .filter(e -> e.getCode().equals(code))
                .findFirst()
                .map(PointsMallBizType::getDesc)
                .orElse("");
    }

    public static boolean isLegal(Integer code) {
        return Arrays.stream(values())
                .anyMatch(e -> e.getCode().equals(code));
    }

    public static List<PointsMallBizType> getBizType(MemberPointsEnum.ModifiedType parentType) {
        return Optional.ofNullable(parentType)
                .map(type -> Arrays.stream(PointsMallBizType.values())
                        .filter(e -> e.getParentType().equals(type))
                        .filter(e -> !PointsMallBizType.EXCEPTION.equals(e))
                        .collect(Collectors.toList()))
                .orElse(Arrays.asList(PointsMallBizType.values()));
    }

}
