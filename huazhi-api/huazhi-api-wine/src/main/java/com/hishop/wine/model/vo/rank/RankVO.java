package com.hishop.wine.model.vo.rank;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.Date;

/**   
 * 头衔表 返回对象
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Data
@ApiModel(value = "RankVO", description = "头衔表返回对象")
public class RankVO {

    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "头衔名称")
	private String rankName;
    
    @ApiModelProperty(value = "模块编码")
	private String moduleCode;
    
    @ApiModelProperty(value = "状态 0：禁用  1：正常")
	private Boolean status;
    
    @ApiModelProperty(value = "是否删除 0:未删除 1:删除")
	private Boolean izDelete;
    
    @ApiModelProperty(value = "创建者ID")
	private Long createBy;
    
    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date createTime;
    
    @ApiModelProperty(value = "更新者ID")
	private Long updateBy;
    
    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
	private Date updateTime;

    @ApiModelProperty(value = "关联用户数")
    private Integer relateUserNum;
    

}
