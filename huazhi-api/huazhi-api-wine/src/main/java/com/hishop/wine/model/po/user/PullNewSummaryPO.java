package com.hishop.wine.model.po.user;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 拉新汇总po
 *
 * <AUTHOR>
 * @date : 2023/8/28
 */
@Data
@ApiModel(value = "PullNewSummaryPO", description = "拉新汇总po")
public class PullNewSummaryPO {

    @ApiModelProperty(value = "注册渠道", required = true)
    @NotNull(message = "注册渠道不能为空")
    private String registerChannel;

    @ApiModelProperty(value = "邀请人id的集合")
    private List<Long> inviterUserIdList;

    @ApiModelProperty("注册业务码")
    private List<String> bizCodeList;

    @ApiModelProperty("合计维度 1-按邀请人 2-按业务码 3-综合")
    @NotNull(message = "合计维度不能为空")
    private Integer totalDimension;

    @ApiModelProperty(value = "注册开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date registerStartTime;

}
