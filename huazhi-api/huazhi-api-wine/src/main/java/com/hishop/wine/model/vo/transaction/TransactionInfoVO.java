package com.hishop.wine.model.vo.transaction;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;


/**
 * 交易明细vo
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionInfoVO", description = "交易明细vo")
public class TransactionInfoVO {

    @ApiModelProperty("交易流水id")
    private Long transactionId;

    @ApiModelProperty("交易流水号")
    private String transactionNo;

    @ApiModelProperty("业务编码")
    private String bizCode;

    @ApiModelProperty("交易方式")
    private String transactionMethod;

    @ApiModelProperty("交易方式名称")
    private String transactionMethodName;

    @ApiModelProperty("交易金额")
    private BigDecimal amount;

    @ApiModelProperty("交易时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @ApiModelProperty("创建人名称")
    private String createName;

}
