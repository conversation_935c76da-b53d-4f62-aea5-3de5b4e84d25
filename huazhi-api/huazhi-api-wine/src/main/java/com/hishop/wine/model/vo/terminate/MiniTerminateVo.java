package com.hishop.wine.model.vo.terminate;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 小程序端门店信息
 * @author: chenzw
 * @date: 2024/7/10 16:34
 */
@Data
@ApiModel(value = "MiniTerminateVo", description = "小程序端门店信息")
public class MiniTerminateVo {

    @ApiModelProperty(value = "门店编码")
    private String code;

    @ApiModelProperty(value = "门店名称")
    private String name;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县名称")
    private String districtName;

    @ApiModelProperty(value = "详细地址")
    private String address;

}
