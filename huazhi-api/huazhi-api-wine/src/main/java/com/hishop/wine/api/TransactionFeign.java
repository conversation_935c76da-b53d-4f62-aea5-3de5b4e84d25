package com.hishop.wine.api;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionQueryPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.vo.transaction.TransactionEntPayVO;
import com.hishop.wine.model.vo.transaction.TransactionInfoVO;
import com.hishop.wine.model.vo.transaction.TransactionPayVO;
import com.hishop.wine.model.vo.transaction.TransactionRefundVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;

/**
 * 交易流水表 微服务接口
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-transaction", url = "${feign.url.basic-system:basic-system}", path = "/wine/transaction")
public interface TransactionFeign {

    /**
     * 发起支付
     *
     * @param payPO 发起支付参数
     * @return 支付参数返回值
     */
    @PostMapping("/pay")
    ResponseBean<TransactionPayVO> pay(@RequestBody @Valid TransactionPayPO payPO);

    /**
     * 判断是否支付中
     * @param payPO
     * @return
     */
    @PostMapping("/queryPaying")
    ResponseBean<Boolean> queryPaying(@RequestBody @Valid TransactionQueryPO payPO);

    /**
     * 发起退款
     *
     * @param refundPO 发起退款参数
     * @return 发起退款返回值
     */
    @PostMapping("/refund")
    ResponseBean<TransactionRefundVO> refund(@RequestBody @Valid TransactionRefundPO refundPO);

    /**
     * 企业付款到零钱
     *
     * @param entPayPO 企业付款到零钱参数
     * @return 企业付款到零钱返回值
     */
    @PostMapping("/entPay")
    ResponseBean<TransactionEntPayVO> entPay(@RequestBody @Valid TransactionEntPayPO entPayPO);

    /**
     * 交易查询
     *
     * @param queryPO 查询参数
     * @return 交易流水
     */
    @PostMapping("/query")
    ResponseBean<PageResult<TransactionInfoVO>> queryTransaction(@RequestBody @Valid TransactionQueryPO queryPO);

    /**
     * 获取商户id
     * @param id
     * @return
     */
    @GetMapping("/getMchId")
    ResponseBean<String> getMchId(@RequestParam("id")Long id);

}