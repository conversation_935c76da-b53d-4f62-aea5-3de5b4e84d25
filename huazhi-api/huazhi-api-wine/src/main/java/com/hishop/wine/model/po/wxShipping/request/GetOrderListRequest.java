package com.hishop.wine.model.po.wxShipping.request;

import com.google.gson.annotations.SerializedName;
import com.hishop.wine.model.po.wxShipping.TimeRange;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description: 查询订单列表请求
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetOrderListRequest extends BaseRequest {
    @SerializedName("pay_time_range")
    private TimeRange payTimeRange;

    @SerializedName("last_index")
    private String lastIndex;

    @SerializedName("page_size")
    private int pageSize;

    @SerializedName("order_state")
    private Integer orderState;

    @SerializedName("openid")
    private String openid;
}
