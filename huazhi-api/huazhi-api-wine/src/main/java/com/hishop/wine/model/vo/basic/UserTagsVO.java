package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 用户标签表 返回对象
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Data
@ApiModel(value = "UserTagsVO", description = "用户标签表返回对象")
public class UserTagsVO {
	
    @ApiModelProperty(value = "主键id")
	private Long id;
    
    @ApiModelProperty(value = "用户ID")
	private Long userId;
    
    @ApiModelProperty(value = "标签ID")
	private Long tagId;
    
    @ApiModelProperty(value = "标签名称")
	private String tagName;
    
}
