package com.hishop.wine.enums.bill;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 交易类型
 */
@Getter
@AllArgsConstructor
public enum TransactionType {

    /**
     * 封坛酒订单支付
     */
    SEAL_WINE_ORDER("SEAL_WINE_ORDER", "封坛酒订单支付"),
    /**
     * 取酒订单支付
     */
    EXTRACTION_WINE_ORDER("EXTRACTION_WINE_ORDER", "取酒订单支付"),
    /**
     * 保管费支付
     */
    STORAGE_FEE("STORAGE_FEE", "保管费支付"),
    ;

    @EnumValue
    private final String value;

    private final String desc;

    // 静态方法，根据字符串查找对应的枚举值
    public static String fromStringDesc(String value) {
        for (TransactionType method : TransactionType.values()) {
            if (method.value.equals(value)) {
                return method.getDesc();
            }
        }
        // 如果没有匹配的枚举值，你可以选择抛出异常或者返回一个默认值，这里返回null作为示例
        return null;
    }
}
