package com.hishop.wine.api;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.dto.BizUserCodeByPhoneDto;
import com.hishop.wine.model.po.basic.UserCombineQryPO;
import com.hishop.wine.model.po.user.CheckRolePO;
import com.hishop.wine.model.po.user.PullNewSummaryPO;
import com.hishop.wine.model.po.user.UserCreatePO;
import com.hishop.wine.model.vo.basic.UserVO;
import com.hishop.wine.model.vo.user.PullNewSummaryVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

import javax.validation.Valid;
import java.util.List;

/**
 * 用户表 微服务接口
 *
 * @author: Hu<PERSON><PERSON><PERSON>
 * @date: 2023-06-17
 */
@FeignClient(name = "basic-system", contextId = "hishop-wine-user", url = "${feign.url.basic-system:basic-system}", path = "/wine/user")
public interface UserFeign {

    @PostMapping("/fegin/listUserByIds")
    ResponseBean<List<UserVO>> listUserByIds(@RequestBody List<Long> userIds);

    @GetMapping("/fegin/userById")
    ResponseBean<UserVO> userById(@RequestParam("userId") Long userId);

    /**
     * 根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户
     *
     * <AUTHOR>
     * @date 2023/6/30
     */
    @PostMapping("/combine/qryUserId")
    ResponseBean<List<Long>> qryUserId(@RequestBody UserCombineQryPO qryPo);

    /**
     * 检测是否有指定角色
     *
     * @param checkRolePO 查询参数
     * @return true-有 false-无
     */
    @PostMapping("/pc/checkRole")
    ResponseBean<Boolean> checkRole(@RequestBody @Valid CheckRolePO checkRolePO);

    /**
     * 获取拉新统计数据
     *
     * @param pullNewSummaryPO 查询参数
     * @return 拉新统计数据
     */
    @PostMapping("/pc/getPullNewSummary")
    ResponseBean<List<PullNewSummaryVO>> getPullNewSummary(@RequestBody @Valid PullNewSummaryPO pullNewSummaryPO);

    @PostMapping("/updateBizUserCodeByPhone")
    ResponseBean<Void> updateBizUserCodeByPhone(@RequestBody @Valid BizUserCodeByPhoneDto dto);

    @PostMapping("/fegin/createUser")
    ResponseBean<UserVO> createUser(@RequestBody @Valid UserCreatePO userCreatePO);
}