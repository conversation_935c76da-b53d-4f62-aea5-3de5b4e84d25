package com.hishop.wine.model.po.basic;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

/**
 * 角色表 新增入参对象
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@ApiModel(value = "RoleCreatePO", description = "角色表新增入参对象")
public class RoleCreatePO {

    @ApiModelProperty(value = "角色名称", required = true)
    @NotBlank(message = "角色名称不能为空")
    @Size(min = 2, message = "角色名称请输入2-6位")
    @Size(max = 6, message = "角色名称请输入2-6位")
    private String name;

    @ApiModelProperty("默认请求地址")
    private String defaultUrl;

    @ApiModelProperty(value = "资源id的集合", required = true)
    @NotNull(message = "请至少选择一个资源")
    @Size(min = 1, message = "请至少选择一个资源")
    private List<Long> resourceIds;

}
