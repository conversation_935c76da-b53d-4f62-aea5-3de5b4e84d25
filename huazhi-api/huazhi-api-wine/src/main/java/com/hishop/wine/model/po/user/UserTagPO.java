package com.hishop.wine.model.po.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 用户打标
 *
 * <AUTHOR>
 * @date : 2023/9/13
 */
@Data
@ApiModel(value = "UserTagPO", description = "用户打标入参")
public class UserTagPO {

    @ApiModelProperty(value = "用户ID")
    @NotNull(message = "用户ID不能为空")
    private Long userId;

    @ApiModelProperty(value = "标签ID列表")
    @NotNull(message = "标签ID列表不能为空")
    @Size(min = 1, message = "标签ID列表不能为空")
    private List<Long> tagIds;

    public static UserTagPO of(Long userId, List<Long> tagIds) {
        UserTagPO userTagPO = new UserTagPO();
        userTagPO.setUserId(userId);
        userTagPO.setTagIds(tagIds);
        return userTagPO;
    }
}
