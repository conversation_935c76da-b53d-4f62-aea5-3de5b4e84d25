package com.hishop.wine.model.po.miniApp.uploadShippingInfo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @Date 2024/05/06/ $
 * @description:
 */
@Data
@ApiModel(value = "OrderKeyPo", description = "订单，需要上传物流信息的订单")
public class OrderKeyPo {

    @ApiModelProperty(value = "订单单号类型，用于确认需要上传详情的订单。枚举值1，使用下单商户号和商户侧单号；枚举值2，使用微信支付单号。", required = true)
    @NotNull(message = "订单单号类型不能为空")
    private Integer order_number_type;

    @ApiModelProperty(value = "原支付交易对应的微信订单号", required = false)
    private String transaction_id;

    @ApiModelProperty(value = "支付下单商户的商户号，由微信支付生成并下发", required = false)
    private String mchid;

    @ApiModelProperty(value = "商户系统内部订单号，只能是数字、大小写字母`_-*`且在同一个商户号下唯一", required = false)
    private String out_trade_no;
}
