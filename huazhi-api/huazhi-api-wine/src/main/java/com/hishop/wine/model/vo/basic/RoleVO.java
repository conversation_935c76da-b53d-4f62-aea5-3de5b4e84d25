package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * 角色表 返回对象
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */

@Data
@ApiModel(value = "RoleVO", description = "角色表返回对象")
public class RoleVO {

    @ApiModelProperty("角色id")
    private Long id;

    @ApiModelProperty(value = "角色名称")
    private String name;

    @ApiModelProperty("默认请求地址")
    private String defaultUrl;

    @ApiModelProperty(value = "资源id的集合")
    private List<Long> resourceIds;

}
