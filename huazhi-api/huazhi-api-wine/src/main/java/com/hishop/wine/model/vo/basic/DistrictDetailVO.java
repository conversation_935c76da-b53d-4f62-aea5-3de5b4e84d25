package com.hishop.wine.model.vo.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 地区详细信息
 *
 * <AUTHOR>
 * @date : 2023/6/29
 */
@Data
@ApiModel(value = "DistrictDetailVO", description = "区域详情对象")
public class DistrictDetailVO {

    @ApiModelProperty("区域等级")
    private Integer level;

    @ApiModelProperty(value = "省ID")
    private Integer provinceId;

    @ApiModelProperty("省份名称")
    private String province;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty("城市名称")
    private String city;

    @ApiModelProperty(value = "区ID")
    private Integer areaId;

    @ApiModelProperty("区名称")
    private String area;

    @ApiModelProperty("街道id")
    private Integer streetId;

    @ApiModelProperty("街道名称")
    private String street;

}
