package com.hishop.wine.model.vo.basic;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.wine.enums.DistrictLevelEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 地区树节点
 *
 * <AUTHOR>
 * @date : 2023/5/29
 */
@Data
@ApiModel(value = "DistrictVO", description = "行政区域返回参数")
public class DistrictVO implements Serializable {

    @ApiModelProperty("主键id")
    private Integer id;

    @ApiModelProperty("地区名称")
    private String name;
    @ApiModelProperty("大区名称")
    private String regionName;
    @ApiModelProperty("父级id全路径")
    private String parentIds;
    @ApiModelProperty("上级地区id")
    private Integer parentId;

    @ApiModelProperty("全路径")
    private String fullName;

    @ApiModelProperty("是否有下级")
    private Boolean hasChild;
    @ApiModelProperty("经度")
    private BigDecimal lng;
    @ApiModelProperty("纬度")
    private BigDecimal lat;
    @ApiModelProperty("等级")
    private Integer level;
    @ApiModelProperty("下级地区")
    private List<DistrictVO> childList;

    /**
     * 构建全部省/市/区
     *
     * @param sameLevelDistrict 同级地区
     * @return 全部省/市/区
     */
    public static DistrictVO ofWhole(DistrictVO sameLevelDistrict) {
        DistrictVO district = BeanUtil.copyProperties(sameLevelDistrict, DistrictVO.class, "childList");
        district.setName("全部" + DistrictLevelEnum.getNameByLevel(sameLevelDistrict.getLevel()));
        String[] fullNameArr = sameLevelDistrict.getFullName().split(StrUtil.COMMA);
        fullNameArr[fullNameArr.length - 1] = district.getName();
        district.setFullName(StrUtil.join(StrUtil.COMMA, fullNameArr));
        district.setId(sameLevelDistrict.getLevel() * -1);
        return district;
    }

}
