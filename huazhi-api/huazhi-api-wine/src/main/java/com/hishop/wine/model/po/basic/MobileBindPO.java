package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 绑定手机号参数
 *
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Data
@ApiModel(value = "MobileBindPO", description = "绑定手机号参数")
public class MobileBindPO {

    @ApiModelProperty(value = "手机号", required = true)
    @NotBlank(message = "请输入手机号")
    @Pattern(regexp = "^1[3|4|5|6|7|8|9][0-9]\\d{8}$", message = "手机号格式不正确")
    private String mobile;

    @ApiModelProperty(value = "验证码", required = true)
    @NotBlank(message = "请输入验证码")
    private String code;

    @ApiModelProperty("登录密码, 修改手机号时必填")
    private String password;

}
