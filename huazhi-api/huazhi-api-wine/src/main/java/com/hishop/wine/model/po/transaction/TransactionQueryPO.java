package com.hishop.wine.model.po.transaction;

import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.enums.TransactionEnum;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;


/**
 * 交易明细查询
 *
 * <AUTHOR>
 * @date : 2023/6/28
 */
@Data
@ApiModel(value = "TransactionQueryPO", description = "交易名称查询")
public class TransactionQueryPO extends PageParam {

    @ApiModelProperty(value = "业务类型")
    private TransactionEnum.BizTypeEnum bizType;

    @ApiModelProperty(value = "业务端业务编码(传订单编码)")
    private String bizCode;

    @ApiModelProperty(value = "状态 1-待确认 2-交易成功 3-交易失败")
    private TransactionEnum.Status status;

}
