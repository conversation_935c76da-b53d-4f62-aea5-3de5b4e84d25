package com.hishop.wine.model.vo.payment;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 支付设置 返回对象
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
@Data
@ApiModel(value = "PaymentSettingVO", description = "支付设置返回对象")
public class PaymentSettingVO {

    @ApiModelProperty("支付设置id")
    private Long id;

    @ApiModelProperty(value = "支付类型 WX_PAY-微信支付 ALI_PAY-支付宝支付 OFFLINE-线下支付")
    private String paymentType;

    @ApiModelProperty("支付类型名称")
    private String paymentTypeName;

    @ApiModelProperty(value = "支付方式名称")
    private String paymentName;

    @ApiModelProperty("关联端口")
    private String relateMiniNames;

    @ApiModelProperty("是否特殊收款公司")
    private Boolean izSpecial;

    @ApiModelProperty("收款公司编码")
    private String specialCode;
}
