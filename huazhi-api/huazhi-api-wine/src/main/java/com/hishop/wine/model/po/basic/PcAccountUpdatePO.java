package com.hishop.wine.model.po.basic;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 更新账号信息参数
 *
 * <AUTHOR>
 * @date : 2023/7/11
 */
@Data
@ApiModel(value = "PcAccountUpdatePO", description = "更新账号信息参数")
public class PcAccountUpdatePO {

    @ApiModelProperty("头像")
    private String icon;

    @ApiModelProperty(value = "姓名", required = true)
    @NotBlank(message = "姓名不能为空")
    private String realName;

    @ApiModelProperty(value = "角色id", required = true)
    @NotNull(message = "请选择角色权限")
    private Long roleId;

}
