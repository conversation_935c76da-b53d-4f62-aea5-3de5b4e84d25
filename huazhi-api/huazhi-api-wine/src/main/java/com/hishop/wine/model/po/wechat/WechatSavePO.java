package com.hishop.wine.model.po.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2023/6/25
 */
@ApiModel(value = "WechatSavePO", description = "微信二维码保存入参对象")
@Data
public class WechatSavePO {

    @ApiModelProperty(value = "二维码类型。1：小程序码；2：公众号二维码")
    @NotNull(message = "二维码类型不能为空")
    private Integer codeType;
    @ApiModelProperty(value = "二维码来源。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部")
    @NotNull(message = "二维码来源不能为空")
    private String codeFrom;
    @ApiModelProperty(value = "二维码唯一标识，通过该字段匹配获取")
    @NotNull(message = "二维码唯一标识不能为空")
    private String codeKey;
    @ApiModelProperty(value = "二维码描述")
    private String codeDesc;
    @ApiModelProperty(value = "二维码地址。云服务器地址，存相对路径")
    @NotNull(message = "二维码地址不能为空")
    private String codeUrl;
    @ApiModelProperty(value = "完整的小程序链接(可带参数)")
    private String page;

}
