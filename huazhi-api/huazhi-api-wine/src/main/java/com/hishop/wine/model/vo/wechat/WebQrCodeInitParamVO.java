package com.hishop.wine.model.vo.wechat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信网页应用二维码初始化参数
 *
 * <AUTHOR>
 * @date : 2023/7/10
 */
@Data
@ApiModel(value = "微信二维码对象")
public class WebQrCodeInitParamVO {

    @ApiModelProperty(value = "网页应用id")
    private String appId;

    @ApiModelProperty(value = "应用授权作用域")
    private String scope;

    @ApiModelProperty(value = "用户回调校验")
    private String state;

    public static WebQrCodeInitParamVO of(String appId, String scope, String state) {
        WebQrCodeInitParamVO webQrCodeInitParamVO = new WebQrCodeInitParamVO();
        webQrCodeInitParamVO.setAppId(appId);
        webQrCodeInitParamVO.setScope(scope);
        webQrCodeInitParamVO.setState(state);
        return webQrCodeInitParamVO;
    }

}
