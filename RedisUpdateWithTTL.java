/**
 * Redis更新值时保持过期时间的工具方法示例
 */
public class RedisUpdateWithTTL {
    
    /**
     * 方案2：获取剩余过期时间并重新设置
     */
    public void updateValueKeepTTL(String key, Object newValue) {
        // 获取key的剩余过期时间（秒）
        Long ttl = RedisUtil.getExpire(key);
        
        if (ttl != null && ttl > 0) {
            // 如果key存在且有过期时间，更新值并保持剩余过期时间
            RedisUtil.set(key, newValue, ttl.intValue());
        } else if (ttl != null && ttl == -1) {
            // key存在但没有过期时间（永不过期）
            RedisUtil.set(key, newValue);
        } else {
            // key不存在，设置默认过期时间
            RedisUtil.set(key, newValue, 30 * 60); // 30分钟
        }
    }
    
    /**
     * 方案3：使用Redis的SET命令保持TTL（如果RedisUtil支持）
     */
    public void updateValueKeepTTLNative(String key, Object newValue) {
        // 如果RedisUtil支持KEEPTTL选项
        // RedisUtil.set(key, newValue, RedisUtil.KEEP_TTL);
        
        // 或者直接使用Redis命令
        // redisTemplate.execute((RedisCallback<String>) connection -> {
        //     connection.set(key.getBytes(), serialize(newValue), Expiration.keepTtl());
        //     return null;
        // });
    }
    
    /**
     * 改进后的忘记密码次数统计方法
     */
    public void countForgetPasswordAttempts(String mobile) {
        String key = BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_NUM_KEY + mobile;
        Integer num = RedisUtil.get(key);
        
        if (num == null) {
            // 第一次尝试，设置初始值和过期时间
            RedisUtil.set(key, 1, 30 * 60);
        } else {
            // 后续尝试，保持原有过期时间
            Long ttl = RedisUtil.getExpire(key);
            if (ttl != null && ttl > 0) {
                RedisUtil.set(key, num + 1, ttl.intValue());
            } else {
                // 如果获取TTL失败，重新设置默认过期时间
                RedisUtil.set(key, num + 1, 30 * 60);
            }
        }
    }
    
    /**
     * 方案4：使用Redis的INCR命令（推荐用于计数场景）
     */
    public void countWithIncr(String mobile) {
        String key = BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_NUM_KEY + mobile;
        
        // 使用INCR命令自增
        Long count = RedisUtil.incr(key);
        
        if (count == 1) {
            // 第一次设置时添加过期时间
            RedisUtil.expire(key, 30 * 60);
        }
        // 后续INCR操作不会影响过期时间
    }
}
