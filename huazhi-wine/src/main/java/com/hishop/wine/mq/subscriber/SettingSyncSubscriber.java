package com.hishop.wine.mq.subscriber;

import cn.hutool.core.util.ObjectUtil;
import com.hishop.mq.api.MQConsumerListener;
import com.hishop.mq.api.MQSubscriber;
import com.hishop.setting.SettingSyncBean;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 全局修改积分名称 消费者
 *
 * <AUTHOR>
 * @date : 2023/7/24
 */
@Component
@Slf4j
@MQConsumerListener
public class SettingSyncSubscriber {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    @MQSubscriber(topicName = RocketMqConstants.SYNC_SETTING_TOPIC)
    private void settingSyncListener(SettingSyncBean settingSyncBean) {
        try {
            log.info("======> 同步配置： {}", settingSyncBean);
            BasicSettingEnum settingEnum = BasicSettingEnum.valueOf(settingSyncBean.getSettingName());
            if (ObjectUtil.isNull(settingEnum) || !settingEnum.getAcceptSync()) {
                log.info("======> 丢弃同步: {}", settingSyncBean);
                return;
            }
            basicSettingBiz.saveSetting(settingEnum, settingSyncBean.getSettingValue());
            log.info("======> 同步配置成功： {}", settingSyncBean);
        } catch (Exception e) {
            log.error("同步配置失败", e);
            throw e;
        }
    }
}
