package com.hishop.wine.mq.subscriber;

import com.hishop.mq.api.MQConsumerListener;
import com.hishop.mq.api.MQSubscriber;
import com.hishop.wine.biz.MemberPointsBiz;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.points.ClearExpirePointsPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员积分调整监听
 *
 * <AUTHOR>
 * @date : 2023/6/30
 */
@Component
@Slf4j
@MQConsumerListener
public class MemberPointsSubscriber {

    @Resource
    private MemberPointsBiz memberPointsBiz;

    /**
     * 监听积分变动
     *
     * @param changePointsPO
     */
    @MQSubscriber(topicName = RocketMqConstants.MEMBER_POINTS_CHANGE_TOPIC)
    private void memberPointsChangeListener(ChangePointsPO changePointsPO) {
        try {
            log.info("======> 会员积分调整： {}", changePointsPO);
            memberPointsBiz.change(changePointsPO);
            log.info("======> 会员积分调整成功： {}", changePointsPO.getBizType() + changePointsPO.getBizCode());
        } catch (Exception e) {
            log.error("会员积分调整失败", e);
            throw e;
        }
    }

    /**
     * 监听积分清零
     *
     * @param clearExpirePointsPO
     */
    @MQSubscriber(topicName = RocketMqConstants.POINTS_CLEAR_TOPIC)
    private void clearPointsListener(ClearExpirePointsPO clearExpirePointsPO) {
        try {
            log.info("======> 【积分清零】： {}", clearExpirePointsPO);
            memberPointsBiz.clearExpirePoints(clearExpirePointsPO.getUserList(), clearExpirePointsPO.getDeadlineTime());
            log.info("======> 【积分清零】执行成功： {}", clearExpirePointsPO);
        } catch (Exception e) {
            log.error("【积分清零】执行失败: {}, error: {}", clearExpirePointsPO, e);
            throw e;
        }
    }

    /**
     * 监听积分清零通知
     *
     * @param noticePO
     */
    @MQSubscriber(topicName = RocketMqConstants.POINTS_CLEAR_NOTICE_TOPIC)
    private void clearPointsNoticeListener(ClearExpirePointsPO noticePO) {
        try {
            log.info("======> 【积分清零通知】： {}", noticePO);
            memberPointsBiz.noticeClearPoints(noticePO.getUserList(), noticePO.getDeadlineTime());
            log.info("======> 【积分清零通知】执行成功： {}", noticePO);
        } catch (Exception e) {
            log.error("【积分清零通知】执行失败: {}, error: {}", noticePO, e);
            throw e;
        }
    }
}
