package com.hishop.wine.mq.subscriber;

import cn.hutool.core.bean.BeanUtil;
import com.hishop.mq.api.MQConsumerListener;
import com.hishop.mq.api.MQSubscriber;
import com.hishop.wine.biz.BillBiz;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.model.po.bill.BillPo;
import com.hishop.wine.repository.entity.Bill;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import javax.annotation.Resource;

@Component
@Slf4j
@MQConsumerListener
public class BillSubscriber {

    @Resource
    private BillBiz billBiz;

    @MQSubscriber(topicName = RocketMqConstants.BILL_TOPIC)
    private void saveBillListener(BillPo billPo) {
        try {
            log.info("======> 【保存账单】： {}", billPo);
            Bill bill = BeanUtil.copyProperties(billPo, Bill.class);
            billBiz.billSave(bill);
            log.info("======> 【保存账单】执行成功： {}", billPo);
        } catch (Exception e) {
            log.error("【保存账单】执行失败: {}, error: {}", billPo, e);
            throw e;
        }
    }
}
