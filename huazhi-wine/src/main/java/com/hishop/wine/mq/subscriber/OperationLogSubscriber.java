package com.hishop.wine.mq.subscriber;

import com.hishop.log.model.OperationRecord;
import com.hishop.mq.api.MQConsumerListener;
import com.hishop.mq.api.MQSubscriber;
import com.hishop.wine.biz.SystemLogBiz;
import com.hishop.wine.constants.RocketMqConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 全局操作日志记录
 *
 * <AUTHOR>
 * @date : 2023/7/24
 */
@Component
@Slf4j
@MQConsumerListener
public class OperationLogSubscriber {

    @Resource
    private SystemLogBiz systemLogBiz;


    /**
     * 记录操作日志
     *
     * @param record
     */
    @MQSubscriber(topicName = RocketMqConstants.LOG_OPERATION_TOPIC)
    private void operationLogListener(OperationRecord record) {
        log.info("======> 记录操作日志： {}", record);
        systemLogBiz.createOperationLog(record);
        log.info("======> 记录操作日志成功： {}", record);
    }
}
