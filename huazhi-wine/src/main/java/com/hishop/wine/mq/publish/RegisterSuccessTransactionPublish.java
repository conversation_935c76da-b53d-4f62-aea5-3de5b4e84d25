package com.hishop.wine.mq.publish;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.mq.rocketmq.AbstractTransactionPublish;
import com.hishop.wine.constants.RocketMqConstants;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.service.IdentityService;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.client.producer.LocalTransactionState;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 注册成功事务消息
 *
 * <AUTHOR>
 * @date : 2023/6/29
 */
@Component
@Slf4j
public class RegisterSuccessTransactionPublish extends AbstractTransactionPublish<Long> {

    @Resource
    private IdentityService identityService;

    public RegisterSuccessTransactionPublish() {
        super(RocketMqConstants.REGISTER_SUCCESS_TOPIC);
    }

    @Override
    protected boolean doExecuteLocalTransaction(Long identityId) {
        return Boolean.FALSE;
    }

    @Override
    protected LocalTransactionState doCheckLocalTransaction(Long identityId) {
        long count = identityService.count(new LambdaQueryWrapper<Identity>().eq(Identity::getId, identityId));
        return count > 0 ? LocalTransactionState.COMMIT_MESSAGE : LocalTransactionState.UNKNOW;
    }
}
