package com.hishop.wine.constants;

/**
 * 产品常量
 *
 * <AUTHOR>
 * @date : 2023/7/4
 */
public interface ProductConstants {


    /**
     * 文件导入临时路径
     */
    String IMPORT_TEMP_PATH = System.getProperty("user.dir") + "/product/import/zip/";

    /**
     * 产品导入模板名称
     */
    String PRODUCT_IMPORT_EXCEL_NAME = "产品导入模板.xlsx";

    /**
     * 产品导入图片路径
     */
    String NFS_PRODUCT_IMPORT_PATH = "hishop/images/product/";

    /**
     * 产品导入结果excel名
     */
    String PRODUCT_IMPORT_RESULT = "hishop/import/result/product/产品导入结果%s.xlsx";

    /**
     * 产品导入结果模板
     */
    String PRODUCT_IMPORT_RESULT_TEMPLATE = "hishop/import/template/product/产品导入结果.xlsx";

}
