package com.hishop.wine.constants;

/**
 * 基础模块缓存常量
 *
 * <AUTHOR>
 * @date : 2023/6/7
 */
public interface BasicCacheConstants {

    /**
     * 地区树cache KEY
     */
    String DISTRICT_TREE_PREFIX = "DISTRICT_TREE_LIST:%s";

    /**
     * 地区列表cache KEY
     */
    String DISTRICT_LIST_PREFIX = "DISTRICT_LIST:%s";

    /**
     * 大区列表cache
     */
    String REGION_LIST_CACHE = "REGION_LIST_CACHE";

    /**
     * 大区列表cache
     */
    String REGION_TREE_CACHE = "REGION_TREE_CACHE";

    /**
     * 区域等级缓存
     */
    String DISTRICT_LIST_LEVEL_PREFIX = "DISTRICT_LIST_LEVEL:%s";

    /**
     * 资源树缓存
     */
    String RESOURCE_TREE_CACHE = "RESOURCE_TREE_CACHE";

    /**
     * 页面树缓存
     */
    String PAGES_TREE_CACHE = "PAGES_TREE_CACHE";

    /**
     * 所有角色缓存
     */
    String ROLE_ALL_LIST_CACHE = "ROLE_ALL_LIST_CACHE";

    /**
     * 所有产品分类缓存
     */
    String PRODUCT_CATEGORY_ALL_LIST_CACHE = "PRODUCT_CATEGORY_ALL_LIST_CACHE";

    /**
     * 微信支付参数配置缓存
     */
    String WECHAT_PAY_PARAM_CACHE = "WECHAT_PAY_PARAM_CACHE:%s";

    /**
     * 绑定邮箱Key
     */
    String PC_BIND_EMAIL_KEY = "PC_BIND_EMAIL:%s";

    /**
     * 绑定手机Key
     */
    String PC_BIND_MOBILE_KEY = "PC_BIND_MOBILE:";

    /**
     * 修改密码key
     */
    String PC_UPDATE_PASSWORD_MOBILE_KEY = "PC_UPDATE_PASSWORD_MOBILE:";

    /**
     * 忘记密码key
     */
    String PC_FORGET_PASSWORD_MOBILE_KEY = "PC_FORGET_PASSWORD_MOBILE:";

    /**
     * 小程序手机号登录验证码
     */
    String MINI_LOGIN_MOBILE_KEY = "MINI_LOGIN_MOBILE:";

    /**
     * 小程序链接管理缓存
     */
    String MINI_LINKS_CACHE = "MINI_LINKS_CACHE:%s";

    /**
     * 注册成功处理加锁
     */
    String REGISTER_SUCCESS_HANDLE_LOCK = "REGISTER_SUCCESS_HANDLE_LOCK:%s";

    /**
     * 用户打标加锁
     */
    String USER_TAG_LOCK = "USER_TAG_LOCK:%s";

    /**
     * 物流查询缓存
     */
    String LOGISTICS_QUERY_CACHE = "LOGISTICS_QUERY_CACHE:%s_%s";

    /**
     * 微页面访问统计
     */
    String MICORPAGE_VISTE_CACHE = "MICORPAGE_VISTE_CACHE";

    /**
     * 红包发放加锁
     */
    String GIVE_RED_BAG_LOCK = "GIVE_RED_BAG_LOCK:%s";

    /**
     * 小程序登录后根据wxcode保存手机号码
     */
    String MINI_LOGIN_MOBILE_CODE_PHONE = "MINI_LOGIN_MOBILE_CODE_PHONE:";

    /**
     * 忘记密码验证码次数
     */
    String PC_FORGET_PASSWORD_MOBILE_NUM_KEY = "PC_FORGET_PASSWORD_NUM_MOBILE:";

}
