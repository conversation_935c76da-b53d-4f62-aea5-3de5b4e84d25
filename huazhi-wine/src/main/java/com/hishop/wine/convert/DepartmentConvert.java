package com.hishop.wine.convert;

import com.hishop.wine.model.vo.department.DepartmentVO;
import com.hishop.wine.repository.entity.Department;
import com.hishop.wine.model.po.DepartmentCreatePO;
import com.hishop.wine.model.po.DepartmentUpdatePO;
import com.hishop.wine.model.po.DepartmentQueryPO;
import org.mapstruct.Mapper;
import java.util.List;

/**
 * @Description: 部门表 对象转换类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Mapper(componentModel = "spring")
public interface DepartmentConvert {

    /**
     * PO to entity
     */
    Department convertCreatePOToEntity(DepartmentCreatePO createPO);

    Department convertUpdatePOToEntity(DepartmentUpdatePO updatePO);

    Department convertQueryPOToEntity(DepartmentQueryPO queryPO);

    /**
    * entity to vo
    */
    DepartmentVO convertEntityToVO(Department entity);

    /**
    * entity list to vo list
    */
    List<DepartmentVO> convertEntityToVOList(List<Department> list);

}