package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**   
 * @Description: 部门表 新增入参对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentCreatePO", description = "部门表新增入参对象")
public class DepartmentCreatePO {

    @NotBlank(message = "部门名称不能为空")
    @ApiModelProperty(name = "departmentName" , value = "部门名称")
	private String departmentName;

    @ApiModelProperty(name = "parentId" , value = "上级部门ID")
	private Long parentId;

}
