package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * @description: 销售区域更新请求参数
 * @author: chenzw
 * @date: 2024/7/4 09:33
 */
@Data
@ApiModel(value = "SaleAreaUpdatePo", description = "销售区域更新请求参数")
public class SaleAreaUpdatePo {

    @ApiModelProperty(value = "id")
    @NotNull(message = "id不能为空")
    private Long id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 20, message = "名称长度不能超过20")
    private String name;
}
