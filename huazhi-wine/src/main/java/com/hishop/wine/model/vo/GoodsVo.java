package com.hishop.wine.model.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * 商品信息表(Goods)表实体类
 *
 * <AUTHOR>
 * @since 2024-01-04 14:49:34
 */
@Data
@ApiModel(value = "GoodsVo", description = "商品信息表")
public class GoodsVo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "商品名称")
    private String name;

    @ApiModelProperty(value = "副标题")
    private String subtitle;

    @ApiModelProperty(value = "排序")
    private Integer sort;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "香型")
    private String flavor;

    @ApiModelProperty(value = "度数")
    private String degree;

    @ApiModelProperty(value = "生产年月")
    @JsonFormat(pattern = "yyyy-MM", timezone = "GMT+8")
    private Date produceDate;

    @ApiModelProperty(value = "库存")
    private BigDecimal stock;

    @ApiModelProperty(value = "锁定库存")
    private BigDecimal lockStock;

    @ApiModelProperty(value = "是否支持酒厂储存,0:否 1:是")
    private Boolean izWineryStore;

    @ApiModelProperty(value = "免费存储年份")
    private Integer freeStoreYears;

    @ApiModelProperty(value = "托管定价(元/L/年)")
    private BigDecimal escrowPrice;

    @ApiModelProperty(value = "是否支持客户自存,0:否 1:是")
    private Boolean izCustomerStore;

    @ApiModelProperty(value = "免运费")
    private Boolean izFreeLogistics;

    @ApiModelProperty(value = "统一运费(元)")
    private BigDecimal logisticsPrice;

    @ApiModelProperty(value = "是否支持分批取酒,0:否 1:是")
    private Boolean izSupportBatch;

    @ApiModelProperty(value = "分批取酒最低容量(L)")
    private BigDecimal supportBatchNum;

    @ApiModelProperty(value = "单次最低取酒")
    private BigDecimal singleLowest;

    @ApiModelProperty(value = "是否整倍取酒,0:否 1:是")
    private Boolean izTakeIntegral;

    @ApiModelProperty(value = "商品图片首图")
    private String goodsFirstImg;

    @ApiModelProperty(value = "商品列表图首图")
    private String goodsFirstTabulationImg;

    @ApiModelProperty(value = "封面图")
    private String goodsCoverImg;

    @ApiModelProperty(value = "主图视频", hidden = true)
    private String goodsVideo;

    @ApiModelProperty(value = "规格信息")
    private String specsInfo;

    @ApiModelProperty(value = "商品图片列表")
    private List<String> goodsFirstImgs;

    @ApiModelProperty(value = "商品详情图列表")
    private List<String> goodsFirstTabulationImgs;

    @ApiModelProperty(value = "商品标签")
    private String goodsLabel;

    @ApiModelProperty(value = "销量")
    private Integer salesVolume;

    @ApiModelProperty(value = "商品列表背景图")
    private String goodsBakImg;

    @ApiModelProperty(value = "售卖容量(L)")
    private BigDecimal saleCapacity;

    @ApiModelProperty(value = "单价(元)")
    private BigDecimal unitPrice;

    @ApiModelProperty(value = "划线价(元)")
    private BigDecimal underLinePrice;

    @ApiModelProperty(value = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
