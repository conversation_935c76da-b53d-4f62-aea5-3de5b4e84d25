package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**   
 * 标签表 新增入参对象
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Data
@ApiModel(value = "TagsUpdatePO", description = "编辑标签")
public class TagsUpdatePO {

    @ApiModelProperty("标签Id")
    @NotNull(message = "标签Id不能为空")
    private Long id;

    @ApiModelProperty(value = "标签名称")
	private String tagName;

}
