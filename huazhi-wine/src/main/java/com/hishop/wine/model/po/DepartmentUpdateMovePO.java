package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/5/21 上午9:11
 * @description
 */
@Getter
@Setter
@ApiModel(value = "DepartmentUpdateMovePO", description = "部门移动入参")
public class DepartmentUpdateMovePO {

    @NotNull(message = "请选择当前需要移动的部门")
    @ApiModelProperty(name = "currDepartmentId" , value = "当前要移动的部门id")
    private Long currDepartmentId;


    //@ApiModelProperty(name = "newParentId" , value = "移动目标部门id")
    //private Long newTargetDepartmentId;


    //@NotNull(message = "请选择需要移动目标部门")
    @ApiModelProperty(name = "newParentId" , value = "移动目标部门id(上级部门ID)")
    private Long newTargetParentId;

    @NotEmpty(message = "部门排序不能为空")
    @ApiModelProperty(name = "depSortList" , value = "部门排序")
    private List<DepSort> depSortList;

    @Getter
    @Setter
    public  static  class  DepSort {

        @ApiModelProperty(name = "departmentId" , value = "部门id")
        private Long departmentId;

        @ApiModelProperty(name = "sort" , value = "排序")
        private Integer sort;

    }
}
