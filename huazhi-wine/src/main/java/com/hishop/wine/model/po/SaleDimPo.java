package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 销售维度划分(SaleDim)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:07:46
 */
@Data
@ApiModel(value = "SaleDimPo", description = "销售维度划分")
public class SaleDimPo {

    @ApiModelProperty(value = "主键id")
    @NotNull(message = "id不能为空")
    private Integer id;

    @ApiModelProperty(value = "名称")
    @NotBlank(message = "名称不能为空")
    @Size(max = 20, message = "名称长度不能超过20")
    private String name;
}
