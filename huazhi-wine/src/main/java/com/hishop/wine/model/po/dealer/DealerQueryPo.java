package com.hishop.wine.model.po.dealer;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 经销商表 查询入参对象
 * @author: chenzw
 * @date: 2024-07-04
 */
@Data
@ApiModel(value = "DealerQueryPo", description = "经销商查询入参对象")
public class DealerQueryPo extends PageParam {

    @ApiModelProperty(value = "搜索关键字。支持负责人姓名或手机号码")
	private String dutyNameOrPhone;

    @ApiModelProperty(value = "搜索关键字。支持经销商编码或编号")
    private String dealerCodeOrName;

    @ApiModelProperty(value = "省份ID")
	private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
	private Integer cityId;

    @ApiModelProperty(value = "区县ID")
	private Integer districtId;

    @ApiModelProperty(value = "销售区域ID")
    private Long saleAreaId;

    @ApiModelProperty(value = "业务员")
    private Long businessUserId;

    @ApiModelProperty(value = "状态")
    private Boolean izEnable;

    @ApiModelProperty(value = "销售区域code")
    private String saleAreaCode;
}
