package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 经营趋势
 * @author: chenzw
 * @date: 2024/6/25 15:33
 */
@Data
@ApiModel(value = "IndexBusinessTrendVo", description = "首页经营趋势")
public class IndexBusinessTrendVo {

    @ApiModelProperty(value = "日期")
    private String dateTime;

    @ApiModelProperty(value = "支付金额")
    private BigDecimal payOrderAmount;

    @ApiModelProperty(value = "支付订单数")
    private Integer payOrderCount;

    @ApiModelProperty(value = "支付人数")
    private Integer payUserCount;
}
