package com.hishop.wine.model.po.dealer;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.PhoneUtil;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**   
 * 经销商表 保存入参对象
 * @author: chenzw
 * @date: 2024-07-04
 */

@Data
@ApiModel(value = "DealerSavePo", description = "经销商表保存入参对象")
public class DealerSavePo {
	
    @ApiModelProperty(value = "主键id")
	private Long id;

    @ApiModelProperty(value = "经销商编码")
    @NotBlank(message = "经销商编码不能为空")
    @Size(max = 20, message = "经销商编码长度不能超过20")
    private String dealerCode;

    @ApiModelProperty(value = "经销商名称")
    @NotBlank(message = "经销商名称不能为空")
    @Size(max = 50, message = "经销商名称长度不能超过50")
	private String dealerName;

    @ApiModelProperty(value = "省份ID")
    @NotNull(message = "省份ID不能为空")
	private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    @NotNull(message = "城市ID不能为空")
	private Integer cityId;

    @ApiModelProperty(value = "区县ID")
	private Integer districtId;

    @ApiModelProperty(value = "详细地址")
    @NotBlank(message = "详细地址不能为空")
    @Size(max = 500, message = "详细地址长度不能超过500")
    private String address;

    @ApiModelProperty(value = "备注")
    @Size(max = 20, message = "备注长度不能超过20")
    private String remark;

    @ApiModelProperty(value = "业务员(用户表id)")
    private Long businessUserId;

    @ApiModelProperty(value = "负责人姓名")
    @NotBlank(message = "负责人姓名不能为空")
    @Size(max = 20, message = "负责人姓名长度不能超过20")
    private String dutyName;

    @ApiModelProperty(value = "负责人姓名")
    @NotBlank(message = "手机号不能为空")
    @Size(max = 20, message = "手机号长度不能超过20")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    @Size(max = 100, message = "电子邮箱长度不能超过100")
    private String mail;

    @ApiModelProperty(value = "销售区域列表")
    private List<Long> saleAreaList;

    public void validate() {
        if(!PhoneUtil.isMobile(phone)) {
            Assert.isTrue(false, "手机号格式不正确");
        }

        if (StringUtils.isNotBlank(mail) && !Validator.isEmail(mail)) {
            Assert.isTrue(false, "邮箱格式不正确");
        }

        if (saleAreaList != null && saleAreaList.size() > 20) {
            Assert.isTrue(false, "销售区域最多选择20个");
        }
    }
}
