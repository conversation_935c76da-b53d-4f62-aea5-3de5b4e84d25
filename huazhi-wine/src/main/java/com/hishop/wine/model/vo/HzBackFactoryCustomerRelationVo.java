package com.hishop.wine.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 实体类
 *
 * @Author: snow.xu
 * @since: 2025-03-19
 */

@Data
@ApiModel(value = "HzBackFactoryCustomerRelationVo", description = "回厂游出参")
public class HzBackFactoryCustomerRelationVo implements Serializable {

    private static final long serialVersionUID = 1742354090790L;

    @ApiModelProperty(value = "主键id")
    private Long id;

    @ApiModelProperty(value = "B端客户编码")
    private String bizUserCode;

    @ApiModelProperty(value = "B端客户名称")
    private String bizUserName;

    @ApiModelProperty(value = "B端客户手机号")
    private String bizPhone;

    @ApiModelProperty(value = "C端客户名称")
    private String userName;

    @ApiModelProperty(value = "C端客户手机号")
    private String userPhone;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "更新时间")
    private Date updateTime;
}
