package com.hishop.wine.model.vo.module;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 模块业务app返回对象
 * @author: chenzw
 * @date: 2024/7/24 10:53
 */
@Data
@ApiModel(value = "ModuleBusinessAppVo", description = "模块业务app返回对象")
public class ModuleBusinessAppVo {

    @ApiModelProperty(value = "模块编码")
    private String moduleBusinessCode;

    @ApiModelProperty(value = "模块名称")
    private String moduleName;

    @ApiModelProperty(value = "业务名称")
    private String businessName;

    @ApiModelProperty(value = "小程序app_id, 为空则表示没有关联小程序")
    private String appId;
}
