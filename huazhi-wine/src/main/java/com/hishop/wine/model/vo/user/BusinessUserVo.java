package com.hishop.wine.model.vo.user;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * @description: 业务员信息返回对象
 * @author: chenzw
 * @date: 2024/7/8 09:17
 */
@Data
@ApiModel(value = "BusinessUserVo", description = "业务员信息返回对象")
public class BusinessUserVo {

    @ApiModelProperty(value = "userId")
    private Long userId;

    @ApiModelProperty(value = "姓名")
    private String realName;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "负责销售区域")
    private List<String> saleAreaList;
}
