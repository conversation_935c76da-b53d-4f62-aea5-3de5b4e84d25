package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 应用推荐(AppRecommend)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-25 17:47:02
 */
@Data
@ApiModel(value = "AppRecommendVo", description = "应用推荐")
public class AppRecommendVo {

    @ApiModelProperty(value = "主键")
    private Long id;

    @ApiModelProperty(value = "应用名称")
    private String name;

    @ApiModelProperty(value = "应用描述")
    private String appDescribe;

    @ApiModelProperty(value = "跳转地址")
    private String linkAddress;

}
