package com.hishop.wine.model.po;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/8/11
 */
@Data
@ApiModel(value = "TagsQueryPO", description = "标签查询入参")
public class TagsQueryPO extends PageParam {

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("客户id")
    private Long id;
}
