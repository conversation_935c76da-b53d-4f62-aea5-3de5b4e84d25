package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 销售区域查询请求参数
 * @author: chenzw
 * @date: 2024/7/4 09:33
 */
@Data
@ApiModel(value = "SaleAreaPo", description = "销售区域")
public class SaleAreaQueryPo {

    @ApiModelProperty(value = "销售维度id")
    private Long saleDimId;

    @ApiModelProperty(value = "上级销售区域id")
    private Long parentId;
}
