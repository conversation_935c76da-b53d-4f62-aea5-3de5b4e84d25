package com.hishop.wine.model.po.fileImport;

import com.hishop.common.pojo.page.PageParam;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**   
 * 导出记录表 查询入参对象
 * @author: LiGuoQiang
 * @date: 2023-07-12
 */

@Data
@ApiModel(value = "FileImportRecordQueryPo", description = "导出记录表查询入参对象")
public class FileImportRecordQueryPo extends PageParam {
	
    @ApiModelProperty(value = "导入类型。DEALER：经销商；TERMINATE：终端；QRCODE：二维码； LOGISTICS_CODE:物流码； LOGISTICS_CODE_SCAN：扫码营销物流码")
	private String importType;

    @ApiModelProperty(value = "导入状态。1：导入中；2：导入成功；3：导入失败")
	private Integer importStatus;

    @ApiModelProperty("业务key")
    private String bizCode;

    @ApiModelProperty("产品编码")
    private String productCode;
}
