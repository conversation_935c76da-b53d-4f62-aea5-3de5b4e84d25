package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**   
 * @Description: 部门负责人设置入参对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentEmployeeHeaderPO", description = "部门负责人设置入参对象")
public class DepartmentEmployeeHeaderPO {

    @NotNull(message = "部门名称不能为空")
    @ApiModelProperty(name = "departmentId" , value = "部门ID")
	private Long departmentId;

    @NotNull(message = "用户ID不能为空")
    @ApiModelProperty(name = "userId" , value = "用户ID")
	private Long userId;

}
