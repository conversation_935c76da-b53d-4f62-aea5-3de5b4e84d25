package com.hishop.wine.model.vo.index;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * @description: 首页数据概览返回对象
 * @author: chenzw
 * @date: 2024/6/25 10:19
 */
@Data
@ApiModel(value = "IndexDataOverviewVo", description = "首页数据概览返回对象")
public class IndexDataOverviewVo {

    @ApiModelProperty(value = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm", timezone = "GMT+8")
    private Date updateTime;

    @ApiModelProperty(value = "今日支付订单数")
    private Integer payOrderCountToday;

    @ApiModelProperty(value = "今日支付金额")
    private BigDecimal payOrderAmountToday;

    @ApiModelProperty(value = "今日支付人数")
    private Integer payUserCountToday;

    @ApiModelProperty(value = "今日客单价")
    private BigDecimal customerPriceToday;

    @ApiModelProperty(value = "昨日支付订单数")
    private Integer payOrderCountYesterday;

    @ApiModelProperty(value = "昨日支付金额")
    private BigDecimal payOrderAmountYesterday;

    @ApiModelProperty(value = "昨日支付人数")
    private Integer payUserCountYesterday;

    @ApiModelProperty(value = "昨日客单价")
    private BigDecimal customerPriceYesterday;
}
