package com.hishop.wine.model.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 瓶内码管理表(LogisticsCodeBottle)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-09 11:39:11
 */
@Data
@ApiModel(value = "LogisticsCodeBottleVo", description = "瓶内码管理表")
public class LogisticsCodeBottleVo {
    
    @ApiModelProperty(value = "主键")
    private Long id;
    
    @ApiModelProperty(value = "瓶内码")
    private String codeBottle;

    @ApiModelProperty(value = "物流码id")
    private Long logisticsCodeId;

    @ApiModelProperty(value = "一级物流编码")
    private String codeFirst;

    @ApiModelProperty(value = "二级物流编码")
    private String codeSecondary;

    @ApiModelProperty(value = "物流码类型 0盒码 1箱码")
    private Integer codeType;

    @ApiModelProperty(value = "物流码类型 盒码 箱码")
    private String codeTypeShow;

    @ApiModelProperty(value = "批次id")
    private Long fileImportId;

}
