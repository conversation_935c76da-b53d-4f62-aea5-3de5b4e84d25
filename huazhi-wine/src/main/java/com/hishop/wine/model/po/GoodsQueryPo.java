package com.hishop.wine.model.po;

import cn.hutool.core.lang.Assert;
import com.hishop.common.pojo.SortPO;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.wine.common.enums.GoodsStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 标签查询入参
 *
 * <AUTHOR>
 * @since 2024-01-04 16:40:33
 */
@Data
@ApiModel(value = "GoodsQueryPo", description = "商品查询入参")
public class GoodsQueryPo extends PageParam {

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "商品标签ids")
    private List<Long> labelIds;

    @ApiModelProperty(value = "排序 0:升序 1:降序")
    private Integer orderType;

    @ApiModelProperty(value = "排序字段 0:创建时间 1:库存 2:排序 3:销量, 4:单价")
    private Integer orderField;

    @ApiModelProperty(value = "商品状态,OFF_SHELF:下架 FOR_SALE:上架")
    private GoodsStatus goodsStatus;

    @ApiModelProperty("排序字段")
    private List<SortPO> sortList;

    @ApiModelProperty(value = "排序sql", hidden = true)
    private String sortSql;

    public void validateParam() {
        Assert.isTrue(this.getPageNo() != null && this.getPageSize() != null, "查询请求分页参数不能为空");
    }
}
