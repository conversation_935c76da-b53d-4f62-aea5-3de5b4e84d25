package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 首页常用功能
 * @author: chenzw
 * @date: 2024/6/25 10:58
 */
@Data
@ApiModel(value = "IndexCommonMenuVo", description = "首页常用功能")
public class IndexCommonMenuVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "名称")
    private String name;

    @ApiModelProperty(value = "图标")
    private String icon;

    @ApiModelProperty(value = "路径")
    private String path;
}
