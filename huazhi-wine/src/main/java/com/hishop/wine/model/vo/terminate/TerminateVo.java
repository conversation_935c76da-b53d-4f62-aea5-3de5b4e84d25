package com.hishop.wine.model.vo.terminate;

import com.hishop.wine.common.enums.AuditStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @description: 门店返回对象
 * @author: chenzw
 * @date: 2024/7/6 15:47
 */
@Data
@ApiModel(value = "TerminateVo", description = "门店返回对象")
public class TerminateVo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "门店编码")
    private String code;

    @ApiModelProperty(value = "门店名称")
    private String name;

    @ApiModelProperty(value = "负责人姓名")
    private String dutyName;

    @ApiModelProperty(value = "手机号")
    private String phone;

    @ApiModelProperty(value = "省份名称")
    private String provinceName;

    @ApiModelProperty(value = "城市名称")
    private String cityName;

    @ApiModelProperty(value = "区县名称")
    private String districtName;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "业务员姓名")
    private String businessUserName;

    @ApiModelProperty(value = "业务员手机号")
    private String businessUserPhone;

    @ApiModelProperty(value = "经销商名称")
    private String dealerName;

    @ApiModelProperty(value = "经销商电话")
    private String dealerPhone;

    @ApiModelProperty(value = "是否启用")
    private Boolean izEnable;

    @ApiModelProperty(value = "审核状态type")
    private Integer auditStatusType;

    @ApiModelProperty(value = "审核状态")
    private AuditStatus auditStatus;

    @ApiModelProperty(value = "审核备注")
    private String auditRemark;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "区县ID")
    private Integer districtId;

    @ApiModelProperty(value = "门店面积")
    private String terminateSquare;

    @ApiModelProperty(value = "类型id")
    private Long terminateTypeId;
}
