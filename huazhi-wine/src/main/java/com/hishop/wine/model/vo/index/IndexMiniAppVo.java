package com.hishop.wine.model.vo.index;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**
 * @description: 首页小程序列表
 * @author: chenzw
 * @date: 2024/6/25 17:47
 */
@Data
@Builder
@AllArgsConstructor
@ApiModel(value = "IndexMiniAppVo", description = "小程序列表")
public class IndexMiniAppVo {

    @ApiModelProperty(value = "二维码信息")
    private String qrCode;

    @ApiModelProperty(value = "小程序名称")
    private String appName;
}
