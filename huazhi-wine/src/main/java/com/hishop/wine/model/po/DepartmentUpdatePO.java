package com.hishop.wine.model.po;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**   
 * @Description: 部门表 更新入参对象
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@ApiModel(value = "DepartmentUpdatePO", description = "部门表更新入参对象")
public class DepartmentUpdatePO {

    @NotNull(message = "部门ID不能为空")
    @ApiModelProperty(name = "id" , value = "部门ID")
	private Long id;

    @NotBlank(message = "部门名称不能为空")
    @Size(max = 20, message = "部门名称最大不能超过20个字")
    @ApiModelProperty(name = "departmentName" , value = "部门名称")
	private String departmentName;

    @ApiModelProperty(name = "parentId" , value = "上级部门ID")
	private Long parentId;

    @ApiModelProperty(name = "sort" , value = "排序")
    private Integer sort;

}
