package com.hishop.wine.model.po.terminate;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.lang.Validator;
import cn.hutool.core.util.PhoneUtil;
import com.hishop.wine.common.enums.TerminateSquare;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 门店保存请求入参
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:43
 */
@Data
@ApiModel(value = "TerminateSavePo", description = "门店保存请求入参")
public class TerminateSavePo {

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "门店编码")
    @NotBlank(message = "门店编码不能为空")
    @Size(max = 20, message = "门店编码长度不能超过20")
    private String code;

    @ApiModelProperty(value = "门店名称")
    @NotBlank(message = "门店名称不能为空")
    @Size(max = 50, message = "门店名称长度不能超过50")
    private String name;

    @ApiModelProperty(value = "类型id")
    private Long terminateTypeId;

    @ApiModelProperty(value = "门店面积")
    private TerminateSquare terminateSquare;

    @ApiModelProperty(value = "经销商id")
    private Long dealerId;

    @ApiModelProperty(value = "业务员")
    private Long businessUserId;

    @ApiModelProperty(value = "省份ID")
    private Integer provinceId;

    @ApiModelProperty(value = "城市ID")
    private Integer cityId;

    @ApiModelProperty(value = "区县ID")
    private Integer districtId;

    @ApiModelProperty(value = "详细地址")
    private String address;

    @ApiModelProperty(value = "标注位置")
    @NotBlank(message = "标注位置不能为空")
    @Size(max = 100, message = "标注位置长度不能超过100")
    private String markLocation;

    @ApiModelProperty(value = "经度")
    @NotNull(message = "经度不能为空")
    private Double lng;

    @ApiModelProperty(value = "纬度")
    @NotNull(message = "纬度不能为空")
    private Double lat;

    @ApiModelProperty(value = "负责人姓名")
    @NotBlank(message = "负责人姓名不能为空")
    @Size(max = 20, message = "负责人姓名长度不能超过20")
    private String dutyName;

    @ApiModelProperty(value = "手机号")
    @NotBlank(message = "手机号不能为空")
    @Size(max = 20, message = "手机号长度不能超过20")
    private String phone;

    @ApiModelProperty(value = "电子邮箱")
    @Size(max = 60, message = "门店电话不能超过60")
    private String mail;

    @ApiModelProperty(value = "门店照片列表")
    private List<String> photoList;

    @ApiModelProperty(value = "门店电话")
    @Size(max = 32, message = "门店电话不能超过32")
    private String storePhone;

    @ApiModelProperty(value = "营业开始时间")
    private String tradeStartTime;

    @ApiModelProperty(value = "营业结束时间")
    private String tradeEndTime;

    @ApiModelProperty(value = "营业星期")
    private List<String> tradeWeekList;

    @ApiModelProperty(value = "简介")
    @Size(max = 300, message = "简介长度不能超过300")
    private String introduce;

    public void validate() {
        if(!PhoneUtil.isMobile(phone)) {
            Assert.isTrue(false, "手机号格式不正确");
        }

        if (StringUtils.isNotBlank(mail) && !Validator.isEmail(mail)) {
            Assert.isTrue(false, "邮箱格式不正确");
        }
    }
}
