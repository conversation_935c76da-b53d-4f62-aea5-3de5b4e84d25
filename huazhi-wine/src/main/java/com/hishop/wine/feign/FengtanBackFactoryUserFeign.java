package com.hishop.wine.feign;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.GoodsQueryPo;
import com.hishop.wine.model.vo.GoodsVo;
import com.hishop.wine.model.vo.HzBackFactoryCustomerRelationVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @Date 2025/01/20/ $
 * @description:
 */
@FeignClient(name = "fengtan-wine",
        contextId = "hishop-fengtanWine-backFactoryUser",
        url = "${feign.url.fengtan}/fengtanWine",
        path = "/backFactory/user")
public interface FengtanBackFactoryUserFeign {

    @ApiOperation(value = "获取回厂用户", httpMethod = "GET")
    @GetMapping("/getBackFactoryUser")
    ResponseBean<HzBackFactoryCustomerRelationVo> getBackFactoryUser(@RequestParam(name = "phone") String phone);

}
