package com.hishop.wine.feign;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.model.po.GoodsQueryPo;
import com.hishop.wine.model.vo.GoodsVo;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 * @Date 2025/01/20/ $
 * @description:
 */
@FeignClient(name = "fengtan-wine",
        contextId = "hishop-fengtanWine-goods",
        url = "${feign.url.fengtan}/fengtanWine",
        path = "/goods")
public interface FengtanGoodsFeign {

    @ApiOperation(value = "分页查询", httpMethod = "POST")
    @PostMapping("/pageList")
    ResponseBean<PageResult<GoodsVo>> pageList(@Validated @RequestBody GoodsQueryPo goodsQueryPo);

}
