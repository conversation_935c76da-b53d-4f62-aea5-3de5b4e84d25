package com.hishop.wine.assist.manager;

import com.hishop.common.util.manager.AbsQueueManager;
import com.hishop.wine.biz.MicropageBiz;
import com.hishop.wine.repository.entity.MicropageVisit;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微页面访问记录收集器
 *
 * <AUTHOR>
 * @date : 2023/9/15
 */
@Component
public class MicroPageVisitManager extends AbsQueueManager<MicropageVisit> {

    @Resource
    private MicropageBiz micropageBiz;

    @Override
    protected Config getConfig() {
        Config config = new Config();
        config.setThreadPrefix("microPage-visit");
        config.setMinPoolSize(0);
        config.setMaxPoolSize(1);
        config.setProcessLimit(100);
        return config;
    }

    @Override
    protected void process(List<MicropageVisit> infoList, Boolean izFeign) {
        micropageBiz.saveVisit(infoList);
    }
}
