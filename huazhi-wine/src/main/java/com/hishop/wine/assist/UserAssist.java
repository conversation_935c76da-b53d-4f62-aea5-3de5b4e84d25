package com.hishop.wine.assist;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.util.HeaderUtil;
import com.hishop.common.util.PasswordUtil;
import com.hishop.wine.biz.RoleBiz;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.IdentityService;
import com.hishop.wine.repository.service.MiniUserService;
import com.hishop.wine.repository.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date : 2023/7/27
 */
@Service
@Slf4j
public class UserAssist {

    @Resource
    private UserService userService;
    @Resource
    private IdentityService identityService;
    @Resource
    private RoleBiz roleBiz;
    @Resource
    private MiniUserService miniUserService;

    public User checkAndGetUser(Long userId) {
        User user = userService.getById(userId);
        checkUser(user);
        return user;
    }

    public User checkAndGetUser(Long userId, Integer identityType) {
        User user = userService.getById(userId);
        checkUser(user);

        Identity identity = checkAndGetIdentity(userId, identityType);
        user.setIdentity(identity);
        return user;
    }

    public User checkAndGetUserByMobile(String mobile) {
        User user = userService.getUserByMobile(mobile);
        checkUser(user);
        return user;
    }

    public User checkAndGetUserByMobile(String mobile, Integer identityType) {
        User user = userService.getUserByMobile(mobile);
        Assert.isTrue(ObjectUtil.isNotNull(user), "重置密码失败");
        Assert.isTrue(user.getStatus(), "重置密码失败");
        //checkUser(user);

        Identity identity = checkAndGetIdentity(user.getId(), identityType);
        user.setIdentity(identity);
        return user;
    }

    public User checkAndGetUserByAccount(String account) {
        User user = userService.getUserByAccount(account);
        checkUser(user);
        return user;
    }

    public User checkAndGetUserByAccount(String account, Integer identityType) {
        User user = userService.getUserByAccount(account);
        checkUser(user);

        Identity identity = checkAndGetIdentity(user.getId(), identityType);
        user.setIdentity(identity);
        return user;
    }

    public Identity checkAndGetIdentity(Long userId, Integer identityType) {
        Identity identity = identityService.getIdentity(userId, identityType, HeaderUtil.getModuleCode());
        checkIdentity(identity);
        return identity;
    }

    public Identity checkAndGetIdentity(Long identityId) {
        Identity identity = identityService.getById(identityId);
        checkIdentity(identity);
        return identity;
    }

    public void checkUser(User user) {
        Assert.isTrue(ObjectUtil.isNotNull(user), "用户不存在");
        Assert.isTrue(user.getStatus(), "用户已被禁用");
    }

    public void checkIdentity(Identity identity) {
        Assert.isTrue(ObjectUtil.isNotNull(identity) && !identity.getIzDelete(), "用户不存在");
        Assert.isTrue(identity.getStatus(), "用户已被禁用");
    }

    public void checkPassword(String password, String dbPassword) {
        Assert.isTrue(StrUtil.isNotEmpty(password), "请输入密码");
        Assert.isTrue(PasswordUtil.checkPassword(password, dbPassword), "密码错误");
    }

    public Boolean checkSuperAdmin(Long roleId) {
        return BasicConstants.SUPER_ADMIN_ROLE_ID.equals(roleId);
    }

    public Boolean checkResourceAuth(Long roleId, Long resourceId) {
        return roleBiz.checkResourceAuth(roleId, resourceId);
    }

    public Boolean checkRole(Long userId, Long identityId, Long roleId) {
        long count = identityService.count(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, userId)
                .eq(ObjectUtil.isNotNull(identityId), Identity::getId, identityId)
                .eq(Identity::getRoleId, roleId)
                .eq(Identity::getStatus, Boolean.TRUE)
                .eq(Identity::getIzDelete, DeleteFlagEnums.NO.getCode()));
        return count > 0;
    }

    public Map<Long, User> getUserMap(List<Long> userIds) {
        if (CollectionUtils.isEmpty(userIds)) {
            return MapUtil.empty();
        }

        return userService.listByIds(userIds).stream().collect(Collectors.toMap(User::getId, user -> user));
    }

    public MiniUser checkAndGetMiniUser(Long userId, String appId) {
        MiniUser miniUser = miniUserService.getMiniUser(userId, appId);
        Assert.isTrue(ObjectUtil.isNotNull(miniUser), "用户不存在");
        return miniUser;
    }
}
