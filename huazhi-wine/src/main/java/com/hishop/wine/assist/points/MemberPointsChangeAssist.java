package com.hishop.wine.assist.points;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.exception.BusinessException;
import com.hishop.wine.assist.lock.LockAssist;
import com.hishop.wine.assist.lock.LockConst;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.enums.points.MemberPointsEnum;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.entity.MemberPointsDetails;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.service.MemberPointsDetailService;
import com.hishop.wine.repository.service.MemberPointsService;
import com.hishop.wine.repository.service.UserService;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 用户积分变更辅助类，公共方法，不管是业务操作，还是手动变更，还是消息队列，都可以调用这个类
 * <AUTHOR>
 * @date 2023/6/25
 */
@Service
@Slf4j
public class MemberPointsChangeAssist {

    @Resource
    private MemberPointsService memberPointsService;
    @Resource
    private MemberPointsDetailService memberPointsDetailService;
    @Resource
    private UserService userService;
    @Resource
    private LockAssist lockAssist;
    @Resource
    private RedisTemplate<String, String> redisTemplate;

    /**
     * 积分变更操作，涉及记录积分变更流水，以及调整会员积分总数
     * <AUTHOR>
     * @date 2023/6/26
     */
    @Transactional
    public void changePoints(ChangePointsPO pointsPo) {
        if (MemberPointsEnum.ModifiedType.INCREASE.getCode().equals(pointsPo.getModifiedType()) &&
                pointsPo.getExpireTime() == null) {
            pointsPo.setExpireTime(DateUtil.offsetDay(new Date(),30));
        }
        // 变更的积分值为空，不需要变更
        Integer modifiedPoints = pointsPo.getModifiedPoints();
        if (modifiedPoints == null || modifiedPoints == 0) {
            log.warn("积分变动值为空，不进行积分变动");
            return;
        }
        validData(pointsPo);
        // 获取用户信息
        User user = userService.getById(pointsPo.getUserId());
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        try {
            /*boolean isNeedChange = idempotentShouldContinue(pointsPo);
            if (!isNeedChange) {
                return;
            }*/
            boolean locked = lockAssist.lockChangePoints(user.getId(), 2, 5, TimeUnit.SECONDS);
            if (!locked) {
                throw new RuntimeException("系统繁忙，请稍后再试");
            }
            executeChange(pointsPo, user, modifiedPoints);
        } catch (Exception e) {
            log.error("积分变更失败，userId：{}，identityType：{}，modifiedPoints：{}，modifiedType：{}，expireTime：{}",
                    pointsPo.getUserId(), pointsPo.getIdentityType(), pointsPo.getModifiedPoints(), pointsPo.getModifiedType(), pointsPo.getExpireTime(), e);
            throw new RuntimeException(e.getMessage());
        } finally {
            lockAssist.unlockChangePoints(user.getId());
        }
    }

    private void executeChange(ChangePointsPO pointsPo, User user, Integer modifiedPoints) {
        // 1. 记录积分变更流水
        MemberPointsDetails memberPointsDetails = BeanUtil.copyProperties(pointsPo, MemberPointsDetails.class);
        memberPointsDetails.setUserPhone(user.getMobile());
        memberPointsDetails.setCreateTime(new Date());
        memberPointsDetails.setIdentityType(pointsPo.getIdentityType());
        memberPointsDetailService.save(memberPointsDetails);
        // 2. 同步调整积分总数
        MemberPoints memberPoints = memberPointsService.getUserPoints(user.getId(), pointsPo.getIdentityType());
        if (memberPoints == null) {
            memberPoints = new MemberPoints();
            memberPoints.setUserId(user.getId());
            memberPoints.setIdentityType(pointsPo.getIdentityType());
            memberPoints.setUserPhone(user.getMobile());
            memberPoints.setAvailablePoints(0);
            memberPoints.setExpiredPoints(0);
            memberPoints.setFrozenPoints(0);
            memberPoints.setTotalPoints(0);
            memberPoints.setConsumedPoints(0);
        }

        int absPoints = Math.abs(modifiedPoints);
        // 根据业务类型计算需要调整的数据
        if (MemberPointsEnum.ModifiedType.INCREASE.getCode().equals(pointsPo.getModifiedType())) {
            // 2.1 增加积分，传入的为正，同步调整：总积分增加、可用积分增加
            memberPoints.setTotalPoints(memberPoints.getTotalPoints() + absPoints);
            memberPoints.setAvailablePoints(memberPoints.getAvailablePoints() + absPoints);
        } else if (MemberPointsEnum.ModifiedType.DECREASE.getCode().equals(pointsPo.getModifiedType())) {
            // 2.2 减少积分，传入的为负，同步调整：可用积分减少、消费积分增加，消费积分记录最后一条记录id、消费积分记录最后一条记录剩余积分
            int remain = memberPoints.getAvailablePoints() - absPoints;
            if (remain < 0) {
                throw new BusinessException("积分不足");
            }
            memberPoints.setConsumedPoints(memberPoints.getConsumedPoints() + absPoints);
            memberPoints.setAvailablePoints(remain);
            // 记录最后的积分消费流水指针，便于做积分过期的精细控制
            //decreaseOnDetail(pointsPo.getUserId(), pointsPo.getIdentityType(), pointsPo.getModuleCode(), memberPoints, absPoints);
        } else if (MemberPointsEnum.ModifiedType.CLEAR.getCode().equals(pointsPo.getModifiedType())) {
            // 2.3 积分清零，也算是积分减少，传入负数，同步调整：过期积分增加、可用积分减少、总积分减少
            memberPoints.setExpiredPoints(memberPoints.getExpiredPoints() + absPoints);
            memberPoints.setAvailablePoints(memberPoints.getAvailablePoints() - absPoints);
        }
        memberPointsService.saveOrUpdate(memberPoints);
    }

    /**
     * 用户操作积分加了分布式锁
     * <AUTHOR>
     * @date 2023/8/3
     */
    private void decreaseOnDetail(Long userId, Integer identityType, String moduleCode, MemberPoints memberPoints, int targetPoints) {
        // 一次 查了10条，理论上够用了，如果不够用，循环获取
        LastDetail lastDetail = calculateLast(userId, identityType, moduleCode, memberPoints, targetPoints);
        while (lastDetail != null && lastDetail.getLastRemain() > 0) {
            lastDetail = calculateLast(userId, identityType, moduleCode, memberPoints, lastDetail.getLastRemain());
        }
        if (lastDetail == null) {
            return;
        }
        memberPoints.setConsumedLastDetailId(lastDetail.getLastId());
        memberPoints.setLastDetailRemainPoint(lastDetail.getLastRemain());
    }

    private LastDetail calculateLast(Long userId, Integer identityType, String moduleCode, MemberPoints memberPoints, int targetPoints) {
        // 根据最后一条消费积分记录指针，获取10条明细记录，按照过期时间升序排序，依次扣减积分
        Integer size = 10;
        Integer modifyType = MemberPointsEnum.ModifiedType.INCREASE.getCode();
        Long lastId = memberPoints.getConsumedLastDetailId();
        List<MemberPointsDetails> list = memberPointsDetailService.getLastDetail(userId, identityType, moduleCode, modifyType, lastId, size);
        if (CollUtil.isEmpty(list)) {
            return null;
        }
        int points = targetPoints;
        boolean hasRemain = memberPoints.getLastDetailRemainPoint() > 0;
        long newLastId = 0L;
        int lastRemain = 0;
        for (MemberPointsDetails detail : list) {
            if (detail.getId().equals(lastId) && !hasRemain) {
                continue;
            }
            points -= detail.getModifiedPoints();
            // 剩余积分小于等于0，则说明积分扣足够了，终止
            if (points <= 0) {
                newLastId = detail.getId();
                lastRemain = Math.abs(points);
                break;
            }
        }
        return new LastDetail(newLastId, lastRemain);
    }

    private void validData(ChangePointsPO pointsPo) {
        String errorMsg = "";
        if (pointsPo.getUserId() == null || pointsPo.getUserId() == 0) {
            errorMsg += "用户id为空或0;";
        }
        if (!MemberPointsEnum.ModifiedType.isLegal(pointsPo.getModifiedType())) {
            errorMsg += "积分变更类型不合法;";
        }
        if (!ModuleEnums.isLegal(pointsPo.getModuleCode())) {
            errorMsg += "积分变更来源不合法;";
        }
        if (StrUtil.isBlank(pointsPo.getTraceNo())) {
            errorMsg += "积分变更跟踪号为空;";
        }
        if (pointsPo.getBizType() == null) {
            errorMsg += "积分变更业务类型为空;";
        }
        if (MemberPointsEnum.ModifiedType.INCREASE.getCode().equals(pointsPo.getModifiedType()) &&
                pointsPo.getExpireTime() == null) {
            errorMsg += "积分过期时间为空;";
        }
        if (MemberPointsEnum.ModifiedType.INCREASE.getCode().equals(pointsPo.getModifiedType()) &&
                pointsPo.getModifiedPoints() < 0) {
            errorMsg += "积分发放时积分值需要大于0;";
        }
        if (MemberPointsEnum.ModifiedType.DECREASE.getCode().equals(pointsPo.getModifiedType()) &&
                pointsPo.getModifiedPoints() > 0) {
            errorMsg += "积分消耗时积分值需要小于0;";
        }
        if (pointsPo.getCreateBy() == null) {
            errorMsg += "积分变更操作人为空;";
        }
        if (StrUtil.isNotBlank(errorMsg)) {
            errorMsg = "积分变更参数校验失败: " + errorMsg;
            log.error("积分变更参数校验失败: {}", errorMsg);
            throw new BusinessException(errorMsg);
        }
    }

    /**
     * 幂等性校验。包含两个层面
     * 1. 跟踪号，每个积分变更请求都需要传入跟踪号，用于幂等性校验
     * 2. 业务编码+业务类型，同一个业务类型，同一个业务编码，同一个用户，只能有一条积分变更记录
     * <AUTHOR>
     * @date 2023/8/2
     */
    private boolean idempotentShouldContinue(ChangePointsPO pointsPo) {
        // 1. 跟踪号幂等性校验
        String traceNo = pointsPo.getTraceNo();
        String traceNoKey = LockConst.LOCK_PREFIX + traceNo;
        Boolean traceNoFlag = redisTemplate.opsForValue().setIfAbsent(traceNoKey, "", 2, TimeUnit.DAYS);
        if (!Boolean.TRUE.equals(traceNoFlag)) {
            return false;
        }
        // 2. 业务编码+业务类型幂等性校验
        String bizKey = LockConst.LOCK_PREFIX + pointsPo.getBizType() + "||" + pointsPo.getBizCode();
        Boolean bizFlag = redisTemplate.opsForValue().setIfAbsent(bizKey, "", 2, TimeUnit.DAYS);
        return Boolean.TRUE.equals(bizFlag);
    }

    @Data
    @AllArgsConstructor
    static class LastDetail {
        private Long lastId;
        private int lastRemain;
    }

}
