package com.hishop.wine.assist.transaction;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.mq.api.MQ;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.transaction.TransactionSuccessNoticePO;
import com.hishop.wine.model.vo.transaction.TransactionEntPayVO;
import com.hishop.wine.model.vo.transaction.TransactionPayVO;
import com.hishop.wine.model.vo.transaction.TransactionRefundVO;
import com.hishop.wine.repository.entity.Transaction;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * 抽象交易类
 *
 * <AUTHOR>
 * @date : 2023/9/18
 */
@Slf4j
public abstract class AbsTransactionHandler {

    @Resource
    private MQ mq;

    /**
     * 发起支付
     *
     * @param payParam 支付参数
     * @return 支付返回值
     */
    public abstract TransactionPayVO pay(TransactionPayPO payParam);

    /**
     * 支付回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    public abstract String notifyPay(String moduleCode, String notifyData);

    /**
     * 退款
     *
     * @param refundParam    退款参数
     * @param orgTransaction 原交易记录
     * @return 退款返回值
     */
    public abstract TransactionRefundVO refund(TransactionRefundPO refundParam, Transaction orgTransaction);

    /**
     * 退款回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    public abstract String notifyRefund(String moduleCode, String notifyData);

    /**
     * 发起企业打款
     *
     * @param entPayParam 企业打款参数
     * @return 企业打款返回值
     */
    public abstract TransactionEntPayVO entPay(TransactionEntPayPO entPayParam);

    /**
     * 检查企业付款到零钱结果
     *
     * @param transaction 交易流水
     */
    public abstract void checkEntPayResult(Transaction transaction);

    /**
     * 指定交易方式
     *
     * @return 交易方式
     */
    public abstract TransactionEnum.MethodEnum getMethod();

    /**
     * 支付回调通知业务系统
     *
     * @param transaction 交易记录
     * @param tags        日志标签
     */
    protected void callBackBusinessSystem(Transaction transaction, String tags) {
        // 获取通知topic
        TransactionEnum.BizTypeEnum byType = TransactionEnum.BizTypeEnum.getByType(transaction.getBizType());
        if (ObjectUtil.isNull(byType) || StrUtil.isEmpty(byType.getCallbackTopic())) {
            log.info(String.format("【%】通知业务系统, 未指定topic, 无需通知, TransactionId: {}", tags), transaction.getId());
            return;
        }

        TransactionSuccessNoticePO transactionSuccessNoticePO = BeanUtil.copyProperties(transaction, TransactionSuccessNoticePO.class);
        transactionSuccessNoticePO.setTransactionId(transaction.getId());
        transactionSuccessNoticePO.setSuccess(transaction.getStatus().equals(TransactionEnum.Status.SUCCESS.getStatus()));
        mq.publish(byType.getCallbackTopic(), transactionSuccessNoticePO);
        log.info(String.format("【%s】回调业务系统: topic: {}, 回调参数: {}", tags), byType.getCallbackTopic(), transactionSuccessNoticePO);
    }

}
