package com.hishop.wine.assist.transaction;

import com.hishop.wine.enums.TransactionEnum;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date : 2023/9/18
 */
public class TransactionHandlerMapping {

    private static final Map<TransactionEnum.MethodEnum, AbsTransactionHandler> HANDLER_MAP = new HashMap<>();

    /**
     * 注册处理器
     *
     * @param handler 处理器
     */
    public static void registerHandler(AbsTransactionHandler handler) {
        HANDLER_MAP.put(handler.getMethod(), handler);
    }

    /**
     * 获取处理器
     *
     * @param method 交易方式
     * @return 处理器
     */
    public static AbsTransactionHandler getHandler(TransactionEnum.MethodEnum method) {
        return HANDLER_MAP.get(method);
    }

}
