package com.hishop.wine.assist.transaction;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.lang.Nullable;
import org.springframework.stereotype.Component;

/**
 * 扫描处理器
 *
 * <AUTHOR>
 * @date : 2023/9/18
 */
@Component
public class TransactionBeanProcessor implements BeanPostProcessor {

    @Override
    @Nullable
    public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof AbsTransactionHandler) {
            TransactionHandlerMapping.registerHandler((AbsTransactionHandler) bean);
        }
        return bean;
    }

}
