package com.hishop.wine.assist.service;

import com.alibaba.fastjson.JSON;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.model.po.wxShipping.request.*;
import com.hishop.wine.model.po.wxShipping.response.*;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Date 2025/03/31/ $
 * @description:
 */
@Slf4j
@Service
public class WxMaShippingService {

    private static final MediaType JSONType = MediaType.parse("application/json; charset=utf-8");
    private OkHttpClient client = new OkHttpClient.Builder()
            .connectTimeout(30, TimeUnit.SECONDS)
            .readTimeout(30, TimeUnit.SECONDS)
            .writeTimeout(30, TimeUnit.SECONDS)
            .build();
    private static final String wxurl = "https://api.weixin.qq.com/wxa/sec/order";

    private static final String TOKEN_URL = "https://api.weixin.qq.com/cgi-bin/token";

    private static String REDIS_TOKEN_KEY = "wx_ma_shipping_token:";

    private static final String API_URL = "https://api.weixin.qq.com/cgi-bin/express/delivery/open_msg/get_delivery_list";

    private static final Integer DEFAULT_EXPIRE_SECONDS = 7200;

    public String getToken(String appid, String secret) {
        String token = RedisUtil.get(REDIS_TOKEN_KEY + appid);
        if (token == null || isTokenExpired(appid)) {
            resetToken(appid, secret);
            return RedisUtil.get(REDIS_TOKEN_KEY + appid);
        }
        return token;
    }

    public void resetToken(String appid, String secret) {
        RedisUtil.lock("reset_token_lock", () -> {
            // 双重检查锁定（Double-Checked Locking）
            String existingToken = RedisUtil.get(REDIS_TOKEN_KEY + appid);
            if (existingToken != null) {
                RedisUtil.del(REDIS_TOKEN_KEY + appid);
            }
            // 调用微信接口获取新 Token（此处需补充具体逻辑）
            String newToken = callWeChatTokenApi(appid, secret);
            RedisUtil.set(REDIS_TOKEN_KEY + appid, newToken, DEFAULT_EXPIRE_SECONDS, TimeUnit.SECONDS);
            log.info("Token 已刷新: {}", newToken);
        });
    }

    private boolean isTokenExpired(String appid) {
        Long expireTime = RedisUtil.getExpire(REDIS_TOKEN_KEY + appid);
        return expireTime == null || expireTime < 5 * 60; // 检查剩余有效期是否不足 5*60 秒
    }

    private String callWeChatTokenApi(String appid, String secret) {
        try {
            String url = TOKEN_URL + "?grant_type=client_credential" +
                    "&appid=" + appid +
                    "&secret=" + secret +
                    "&force_refresh=false"; // 普通模式获取

            // 使用Spring的RestTemplate调用接口（示例）
            // 实际项目中建议使用更健壮的HTTP客户端如OkHttp
            RestTemplate restTemplate = new RestTemplate();
            String response = restTemplate.getForObject(url, String.class);

            // 解析微信返回的JSON
            WeChatTokenResponse tokenResponse = new Gson().fromJson(response, WeChatTokenResponse.class);
            if (tokenResponse.getErrcode() == 0) {
                return tokenResponse.getAccessToken();
            } else {
                log.error("微信接口错误: {}", tokenResponse.getErrmsg());
                return null;
            }
        } catch (Exception e) {
            log.error("HTTP请求失败", e);
            return null;
        }
    }

    /**
     * 发货信息录入接口
     * @param request
     * @return
     * @throws IOException
     */
    public UploadShippingResponse uploadShipping(UploadShippingRequest request) throws IOException {
        log.info("发货信息录入接口入参：" + JSON.toJSONString(request));
        String url = wxurl + "/upload_shipping_info?access_token=" + request.getAccessToken();
        request.setAccessToken(null);
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("发货信息录入接口出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("发货信息录入接口请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("发货信息录入接口请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), UploadShippingResponse.class);
        }
    }

    /**
     * 合单发货信息录入接口
     * @param request
     * @return
     * @throws IOException
     */
    public UploadCombinedShippingResponse uploadCombinedShipping(UploadCombinedShippingRequest request) throws IOException {
        log.info("合单发货信息录入接口入参：" + JSON.toJSONString(request));
        String url = wxurl + "/upload_combined_shipping_info?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("合单发货信息录入接口出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("合单发货信息录入接口请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("合单发货信息录入接口请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), UploadCombinedShippingResponse.class);
        }
    }

    /**
     * 查询订单发货状态
     * @param request
     * @return
     * @throws IOException
     */
    public GetOrderResponse getOrder(GetOrderRequest request) throws IOException {
        log.info("查询订单发货状态入参：" + JSON.toJSONString(request));
        String url = wxurl + "/get_order?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("查询订单发货状态出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("查询订单发货状态请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("查询订单发货状态请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), GetOrderResponse.class);
        }
    }

    /**
     * 查询订单列表
     * @param request
     * @return
     * @throws IOException
     */
    public GetOrderListResponse getOrderList(GetOrderListRequest request) throws IOException {
        log.info("查询订单列表入参：" + JSON.toJSONString(request));
        String url = wxurl + "/get_order_list?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("查询订单列表出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("查询订单列表请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("查询订单列表请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), GetOrderListResponse.class);
        }
    }

    /**
     * 确认收货提醒接口
     * @param request
     * @return
     * @throws IOException
     */
    public ConfirmReceiveResponse confirmReceive(ConfirmReceiveRequest request) throws IOException {
        log.info("确认收货提醒接口入参：" + JSON.toJSONString(request));
        String url = wxurl + "/notify_confirm_receive?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("确认收货提醒接口出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("确认收货提醒接口请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("确认收货提醒接口请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), ConfirmReceiveResponse.class);
        }
    }

    /**
     * 设置消息跳转路径
     * @param request
     * @return
     * @throws IOException
     */
    public SetMsgJumpPathResponse setMsgJumpPath(SetMsgJumpPathRequest request) throws IOException {
        log.info("设置消息跳转路径入参：" + JSON.toJSONString(request));
        String url = wxurl + "/set_msg_jump_path?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(requestObj).execute()) {
            log.info("设置消息跳转路径出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("设置消息跳转路径请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("设置消息跳转路径请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), SetMsgJumpPathResponse.class);
        }
    }

    /**
     * 查询小程序是否开通服务
     * @param request
     * @return
     * @throws IOException
     */
    public IsTradeManagedResponse isTradeManaged(IsTradeManagedRequest request) throws IOException {
        log.info("查询小程序是否开通服务入参：" + JSON.toJSONString(request));
        String url = wxurl + "/is_trade_managed?access_token=" + request.getAccessToken();
        String jsonBody = "{\"appid\":\""+request.getAppid()+"\"}";

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request requestObj = new Request.Builder()
                .url(url)
                .post(body)
                .build();
        try (Response response = client.newCall(requestObj).execute()) {
            log.info("查询小程序是否开通服务出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("查询小程序是否开通服务请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("查询小程序是否开通服务请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), IsTradeManagedResponse.class);
        }
    }

    /**
     * 查询小程序是否已完成交易结算管理确认
     * @param request
     * @return
     * @throws IOException
     */
    public TradeManagementCheckResponse isTradeManagementConfirmationCompleted(TradeManagementCheckRequest request) throws IOException {
        log.info("查询小程序是否已完成交易结算管理确认入参：" + JSON.toJSONString(request));
        String url = wxurl + "/is_trade_management_confirmation_completed?access_token=" + request.getAccessToken();
        String jsonBody = GsonUtil.toJson(request);

        RequestBody body = RequestBody.create(jsonBody, JSONType);
        Request httpRequest = new Request.Builder()
                .url(url)
                .post(body)
                .build();

        try (Response response = client.newCall(httpRequest).execute()) {
            log.info("查询小程序是否已完成交易结算管理确认出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("查询小程序是否已完成交易结算管理确认请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("查询小程序是否已完成交易结算管理确认请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), TradeManagementCheckResponse.class);
        }
    }

    /**
     * 获取运力id列表
     * @param accessToken
     * @return
     * @throws IOException
     */
    public DeliveryListResponse getDeliveryList(String accessToken) throws IOException {
        RequestBody body = RequestBody.create("{}", JSONType);
        Request request = new Request.Builder()
                .url(API_URL + "?access_token=" + accessToken)
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            log.info("获取运力id列表出参：" + JSON.toJSONString(response));
            if (!response.isSuccessful()) {
                log.error("获取运力id列表请求失败：" + GsonUtil.toJson(response));
                throw new BusinessException("获取运力id列表请求失败");
            }
            return GsonUtil.fromJson(response.body().string(), DeliveryListResponse.class);
        }
    }

    // Gson工具类（简化处理）
    public static class GsonUtil {
        private static Gson gson = new GsonBuilder().create();

        public static String toJson(Object obj) {
            return gson.toJson(obj);
        }

        public static <T> T fromJson(String json, Class<T> clazz) {
            return gson.fromJson(json, clazz);
        }
    }
}
