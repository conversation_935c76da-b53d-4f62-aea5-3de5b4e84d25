package com.hishop.wine.assist.transaction.weixin;

import lombok.Getter;

/**
 * 微信交易枚举
 *
 * <AUTHOR>
 * @date : 2023/9/19
 */
public class WeixinTransactionEnum {

    /**
     * 企业打款状态
     * 【明细状态】 INIT: 初始态。 系统转账校验中
     * WAIT_PAY: 待确认。待商户确认, 符合免密条件时, 系统会自动扭转为转账中
     * PROCESSING:转账中。正在处理中，转账结果尚未明确
     * SUCCESS:转账成功
     * FAIL:转账失败。需要确认失败原因后，再决定是否重新发起对该笔明细单的转账（并非整个转账批次单）
     */
    @Getter
    public enum EntryPayStatus {
        INIT,
        WAIT_PAY,
        PROCESSING,
        SUCCESS,
        FAIL
    }

}
