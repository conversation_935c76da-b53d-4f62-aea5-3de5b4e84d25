package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.entity.Tags;
import java.util.List;

/**   
 * 标签表 数据层服务类
 * @author: chenpeng
 * @date: 2023-07-17
 */

public interface TagsService extends IService<Tags> {

    Page<Tags> pageList(Page<Tags> page, TagsQueryPO pagePO);

    List<TagsVO> getMyList(Long userId);

    List<TagsVO> getTopList(Integer num, List<Long> tagIds, String tagName);
}