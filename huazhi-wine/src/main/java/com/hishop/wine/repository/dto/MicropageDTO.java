package com.hishop.wine.repository.dto;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.util.Date;

@Data
public class MicropageDTO {
    /**
     * id
     */
    private int id;

    /**
     * 模板id
     */
    private int templateIndex;

    /**
     * 分组id
     */
    private long categoryId;

    /**
     * 名称
     */
    private String name;

    /**
     * 备注
     */
    private String remark;

    /**
     * 背景色
     */
    private String bgColor;

    /**
     * 分享图片
     */
    private String shareImg;

    /**
     * 启用顶栏
     */
    private boolean useTop;

    /**
     * 顶栏背景颜色
     */
    private String topBgColor;

    /**
     * 顶栏背景图片
     */
    private String topBgPath;

    /**
     * 显示名称
     */
    private boolean showName;

    /**
     * 名称文字颜色
     */
    private String showNameFizeColor;

    /**
     * 显示logo
     */
    private boolean showLogo;

    /**
     * logo 路径
     */
    private String logoImagePath;

    /**
     * 草稿名称
     */
    private String draftName;

    /**
     * 草稿备注
     */
    private String draftRemark;

    /**
     * 草稿背景颜色
     */
    private String draftBgColor;

    /**
     * 草稿分享图片
     */
    private String draftShareImg;

    /**
     * 草稿启用顶栏
     */
    private boolean draftUseTop;

    /**
     * 草稿顶栏背景颜色
     */
    private String draftTopBgColor;

    /**
     * 草稿顶栏背景图片
     */
    private String draftTopBgPath;

    /**
     * 草稿显示名称
     */
    private boolean draftShowName;

    /**
     * 草稿显示文字颜色
     */
    private String draftShowNameFizeColor;

    /**
     * 草稿显示logo
     */
    private boolean draftShowLogo;

    /**
     * 草稿显示logo路径
     */
    private String draftLogoImagePath;

    /**
     * pv
     */
    private int pv;

    /**
     * uv
     */
    private int uv;

    /**
     * 创建人
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 修改人
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


    private boolean isShowDraft;


    /**
     * 微页面json
     */
    private String dataJson;

    /**
     * 草稿微页面json
     */
    private String draftDataJson;

    /**
     * 分享标题
     */
    private String shareTitle;
}
