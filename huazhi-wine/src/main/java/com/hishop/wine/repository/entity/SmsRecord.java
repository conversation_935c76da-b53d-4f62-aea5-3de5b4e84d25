package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;
import java.util.Date;

/**   
 * 短信发送记录表 实体类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Data
@NoArgsConstructor
@TableName("hishop_sms_record")
public class SmsRecord implements Serializable {

	private static final long serialVersionUID = 1678971120765427712L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 短信类型 0-通知短信 1-营销短信 2-验证码短信
    */
	private Integer channel;
    
    /**
    * 短信内容
    */
	private String content;
    
    /**
    * 发送状态 0-未发送 1-发送成功 2-发送失败
    */
	private Integer status;
    
    /**
    * 发送结果
    */
	private String result;

    /**
     * 发送时间(现在就是创建时间, 暂无延迟推送)
     */
	private Date sendTime;
    
    /**
    * 创建者
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

    /**
     * 发送手机号
     */
    @TableField(exist = false)
    private String mobiles;
    

}
