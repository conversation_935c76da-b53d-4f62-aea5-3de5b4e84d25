package com.hishop.wine.repository.param.moderation;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;

import java.util.Date;
import java.util.List;

/**
 * 内容审核表 数据库查询类
 *
 * @author: <PERSON><PERSON>ia<PERSON>
 * @date: 2023-09-11
 */

@Data
@Builder
public class ContentModerationParam {


    private Long id;


    private String moduleCode;


    private String bizType;


    private String bizCode;


    private String bizDesc;


    private String status;


    private Long createBy;


    private Date createTime;


    private Long updateBy;


    private Date updateTime;


    private List<String> statusList;


}
