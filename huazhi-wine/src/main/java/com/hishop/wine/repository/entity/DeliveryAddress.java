package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 收货地址表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */
@Data
@NoArgsConstructor
@TableName("hishop_delivery_address")
public class DeliveryAddress implements Serializable {

    private static final long serialVersionUID = 1674239024905924608L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 身份id
     */
    private Long identityId;

    /**
     * 收货人
     */
    private String consignee;

    /**
     * 收货人手机号
     */
    private String consigneePhone;

    /**
     * 省ID
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市ID
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区ID
     */
    private Integer areaId;

    /**
     * 区
     */
    private String area;

    /**
     * 街道id
     */
    private Integer streetId;

    /**
     * 街道名称
     */
    private String street;

    /**
     * 详细地址
     */
    private String address;

    /**
     * 是否是默认地址
     */
    private Boolean izDefault;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
