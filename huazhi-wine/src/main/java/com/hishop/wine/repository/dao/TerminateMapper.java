package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.terminate.TerminateQueryPo;
import com.hishop.wine.model.vo.terminate.TerminateVo;
import com.hishop.wine.repository.entity.Terminate;
import org.apache.ibatis.annotations.Param;

/**
 * 门店表(Terminate)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:43
 */
public interface TerminateMapper extends BaseMapper<Terminate> {

    /**
     * 分页查询
     * @param page 分页对象
     * @param terminateQueryPo 查询实体
     * @return 分页对象
     */
    Page<TerminateVo> queryTerminatePageList(Page<TerminateVo> page, @Param("param") TerminateQueryPo terminateQueryPo);
}

