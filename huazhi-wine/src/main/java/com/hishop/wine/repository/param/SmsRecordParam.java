package com.hishop.wine.repository.param;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 短信发送记录表 数据库查询类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Data
@Builder
public class SmsRecordParam {

	private String mobile;

	private Date startTime;

	private Date endTime;

}
