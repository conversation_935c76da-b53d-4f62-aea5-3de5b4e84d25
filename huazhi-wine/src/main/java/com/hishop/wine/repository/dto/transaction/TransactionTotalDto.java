package com.hishop.wine.repository.dto.transaction;

import lombok.Data;

import java.math.BigDecimal;

/**
 * @description: 交易统计dto
 * @author: chenzw
 * @date: 2024/6/25 14:11
 */
@Data
public class TransactionTotalDto {

    /**
     * 日期
     */
    private String dateTime;

    /**
     * 支付订单数
     */
    private Integer payOrderCount;

    /**
     * 支付金额
     */
    private BigDecimal payOrderAmount;

    /**
     * 支付人数
     */
    private Integer payUserCount;
}
