package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.RefundReasonConfigMapper;
import com.hishop.wine.repository.entity.RefundReasonConfig;
import com.hishop.wine.repository.service.RefundReasonConfigService;
import org.springframework.stereotype.Service;

/**   
 * 退款原因配置表 服务实现类
 * @author: chenpeng
 * @date: 2023-07-08
 */

@Service
public class RefundReasonConfigServiceImpl extends ServiceImpl<RefundReasonConfigMapper, RefundReasonConfig> implements RefundReasonConfigService  {

}