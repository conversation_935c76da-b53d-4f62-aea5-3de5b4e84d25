package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 应用推荐(AppRecommend)表实体类
 *
 * <AUTHOR>
 * @since 2024-06-25 17:47:02
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_app_recommend")
public class AppRecommend extends BaseEntity {

    /**
     * 主键
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 应用名称
     **/
    private String name;
    /**
     * 应用描述
     **/
    private String appDescribe;
    /**
     * 跳转地址
     **/
    private String linkAddress;
    /**
     * 是否删除 0否 1是
     **/
    private Boolean izDelete;
}

