package com.hishop.wine.repository.param.rank;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 头衔表 数据库查询类
 * @author: HuBia<PERSON>
 * @date: 2023-07-25
 */

@Data
@Builder
public class RankParam {


	private Long id;

	private String rankName;
    

	private String moduleCode;
    

	private Boolean status;
    

	private Boolean izDelete;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
