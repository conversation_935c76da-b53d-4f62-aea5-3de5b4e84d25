package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 地址库表 数据库查询类
 * @author: Hu<PERSON>ia<PERSON>
 * @date: 2023-07-17
 */

@Data
@Builder
public class BaseAddressParam {


	private Long id;
    

	private Long userId;
    

	private Long identityId;
    

	private String contacts;
    

	private String contactsPhone;
    

	private Integer provinceId;
    

	private String province;
    

	private Integer cityId;
    

	private String city;
    

	private Integer areaId;
    

	private String area;
    

	private Integer streetId;
    

	private String street;
    

	private String address;
    

	private String postalCode;
    

	private Boolean izDefaultSendAddress;
    

	private Boolean izDefaultReceiveAddress;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
