package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import com.hishop.wine.repository.dao.DealerMapper;
import com.hishop.wine.repository.entity.Dealer;
import com.hishop.wine.repository.service.DealerService;
import org.springframework.stereotype.Service;

/**
 * 经销商表(Dealer)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
@Service
public class DealerServiceImpl extends ServiceImpl<DealerMapper, Dealer> implements DealerService {

    @Override
    public Page<DealerVo> queryDealerPageList(DealerQueryPo dealerQueryPo) {
        return baseMapper.queryDealerPageList(dealerQueryPo.buildPage(),dealerQueryPo);
    }
}

