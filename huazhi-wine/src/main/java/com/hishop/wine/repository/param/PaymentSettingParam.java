package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 支付设置 数据库查询类
 * @author: HuBia<PERSON>
 * @date: 2023-07-18
 */

@Data
@Builder
public class PaymentSettingParam {


	private Long id;
    

	private String paymentName;
    

	private String paymentType;
    

	private String settingValue;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
