package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.DecorateMapper;
import com.hishop.wine.repository.entity.Decorate;
import com.hishop.wine.repository.service.DecorateService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

/**   
 * 商城装修表 服务实现类
 * @author: LiGuoQiang
 * @date: 2023-06-26
 */

@Service
public class DecorateServiceImpl extends ServiceImpl<DecorateMapper, Decorate> implements DecorateService {

    @Override
    public Decorate getByType(String appId, String moduleCode, String decorateType) {
        LambdaQueryWrapper<Decorate> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Decorate::getDecorateType, decorateType)
                .eq(StringUtils.isNotEmpty(moduleCode), Decorate::getModuleCode, moduleCode)
                .eq(Decorate::getIzDelete, false)
                .orderByDesc(Decorate::getIzDefault);
        if(StringUtils.isNotEmpty(appId)) {
            wrapper.eq(Decorate::getAppId, appId);
        }
        wrapper.last(" limit 1");
        return super.getOne(wrapper);
    }

    @Override
    public void updateOtherDefault(String appId, String modelCode, String decorateType) {
        LambdaUpdateWrapper<Decorate> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.set(Decorate::getIzDefault, false)
                .eq(Decorate::getAppId, appId)
                .eq(Decorate::getDecorateType, decorateType)
                .ne(Decorate::getModuleCode, modelCode);
        super.update(updateWrapper);
    }
}