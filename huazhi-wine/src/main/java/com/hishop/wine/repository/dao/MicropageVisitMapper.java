package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.MicropageVisit;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**   
 * 微页面访客记录表 Mapper 接口
 * @author: HuBiao
 * @date: 2023-08-28
 */

public interface MicropageVisitMapper extends BaseMapper<MicropageVisit> {

    /**
     * 添加访问明细
     *
     * @param visitList 访问记录
     */
    void saveVisitLog(@Param("visitList") List<MicropageVisit> visitList);
}
