package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**   
 * 部门员工表 实体类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Data
@NoArgsConstructor
@TableName("hishop_department_employee")
public class DepartmentEmployee extends BaseEntity implements Serializable {

	private static final long serialVersionUID = 1L;

	/**
	 * 主键id
	 */
	@TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
    * 部门ID
    */
	private Long departmentId;
    
    /**
    * 用户ID
    */
	private Long userId;
    
    /**
    * 是否部门负责人
    */
	private Boolean izHead;
    

}
