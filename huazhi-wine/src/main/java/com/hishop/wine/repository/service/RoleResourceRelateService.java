package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.entity.RoleResourceRelate;

import java.util.List;

/**
 * @Description:角色资源关联表 服务类
 * @author: Hu<PERSON>ia<PERSON>
 * @since: 2023-06-17
 */
public interface RoleResourceRelateService extends IService<RoleResourceRelate> {

    /**
     * 通过角色id 删除关联关系
     *
     * @param roleId 角色id
     */
    void removeByRoleId(Long roleId);


    /**
     * 关联角色资源
     *
     * @param roleResourceRelateList 角色资源关系集合
     */
    void insertRoleResourceRelateBatch(List<RoleResourceRelate> roleResourceRelateList);

    /**
     * 根据角色id 查询权限id
     *
     * @param roleId 角色id
     * @return 权限id集合
     */
    List<Long> listResourceIdsByRoleId(Long roleId);

    /**
     * 判断角色是否有权限
     *
     * @param roleId     角色id
     * @param resourceId 资源id
     * @return 是否有权限
     */
    Integer checkResourceAuth(Long roleId, Long resourceId);
}