package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.wine.repository.dao.IdentityMapper;
import com.hishop.wine.repository.dto.PullNewSummaryDTO;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.param.PullNewSummaryParam;
import com.hishop.wine.repository.service.IdentityService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户身份表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Service
public class IdentityServiceImpl extends ServiceImpl<IdentityMapper, Identity> implements IdentityService {

    /**
     * 逻辑删除身份信息
     *
     * @param ids    身份id列表
     * @param userId 操作人id
     */
    @Override
    public void logicDeleteByIds(List<Long> ids, Long userId) {
        baseMapper.logicDeleteByIds(ids, userId);
    }

    /**
     * 根据用户id和身份类型获取身份信息
     *
     * @param userId       用户id
     * @param identityType 身份类型
     * @param moduleCode   模块编码
     * @return 身份信息
     */
    @Override
    public Identity getIdentity(Long userId, Integer identityType, String moduleCode) {
        return getOne(new LambdaQueryWrapper<Identity>()
                .eq(Identity::getUserId, userId)
                .eq(Identity::getIdentityType, identityType)
                .eq(Identity::getModuleCode, moduleCode)
                .eq(Identity::getIzDelete, DeleteFlagEnums.NO.getCode()));
    }

    @Override
    public Identity getByUserId(Long userId, Integer identityType) {
        LambdaQueryWrapper<Identity> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Identity::getUserId, userId)
                .eq(Identity::getIdentityType, identityType)
                .eq(Identity::getIzDelete, DeleteFlagEnums.NO.getCode());
        return this.getOne(queryWrapper);
    }

    /**
     * 获取拉新汇总
     *
     * @param param 查询参数
     * @return 拉新汇总
     */
    @Override
    public List<PullNewSummaryDTO> getPullNewSummary(PullNewSummaryParam param) {
        return baseMapper.getPullNewSummary(param);
    }
}