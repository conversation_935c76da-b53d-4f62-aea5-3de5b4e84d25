package com.hishop.wine.repository.dto.payment;

import lombok.Data;

/**
 * @description: 线下支付DTO
 * @author: chenzw
 * @date: 2024/1/9 11:02
 */
@Data
public class PaymentSettingOfflineDTO {

    /**
     * 支付设置ID
     */
    private Long paymentId;

    /**
     * 线下支付方式
     */
    private String offlineChannel;

    /**
     * 收款人姓名
     */
    private String beneficiaryName;

    /**
     * 收款人账号
     */
    private String beneficiaryAccount;

    /**
     * 银行卡号
     */
    private String bankCardNumber;

    /**
     * 开户银行名称
     */
    private String bankName;

    /**
     * 二维码信息
     */
    private String qrCode;
}
