package com.hishop.wine.repository.dto.payment;

import lombok.Data;

/**
 * 支付设置 返回对象
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */
@Data
public class PaymentSettingDTO {

    /**
     * 支付设置id
     */
    private Long id;

    /**
     * 支付类型
     */
    private String paymentType;

    /**
     * 支付方式名称
     */
    private String paymentName;

    /**
     * 关联小程序名称
     */
    private String relateMiniNames;
}
