package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.ContentModerationInfo;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.param.moderation.ContentModerationInfoParam;

import java.util.List;

/**
 * 内容审核明细表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */

public interface ContentModerationInfoService extends IService<ContentModerationInfo> {

    /**
     * 统计数量
     *
     * @param param 查询参数
     * @return 数量
     */
    Long count(ContentModerationInfoParam param);

    /**
     * 查询明细列表
     *
     * @param param 查询参数
     * @return 明细列表
     */
    List<ContentModerationInfo> list(ContentModerationInfoParam param);
}