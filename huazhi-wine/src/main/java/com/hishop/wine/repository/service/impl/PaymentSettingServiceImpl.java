package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.dto.payment.PaymentSettingDTO;
import com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO;
import com.hishop.wine.repository.entity.PaymentSetting;
import com.hishop.wine.repository.dao.PaymentSettingMapper;
import com.hishop.wine.repository.service.PaymentSettingService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**   
 * 支付设置 服务实现类
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Service
public class PaymentSettingServiceImpl extends ServiceImpl<PaymentSettingMapper, PaymentSetting> implements PaymentSettingService  {

    /**
     * 获取支付设置列表
     *
     * @return 获取支付设置列表
     */
    @Override
    public List<PaymentSettingDTO> listAll() {
        return baseMapper.listAll();
    }

    /**
     * 获取线下支付设置列表
     * @return 获取线下支付设置列表
     */
    @Override
    public List<PaymentSettingOfflineDTO> listForOfflineSelect(String moduleCode) {
        return baseMapper.listForOfflineSelect(moduleCode);
    }

    @Override
    public List<PaymentSettingOfflineDTO> listForOfflineSelectBySpecialCode(String moduleCode, String specialCode) {
        return baseMapper.listForOfflineSelectBySpecialCode(moduleCode, specialCode);
    }

    @Override
    public List<PaymentSettingOfflineDTO> listForOfflineSelectAll(String moduleCode) {
        return baseMapper.listForOfflineSelectAll(moduleCode);
    }
}