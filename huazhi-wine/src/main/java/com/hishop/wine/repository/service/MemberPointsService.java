package com.hishop.wine.repository.service;

import com.hishop.wine.repository.dto.points.MemberPointsDTO;
import com.hishop.wine.repository.dto.points.MemberPointsSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.param.MemberPointsParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.List;

/**   
 * 会员积分表 数据层服务类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

public interface MemberPointsService extends IService<MemberPoints> {

    /**
     * 分页获取 会员积分
     * @author: LiGuoQiang
     * @date: 2023-06-25
    */
    Page<MemberPointsDTO> qryPage(Page<MemberPoints> pageInfo, MemberPointsParam param);

    /**
     * 汇总 会员积分数据
     * @author: LiGuoQiang
     * @date: 2023-06-25
     */
    MemberPointsSummaryDTO summary(MemberPointsParam param);

    /**
     * 获取用户积分数据
     * <AUTHOR>
     * @date 2023/6/26
     */
    MemberPoints getUserPoints(Long userId, Integer identityType);

    List<MemberPoints> listAll();

}