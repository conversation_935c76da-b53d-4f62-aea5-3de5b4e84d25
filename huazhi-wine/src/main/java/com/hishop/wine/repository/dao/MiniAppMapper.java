package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.miniApp.MiniAppDTO;
import com.hishop.wine.repository.dto.miniApp.MiniAppModuleDTO;
import com.hishop.wine.repository.entity.MiniApp;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 小程序表 Mapper 接口
 *
 * @author: Hu<PERSON><PERSON><PERSON>
 * @date: 2023-06-21
 */
public interface MiniAppMapper extends BaseMapper<MiniApp> {

    /**
     * 查询小程序列表
     *
     * @return 小程序列表
     */
    List<MiniAppDTO> listMiniApp();

    /**
     * 查询绑定模块的名称
     *
     * @param appId 小程序appId
     * @return 绑定模块的名称
     */
    String getBindModuleNames(@Param("appId") String appId);

    /**
     * 查询小程序模块绑定关系
     *
     * @return 绑定关系
     */
    List<MiniAppModuleDTO> listMiniAppModule();
}
