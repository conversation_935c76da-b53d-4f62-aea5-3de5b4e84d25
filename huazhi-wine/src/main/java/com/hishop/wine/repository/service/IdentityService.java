package com.hishop.wine.repository.service;

import com.hishop.wine.repository.dto.PullNewSummaryDTO;
import com.hishop.wine.repository.entity.Identity;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.param.PullNewSummaryParam;

import java.util.List;

/**
 * 用户身份表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface IdentityService extends IService<Identity> {

    /**
     * 逻辑删除身份信息
     *
     * @param ids    身份id列表
     * @param userId 操作人id
     */
    void logicDeleteByIds(List<Long> ids, Long userId);

    /**
     * 根据用户id和身份类型获取身份信息
     *
     * @param userId       用户id
     * @param identityType 身份类型
     * @param moduleCode   模块编码
     * @return 身份信息
     */
    Identity getIdentity(Long userId, Integer identityType, String moduleCode);

    /**
     * 根据用户id和身份查询
     * @param userId
     * @return
     */
    Identity getByUserId(Long userId, Integer identityType);

    /**
     * 获取拉新汇总
     *
     * @param param 查询参数
     * @return 拉新汇总
     */
    List<PullNewSummaryDTO> getPullNewSummary(PullNewSummaryParam param);
}