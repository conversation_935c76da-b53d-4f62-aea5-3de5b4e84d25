package com.hishop.wine.repository.service;

import com.hishop.wine.repository.dto.miniApp.MiniAppDTO;
import com.hishop.wine.repository.dto.miniApp.MiniAppModuleDTO;
import com.hishop.wine.repository.entity.MiniApp;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 小程序表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface MiniAppService extends IService<MiniApp> {

    /**
     * 查询小程序列表
     *
     * @return 小程序列表
     */
    List<MiniAppDTO> listMiniApp();

    /**
     * 根据appId 获取小程序信息
     *
     * @param appId 小程序appId
     * @return 小程序信息
     */
    MiniApp getByAppId(String appId);

    /**
     * 查询绑定的模块名称
     *
     * @param appId 小程序appId
     * @return 绑定的模块名称
     */
    String getBindModuleNames(String appId);

    /**
     * 查询小程序模块绑定关系
     *
     * @return 绑定关系
     */
    List<MiniAppModuleDTO> listMiniAppModule();
}