package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 头衔表 实体类
 * @author: Hu<PERSON>iao
 * @date: 2023-07-25
 */

@Data
@NoArgsConstructor
@TableName("hishop_rank")
public class Rank implements Serializable {

	private static final long serialVersionUID = 1683682162531692544L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
	private Long id;
    
    /**
    * 头衔名称
    */
	private String rankName;

    /**
    * 模块编码
    */
	private String moduleCode;
    
    /**
    * 状态 0：禁用  1：正常
    */
	private Boolean status;
    
    /**
    * 是否删除 0:未删除 1:删除
    */
	private Boolean izDelete;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

    /**
     * 关联用户数
     */
    @TableField(exist = false)
    private Integer relateUserNum;

}
