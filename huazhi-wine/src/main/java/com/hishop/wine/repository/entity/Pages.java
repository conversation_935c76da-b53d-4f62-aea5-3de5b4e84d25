package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 页面配置表 实体类
 * @author: Hu<PERSON>iao
 * @date: 2023-07-07
 */

@Data
@NoArgsConstructor
@TableName("hishop_pages")
public class Pages implements Serializable {

	private static final long serialVersionUID = 1677242836671344640L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 模块编码
    */
	private String moduleCode;
    
    /**
    * 名称
    */
	private String name;
    
    /**
    * 请求地址
    */
	private String path;

    /**
     * 链接类型
     */
	private String linkType;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
