package com.hishop.wine.repository.service;

import com.hishop.wine.model.po.DepartmentEmployeeHeaderPO;
import com.hishop.wine.model.po.DepartmentUpdateIdPO;
import com.hishop.wine.repository.entity.DepartmentEmployee;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**   
 * @Description: 部门员工表 服务类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

public interface DepartmentEmployeeService extends IService<DepartmentEmployee> {

    List<DepartmentEmployee> list(DepartmentEmployee entity);

    /**
     * 统计部门下的员工数量
     * @param departmentId 部门ID
     * @return 员工数量
     */
    long countByDepartmentId(Long departmentId);

    /**
     * 获取指定部门和用户的DepartmentEmployee对象
     * @param departmentId 部门ID
     * @param userId 用户ID
     * @return DepartmentEmployee对象
     */
    DepartmentEmployee getByDepartmentIdAndUserId(Long departmentId, Long userId);


    /**
     * 根据用户id查询部门是否责人
     * @param userId
     * @return
     */
    DepartmentEmployee getByDepartmentIdAndUserId(Long userId);

    /**
     * 获取指定部门的负责人
     * @param departmentId 部门ID
     * @return DepartmentEmployee对象
     */
    DepartmentEmployee getHeaderByDepartmentId(Long departmentId);


    /**
     * 添加部门用户
     * @param entity
     * @return
     */
    boolean add(DepartmentEmployee entity);


    /**
     * 修改部门用户
     * @param entity
     * @return
     */
    boolean updateDep(DepartmentUpdateIdPO entity);

    /**
     * 设置部门负责人
     * @param headerPO
     */
    void setHeader(DepartmentEmployee entity);

    void deleteUserIds(List<Long> userIds);
}