package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.dto.points.MemberPointsDetailDTO;
import com.hishop.wine.repository.dto.points.PointsModifiedSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.entity.MemberPointsDetails;
import com.hishop.wine.repository.param.MemberPointsParam;

import java.util.Date;
import java.util.List;

/**   
 * 会员积分表 数据层服务类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

public interface MemberPointsDetailService extends IService<MemberPointsDetails> {

    /**
     * 分页获取 会员积分明细
     * <AUTHOR>
     * @date 2023/6/25
     */
    Page<MemberPointsDetailDTO> pageDetail(Page<MemberPoints> pageInfo, MemberPointsParam param);

    /**
     * 基于最后一次的明细ID获取指定数量的明细记录
     * <AUTHOR>
     * @date 2023/8/2
     */
    List<MemberPointsDetails> getLastDetail(Long userId, Integer identityType, String moduleCode, Integer modifyType, Long lastId, Integer size);

    /**
     * 汇总用户积分
     * <AUTHOR>
     * @date 2023/8/3
     */
    Integer getUserPointsSummary(Long userId, Integer identityType, Long minId, Date maxCreateTime);

    /**
     * 统计该时间段内的积分变动
     * @param userId 用户ID
     * @param identityType 身份类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 积分变动汇总
     */
    List<PointsModifiedSummaryDTO> summaryPointsModified(Long userId, Integer identityType, Date startTime, Date endTime);
}