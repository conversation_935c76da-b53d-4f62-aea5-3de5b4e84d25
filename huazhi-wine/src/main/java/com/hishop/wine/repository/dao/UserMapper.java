package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.repository.dto.member.MemberDTO;
import com.hishop.wine.repository.dto.member.MemberSelectDTO;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.AdminParam;
import com.hishop.wine.repository.param.UserCombineQryParam;
import com.hishop.wine.repository.param.member.MemberParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface UserMapper extends BaseMapper<User> {

    /**
     * 分页查询管理员列表
     *
     * @param page       分页参数
     * @param adminParam 管理员筛选参数
     * @return 管理员列表
     */
    Page<User> pageListAdmin(Page<User> page, @Param("param") AdminParam adminParam);

    /**
     * 分页查询管理员部门列表
     *
     * @param page       分页参数
     * @param adminParam 管理员筛选参数
     * @return 管理员列表
     */
    Page<AdminVO> pageListAdminDep(Page<User> page, @Param("param") AdminParam adminParam);


    /**
     * 根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户
     *
     * <AUTHOR>
     * @date 2023/6/30
     */
    List<Long> qryUserId(@Param("param") UserCombineQryParam param);

    /**
     * 分页查询会员列表
     *
     * @param page 分页参数
     * @param param 筛选参数
     * @return 会员列表
     */
    Page<MemberDTO> pageListMember(Page<MemberDTO> page, @Param("param") MemberParam param);

    /**
     * 查询会员列表
     *
     * @param param 筛选参数
     * @return 会员列表
     */
    List<MemberDTO> pageListMember(@Param("param") MemberParam param);

    /**
     * 查询会员下拉
     *
     * @param param 筛选值
     * @return 会员列表
     */
    List<MemberSelectDTO> listMemberSelect(@Param("param") MemberParam param);
}
