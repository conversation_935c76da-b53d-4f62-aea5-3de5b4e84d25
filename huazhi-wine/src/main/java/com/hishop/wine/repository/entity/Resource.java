package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 资源表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@NoArgsConstructor
@TableName("hishop_resource")
public class Resource implements Serializable {

    private static final long serialVersionUID = 4815913860136228709L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Long id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 上级资源id, 一级资源为0
     */
    private Long parentId;

    /**
     * 上级id的集合
     */
    private String parentIds;

    /**
     * 资源名称
     */
    private String name;

    /**
     * 请求地址(用于后端拦截)
     */
    private String path;

    /**
     * 资源编码(用于前端展示)
     */
    private String privilege;

    /**
     * 类型  0-系统 1：目录  2：菜单 3：按钮
     */
    private Integer type;

    /**
     * 图标
     */
    private String icon;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 状态 0：禁用  1：正常
     */
    private Boolean status;

    /**
     * 是否展示
     */
    private Boolean izShow;

    /**
     * 是否是tab
     */
    private Boolean izTab;

    /**
     * 是否分组
     */
    private Boolean izGroup;

    /**
     * 菜单布局 1-左侧菜单 2-顶部菜单
     */
    private Integer menuLayout;

    /**
     * 默认资源的id (只有type=0时才有值)
     */
    private Long defaultResourceId;

    /**
     * 默认资源path (只有type=0时才有值)
     */
    private String defaultResourcePath;

    /**
     * 逻辑删除 0-未删除 其余为已删除
     */
    private Long izDelete;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
