package com.hishop.wine.repository.dto;

import com.hishop.wine.model.vo.tags.TagsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/05/16/ $
 * @description:
 */
@Data
@ApiModel("包含我的标签对象")
public class MyTagsDto {

    @ApiModelProperty("我的标签")
    private List<TagsVO> myTags;

    @ApiModelProperty("top标签")
    private List<TagsVO> topTags;

}
