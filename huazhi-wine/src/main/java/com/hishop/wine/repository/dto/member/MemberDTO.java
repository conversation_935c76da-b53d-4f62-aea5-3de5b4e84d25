package com.hishop.wine.repository.dto.member;

import lombok.Data;

import java.util.Date;

/**
 * 会员列表返回值
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
@Data
public class MemberDTO {

    /**
     * 会员id
     */
    private Long id;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 头像
     */
    private String icon;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 头衔
     */
    private String rankName;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 头衔id
     */
    private Long rankId;

    /**
     * 头衔状态 true-启用 false-禁用
     */
    private Boolean rankStatus;

}
