package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Objects;

/**
 * 地区表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
@Data
@NoArgsConstructor
@TableName("hishop_district")
public class District implements Serializable {

    private static final long serialVersionUID = 1673177862474838016L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.INPUT)
    private Integer id;

    /**
     * 大区名称
     */
    private String regionName;

    /**
     * 地区名称
     */
    private String name;

    /**
     * 完整的名称
     */
    private String fullName;

    /**
     * 父级id
     */
    private Integer parentId;

    /**
     * 父级链
     */
    private String parentIds;

    /**
     * 经度
     */
    private BigDecimal lng;

    /**
     * 纬度
     */
    private BigDecimal lat;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 是否有子级
     */
    private Boolean hasChild;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        District district = (District) o;
        return Objects.equals(id, district.id) && Objects.equals(regionName, district.regionName) && Objects.equals(name, district.name) && Objects.equals(fullName, district.fullName) && Objects.equals(parentId, district.parentId) && Objects.equals(parentIds, district.parentIds) && Objects.equals(lng, district.lng) && Objects.equals(lat, district.lat) && Objects.equals(level, district.level) && Objects.equals(hasChild, district.hasChild);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id, regionName, name, fullName, parentId, parentIds, lng, lat, level, hasChild);
    }
}
