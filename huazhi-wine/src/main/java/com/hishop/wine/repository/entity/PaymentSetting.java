package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 支付设置 实体类
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Data
@NoArgsConstructor
@TableName("hishop_payment_setting")
public class PaymentSetting implements Serializable {

	private static final long serialVersionUID = 1681264747503951872L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
     * 商户id(各种支付方式的商户id)
     */
    private String merchantId;
    
    /**
    * 支付方式名称
    */
	private String paymentName;
    
    /**
    * 支付类型 WX_PAY-微信支付 ALI_PAY-支付宝支付 OFFLINE-线下支付
    */
	private String paymentType;

    /**
     * 线下支付渠道 WECHAT_TRANSFER-微信转账 ALI_TRANSFER-支付宝转账 BANK_CARD_TRANSFER-银行卡转账
     */
    private String offlineChannel;

    /**
     * 场景值
     */
	private String sceneValue;
    
    /**
    * 配置值（json格式）
    */
	private String settingValue;
    
    /**
    * 创建者
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;

    /**
     * 是否特殊收款公司
     */
    private Boolean izSpecial;

    /**
     * 收款公司编码
     */
    private String specialCode;
    

}
