package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.hishop.wine.common.enums.MaterialStatus;
import com.hishop.wine.common.enums.MaterialType;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.Date;

/**
 * 资源库表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@NoArgsConstructor
@TableName("hishop_material")
public class Material implements Serializable {

    private static final long serialVersionUID = 1669986857407791104L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 资源所属分组
     */
    private Long materialCategoryId;

    /**
     * 标题
     */
    private String title;

    /**
     * 资源路径
     */
    private String path;

    /**
     * 封面路径
     */
    private String bannerPath;

    /**
     * 资源类型
     */
    private Integer type;

    /**
     * 资源大小
     */
    private Long size;

    /**
     * 状态
     */
    private MaterialStatus status;

    /**
     * 转码任务id
     */
    private String jobId;

    /**
     * 使用次数
     */
    private Integer usedTimes;

    /**
     * 单视频长度(秒)
     */
    private Integer duration;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
