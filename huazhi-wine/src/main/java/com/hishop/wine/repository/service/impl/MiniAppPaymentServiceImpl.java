package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.MiniAppPayment;
import com.hishop.wine.repository.dao.MiniAppPaymentMapper;
import com.hishop.wine.repository.service.MiniAppPaymentService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**   
 * 小程序关联支付设置 服务实现类
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Service
public class MiniAppPaymentServiceImpl extends ServiceImpl<MiniAppPaymentMapper, MiniAppPayment> implements MiniAppPaymentService  {


}