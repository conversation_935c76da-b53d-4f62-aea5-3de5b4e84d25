package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.MicropageCategory;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface MicropageCategoryMapper extends BaseMapper<MicropageCategory> {

    List<MicropageCategory> listAllCategorys(@Param("name") String name);

    List<Long> recursionChildIds(@Param("pid") Long pid);
}
