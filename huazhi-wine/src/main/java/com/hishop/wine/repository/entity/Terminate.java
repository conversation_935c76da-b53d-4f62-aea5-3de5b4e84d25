package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 门店表(Terminate)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:43
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_terminate")
public class Terminate extends BaseEntity {

    /**
     * id
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 门店编码
     **/
    private String code;
    /**
     * 门店名称
     **/
    private String name;
    /**
     * 类型id
     **/
    private Long terminateTypeId;
    /**
     * 门店面积
     **/
    private Integer square;
    /**
     * 经销商id
     **/
    private Long dealerId;
    /**
     * 业务员
     **/
    private Long businessUserId;
    /**
     * 省份ID
     **/
    private Integer provinceId;
    /**
     * 省份名称
     **/
    private String provinceName;
    /**
     * 城市ID
     **/
    private Integer cityId;
    /**
     * 城市名称
     **/
    private String cityName;
    /**
     * 区县ID
     **/
    private Integer districtId;
    /**
     * 区县名称
     **/
    private String districtName;
    /**
     * 详细地址
     **/
    private String address;
    /**
     * 标注位置
     **/
    private String markLocation;
    /**
     * 经度
     **/
    private Double lng;
    /**
     * 纬度
     **/
    private Double lat;
    /**
     * 负责人姓名
     **/
    private String dutyName;
    /**
     * 手机号
     **/
    private String phone;
    /**
     * 电子邮箱
     **/
    private String mail;
    /**
     * 门店照片
     **/
    private String photo;
    /**
     * 门店电话
     **/
    private String storePhone;
    /**
     * 营业开始时间
     **/
    private String tradeStartTime;
    /**
     * 营业结束时间
     **/
    private String tradeEndTime;
    /**
     * 营业星期
     **/
    private String tradeWeek;
    /**
     * 简介
     **/
    private String introduce;

    /**
     * 是否启用
     **/
    private Boolean izEnable;

    /**
     * 审核状态
     **/
    private Integer auditStatus;

    /**
     * 审核备注
     **/
    private String auditRemark;

    /**
     * 数据来源
     **/
    private Integer source;
}

