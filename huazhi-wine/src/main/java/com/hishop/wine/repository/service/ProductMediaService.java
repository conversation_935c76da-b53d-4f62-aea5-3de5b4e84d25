package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.ProductMedia;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 产品媒体资源表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductMediaService extends IService<ProductMedia> {

    /**
     * 根据产品Id删除
     *
     * @param productId 产品Id
     */
    void removeByProductId(Long productId);

    /**
     * 关联媒体资源
     *
     * @param productMediaList 关联关系集合
     */
    void insertProductMediaBatch(List<ProductMedia> productMediaList);
}