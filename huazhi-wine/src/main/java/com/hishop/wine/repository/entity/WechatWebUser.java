package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 微信网页应用用户表 实体类
 * @author: HuBiao
 * @date: 2023-07-10
 */

@Data
@NoArgsConstructor
@TableName("hishop_wechat_web_user")
public class WechatWebUser implements Serializable {

	private static final long serialVersionUID = 1678279152355778560L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 用户id
    */
	private Long userId;
    
    /**
    * 昵称
    */
	private String nickName;
    
    /**
    * 微信头像
    */
	private String avatarUrl;
    
    /**
    * 网页应用app_id(冗余字段)
    */
	private String appId;
    
    /**
    * 注册模块编码(冗余字段)
    */
	private String registerModuleCode;
    
    /**
    * unionid
    */
	private String unionId;
    
    /**
    * openid
    */
	private String openId;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
