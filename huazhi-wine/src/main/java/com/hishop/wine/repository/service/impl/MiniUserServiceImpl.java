package com.hishop.wine.repository.service.impl;

import com.hishop.wine.model.po.minUser.MiniUserBlacklistQueryPo;
import com.hishop.wine.model.po.minUser.MiniUserQueryPo;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.dao.MiniUserMapper;
import com.hishop.wine.repository.service.MiniUserService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;

import java.util.List;

/**
 * 小程序用户表 服务实现类
 *
 * @author: Hu<PERSON>iao
 * @date: 2023-06-21
 */
@Service
public class MiniUserServiceImpl extends ServiceImpl<MiniUserMapper, MiniUser> implements MiniUserService {

    @Override
    public List<MiniUser> getMiniUser(List<Long> userIdList) {
        LambdaQueryWrapper<MiniUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MiniUser::getUserId, userIdList);
        return super.list(wrapper);
    }

    @Override
    public MiniUserVO getMiniUserByUserIdAndModuleCode(Long userId, String moduleCode) {
        return baseMapper.getMiniUserByUserIdAndModuleCode(userId, moduleCode);
    }

    /**
     * 根据userId获取小程序用户信息
     *
     * @param userId 用户id
     * @param appId  小程序appId
     * @return 小程序用户信息
     */
    @Override
    public MiniUser getMiniUser(Long userId, String appId) {
        return getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getUserId, userId).eq(MiniUser::getAppId, appId));
    }

    @Override
    public Page<MiniUserVO> pageList(Page<MiniUserVO> pageInfo, MiniUserQueryPo miniUserQueryPo) {
        return baseMapper.queryPage(pageInfo, miniUserQueryPo);
    }

    @Override
    public MiniUserVO getUserDetail(Long id) {
        return baseMapper.getUserDetail(id);
    }

    @Override
    public Page<MiniUserBlacklistVo> blacklistPage(Page<MiniUserBlacklistVo> pageInfo, MiniUserBlacklistQueryPo miniUserBlacklistQueryPo) {
        return baseMapper.blacklistPage(pageInfo, miniUserBlacklistQueryPo);
    }

    @Override
    public Long getMiniUserCount() {
        return baseMapper.getMiniUserCount();
    }
}