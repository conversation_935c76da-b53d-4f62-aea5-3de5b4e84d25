package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.hishop.common.pojo.entity.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 销售维度划分(SaleDim)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:07:46
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_sale_dim")
public class SaleDim extends BaseEntity {

    /**
     * 主键id
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 名称
     **/
    private String name;
    /**
     * 级别
     **/
    private Integer level;
}

