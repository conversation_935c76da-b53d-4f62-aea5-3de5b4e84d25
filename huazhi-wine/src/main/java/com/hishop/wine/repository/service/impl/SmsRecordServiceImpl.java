package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.SmsRecord;
import com.hishop.wine.repository.dao.SmsRecordMapper;
import com.hishop.wine.repository.service.SmsRecordService;
import com.hishop.wine.repository.param.SmsRecordParam;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**   
 * 短信发送记录表 服务实现类
 * @author: HuBiao
 * @date: 2023-07-12
 */

@Service
public class SmsRecordServiceImpl extends ServiceImpl<SmsRecordMapper, SmsRecord> implements SmsRecordService  {

    /**
     * 分页获取短信发送记录
     *
     * @param pageInfo 分页参数
     * @param param 筛选参数
     * @return 短信发送记录列表
     */
    @Override
    public Page<SmsRecord> qryPage(Page<SmsRecord> pageInfo, SmsRecordParam param) {
        return baseMapper.qryPage(pageInfo, param);
    }

}