package com.hishop.wine.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import com.hishop.wine.repository.entity.Dealer;

/**
 * 经销商表(Dealer)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-04 15:53:16
 */
public interface DealerService extends IService<Dealer> {

    /**
     * 经销商分页列表
     * @param dealerQueryPo 查询条件
     * @return List<DealerVo>
     */
    Page<DealerVo> queryDealerPageList(DealerQueryPo dealerQueryPo);
}

