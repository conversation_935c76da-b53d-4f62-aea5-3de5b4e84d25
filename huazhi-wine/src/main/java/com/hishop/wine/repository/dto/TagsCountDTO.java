package com.hishop.wine.repository.dto;

import com.hishop.wine.model.vo.tags.TagsVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 2024/05/16/ $
 * @description:
 */
@Data
@ApiModel("标签统计对象")
public class TagsCountDTO {

    @ApiModelProperty("用户id")
    private Long userId;

    @ApiModelProperty("用户已有标签数量")
    private Integer num;

}
