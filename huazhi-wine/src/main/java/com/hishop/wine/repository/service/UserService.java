package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.repository.dto.member.MemberDTO;
import com.hishop.wine.repository.dto.member.MemberSelectDTO;
import com.hishop.wine.repository.entity.User;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.param.AdminParam;
import com.hishop.wine.repository.param.UserCombineQryParam;
import com.hishop.wine.repository.param.member.MemberParam;

import java.util.List;

/**
 * @Description:用户表 服务类
 * @author: Hu<PERSON>iao
 * @since: 2023-06-17
 */
public interface UserService extends IService<User> {

    /**
     * 通过手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    User getUserByMobile(String mobile);

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    User getUserByUsername(String username);

    /**
     * 分页查询管理员列表
     *
     * @param page       分页参数
     * @param adminParam 管理员筛选参数
     * @return 管理员列表
     */
    Page<User> pageListAdmin(Page<User> page, AdminParam adminParam);


    /**
     * 分页查询管理员部门列表
     *
     * @param page       分页参数
     * @param adminParam 管理员筛选参数
     * @return 管理员列表
     */
    Page<AdminVO> pageListAdminDep(Page<User> page, AdminParam adminParam);


    /**
     * 根据条件关联组合查询用户ID，可能会关联用户+身份+小程序用户
     *
     * <AUTHOR>
     * @date 2023/6/30
     */
    List<Long> qryUserId(UserCombineQryParam param);

    /**
     * 分页查询会员列表
     *
     * @param page  分页参数
     * @param param 筛选参数
     * @return 会员列表
     */
    Page<MemberDTO> pageListMember(Page<MemberDTO> page, MemberParam param);

    /**
     * 查询会员列表
     *
     * @param param 筛选参数
     * @return 会员列表
     */
    List<MemberDTO> listMember(MemberParam param);

    /**
     * 查询会员下拉选择
     *
     * @param param 筛选值
     * @return 会员列表
     */
    List<MemberSelectDTO> listMemberSelect(MemberParam param);

    List<User> listUserByUserIds(List<Long> userIds);

    /**
     * 根据账号获取用户信息
     *
     * @param account 账号(手机号/邮箱/用户名)
     * @return 用户信息
     */
    User getUserByAccount(String account);
}