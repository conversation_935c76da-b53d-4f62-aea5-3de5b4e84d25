package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.TerminateTypeMapper;
import com.hishop.wine.repository.entity.TerminateType;
import com.hishop.wine.repository.service.TerminateTypeService;
import org.springframework.stereotype.Service;

/**
 * 门店类型表(TerminateType)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:24
 */
@Service
public class TerminateTypeServiceImpl extends ServiceImpl<TerminateTypeMapper, TerminateType> implements TerminateTypeService {

}

