package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.Role;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @Description:角色表 服务类
 * @author: HuBiao
 * @since: 2023-06-17
 */
public interface RoleService extends IService<Role> {

    /**
     * 查询所有角色信息(包括用户数量)
     *
     * @return 角色信息
     */
    List<Role> listRoleIncludeUserNum();

}