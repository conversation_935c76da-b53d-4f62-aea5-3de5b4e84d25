package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@NoArgsConstructor
@TableName("hishop_product")
public class Product implements Serializable {

    private static final long serialVersionUID = 1670672860099493888L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品类型 1-商品 2-礼品
     */
    private Integer productType;

    /**
     * 产品名称
     */
    private String productName;

    /**
     * 产品首图
     */
    private String productImg;

    /**
     * 产品编码
     */
    private String productCode;

    /**
     * 产品单位
     */
    private String productUnit;

    /**
     * 产品分类id
     */
    private Long productCategoryId;

    /**
     * 市场价格
     */
    private BigDecimal marketPrice;

    /**
     * 状态 0：禁用  1：正常 (暂未使用)
     */
    private Boolean status;

    /**
     * 逻辑删除 0-未删除 其余为已删除
     */
    private Long izDelete;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
