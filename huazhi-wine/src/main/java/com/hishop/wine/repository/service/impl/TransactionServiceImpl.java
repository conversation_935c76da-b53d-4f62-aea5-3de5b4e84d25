package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.repository.dto.transaction.TransactionTotalDto;
import com.hishop.wine.repository.entity.Transaction;
import com.hishop.wine.repository.dao.TransactionMapper;
import com.hishop.wine.repository.param.transaction.TransactionParam;
import com.hishop.wine.repository.service.TransactionService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * 交易流水表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Service
public class TransactionServiceImpl extends ServiceImpl<TransactionMapper, Transaction> implements TransactionService {

    /**
     * 判断交易是否成功
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 是否成功
     */
    @Override
    public Boolean izTransactionSuccess(String bizType, String bizCode) {
        return countByStatus(bizType, bizCode, TransactionEnum.Status.SUCCESS.getStatus()) > 0;
    }

    /**
     * 判断交易待确认
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 是否待确认
     */
    @Override
    public Boolean izTransactionWaiting(String bizType, String bizCode) {
        return countByStatus(bizType, bizCode, TransactionEnum.Status.WAITING.getStatus()) > 0;
    }

    /**
     * 根据状态统计交易记录
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @param status  状态
     * @return 交易数量
     */
    @Override
    public Long countByStatus(String bizType, String bizCode, Integer status) {
        return count(new LambdaQueryWrapper<Transaction>().eq(Transaction::getBizType, bizType)
                .eq(Transaction::getBizCode, bizCode).eq(Transaction::getStatus, status));
    }

    /**
     * 重置退款信息
     *
     * @param transactionId 交易流水id
     */
    @Override
    public void resetRefundData(Long transactionId) {
        baseMapper.resetRefundData(transactionId);
    }

    /**
     * 查询交易流水
     *
     * @param page  分页参数
     * @param param 查询参数
     * @return 交易流水
     */
    @Override
    public Page<Transaction> queryTransaction(Page<Transaction> page, TransactionParam param) {
        LambdaQueryWrapper<Transaction> wrapper = new LambdaQueryWrapper<Transaction>()
                .eq(StrUtil.isNotEmpty(param.getBizType()), Transaction::getBizType, param.getBizType())
                .eq(StrUtil.isNotEmpty(param.getBizCode()), Transaction::getBizCode, param.getBizCode())
                .eq(ObjectUtil.isNotNull(param.getStatus()), Transaction::getStatus, param.getStatus());
        return baseMapper.selectPage(page, wrapper);
    }

    @Override
    public TransactionTotalDto getTransactionTotal(String startTime, String endTime) {
        return baseMapper.getTransactionTotal(startTime, endTime);
    }

    @Override
    public List<TransactionTotalDto> queryTransactionTotalList(String startTime, String endTime) {
        return baseMapper.queryTransactionTotalList(startTime, endTime);
    }
}