package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.hishop.wine.model.po.DepartmentUpdateIdPO;
import com.hishop.wine.repository.entity.DepartmentEmployee;
import com.hishop.wine.repository.dao.DepartmentEmployeeMapper;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.service.DepartmentEmployeeService;
import com.hishop.wine.repository.service.IdentityService;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import java.util.List;

/**   
 * @Description: 部门员工表 服务实现类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Service
public class DepartmentEmployeeServiceImpl extends ServiceImpl<DepartmentEmployeeMapper, DepartmentEmployee> implements DepartmentEmployeeService  {

    @Override
    public List<DepartmentEmployee> list(DepartmentEmployee entity) {
     return null;
    }

    @Override
    public long countByDepartmentId(Long departmentId) {
        LambdaQueryWrapper<DepartmentEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentEmployee::getDepartmentId, departmentId)
                .eq(DepartmentEmployee::getIzDelete, false);
        return this.count(queryWrapper);
    }

    @Override
    public DepartmentEmployee getByDepartmentIdAndUserId(Long departmentId, Long userId) {
        LambdaQueryWrapper<DepartmentEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentEmployee::getDepartmentId, departmentId)
                .eq(DepartmentEmployee::getUserId, userId)
                .eq(DepartmentEmployee::getIzDelete, false);
        return this.getOne(queryWrapper);
    }

    @Override
    public DepartmentEmployee getByDepartmentIdAndUserId(Long userId) {
        LambdaQueryWrapper<DepartmentEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentEmployee::getUserId, userId)
                .eq(DepartmentEmployee::getIzDelete, false)
                .last("limit 1");
        return this.getOne(queryWrapper);
    }

    @Override
    public DepartmentEmployee getHeaderByDepartmentId(Long departmentId) {
        LambdaQueryWrapper<DepartmentEmployee> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentEmployee::getDepartmentId, departmentId)
                .eq(DepartmentEmployee::getIzHead, true)
                .eq(DepartmentEmployee::getIzDelete, false);
        return this.getOne(queryWrapper);
    }

    @Override
    public boolean add(DepartmentEmployee entity) {
        return this.baseMapper.add(entity)>0;
    }

    @Override
    public boolean updateDep(DepartmentUpdateIdPO entity) {
        if (CollectionUtils.isEmpty(entity.getUserIds())){
            return false;
        }
        return this.baseMapper.updateDepByUserId(entity)>0;
    }

    @Override
    public void setHeader(DepartmentEmployee entity) {
        LambdaUpdateWrapper<DepartmentEmployee> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(DepartmentEmployee::getDepartmentId, entity.getDepartmentId())
                .eq(DepartmentEmployee::getUserId, entity.getUserId())
                .set(DepartmentEmployee::getIzHead, entity.getIzHead());
        this.update(updateWrapper);
    }

    @Override
    public void deleteUserIds(List<Long> userIds) {
        LambdaUpdateWrapper<DepartmentEmployee> updateWrapper = new LambdaUpdateWrapper();
        updateWrapper.in(DepartmentEmployee::getUserId, userIds);
        this.remove(updateWrapper);
    }
}