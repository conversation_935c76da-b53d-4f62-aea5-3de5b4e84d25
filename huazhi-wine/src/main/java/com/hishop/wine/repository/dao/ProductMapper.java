package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.handler.EasyBaseMapper;
import com.hishop.wine.repository.dto.ProductPageDTO;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.param.ProductParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductMapper extends EasyBaseMapper<Product> {

    /**
     * 分页查询产品列表
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 产品列表
     */
    Page<ProductPageDTO> queryProductPage(Page<Product> pageInfo, @Param("param") ProductParam param);

    /**
     * 逻辑删除产品
     *
     * @param ids 产品id的集合
     * @param userId 操作人id
     */
    void logicDeleteByIds(@Param("ids") List<Long> ids, @Param("userId") Long userId);
}
