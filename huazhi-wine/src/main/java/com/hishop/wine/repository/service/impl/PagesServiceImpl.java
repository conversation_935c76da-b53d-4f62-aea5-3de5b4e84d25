package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.Pages;
import com.hishop.wine.repository.dao.PagesMapper;
import com.hishop.wine.repository.service.PagesService;
import com.hishop.wine.repository.param.PagesParam;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import java.util.List;

/**   
 * 页面配置表 服务实现类
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Service
public class PagesServiceImpl extends ServiceImpl<PagesMapper, Pages> implements PagesService  {

    public Page<Pages> qryPage(Page<Pages> pageInfo, PagesParam param) {
        LambdaQueryWrapper<Pages> wrapper = new LambdaQueryWrapper<>();
        return this.page(pageInfo, wrapper);
    }

    public List<Pages> qryList(PagesParam param) {
        LambdaQueryWrapper<Pages> wrapper = new LambdaQueryWrapper<>();
        return super.list(wrapper);
    }
}