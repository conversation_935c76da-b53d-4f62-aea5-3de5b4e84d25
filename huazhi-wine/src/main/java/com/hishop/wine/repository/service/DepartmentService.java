package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.Department;
import com.baomidou.mybatisplus.extension.service.IService;
import java.util.List;

/**   
 * @Description: 部门表 服务类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

public interface DepartmentService extends IService<Department> {

    List<Department> listAll();

    /**
     * 查询部门下面，部门名称相同的部门
     * @return 部门实体
     */
    Department getByNameAndParent(Long parentId, String departmentName);

    /**
     * 逻辑删除部门
     * @param id 部门ID
     */
    void logicalDeleteById(Long id);

    /**
     * 获取指定父部门下的最大formId
     * @param parentId 父部门ID
     * @return 最大formId
     */
    Integer getMaxFormIdByParentId(Long parentId);


    /**
     * 获取指定父部门下的最大formId
     * @param parentId 父部门ID
     * @return 最大formId
     */
    Department getMaxByParentId(Long parentId);

    /**
     * 父级部门id查询下级所有部门名称
     * @param parentId
     * @return
     */
    List<String> getDepartmentNameByParentId(Long parentId);
}