package com.hishop.wine.repository.param;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 拉新查询参数
 *
 * <AUTHOR>
 * @date : 2023/8/28
 */
@Data
public class PullNewSummaryParam {

    /**
     * 注册渠道
     */
    private String registerChannel;

    /**
     * 邀请人id的集合
     */
    private List<Long> inviterUserIdList;

    /**
     * 注册业务码
     */
    private List<String> bizCodeList;

    /**
     * 注册时间
     */
    private Date registerTime;

    /**
     * 合计维度 1-按邀请人 2-按业务码 3-综合
     */
    private Integer totalDimension;

}
