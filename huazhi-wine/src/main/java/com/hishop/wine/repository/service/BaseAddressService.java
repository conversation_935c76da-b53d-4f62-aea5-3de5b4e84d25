package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.BaseAddress;
import com.hishop.wine.repository.param.BaseAddressParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 地址库表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-07-17
 */
public interface BaseAddressService extends IService<BaseAddress> {

    /**
     * 分页获取 地址库表
     *
     * @param pageInfo 分页信息
     * @param param    查询参数
     * @return 地址库表分页数据
     */
    Page<BaseAddress> qryPage(Page<BaseAddress> pageInfo, BaseAddressParam param);

    /**
     * 获取 地址库表 列表
     *
     * @param param 查询参数
     * @return 地址库表列表
     */
    List<BaseAddress> qryList(BaseAddressParam param);

    /**
     * 逻辑删除地址
     *
     * @param id 地址id
     */
    void logicRemoveById(Long id);
}