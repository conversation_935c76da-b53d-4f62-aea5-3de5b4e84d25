package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import com.hishop.wine.repository.dao.FileImportRecordMapper;
import com.hishop.wine.repository.entity.FileImportRecord;
import com.hishop.wine.repository.service.FileImportRecordService;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 导入记录表(ImportRecord)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-05 16:16:32
 */
@Service
public class FileImportRecordServiceImpl extends ServiceImpl<FileImportRecordMapper, FileImportRecord> implements FileImportRecordService {

    @Override
    public void updateFileImportRecord(FileImportRecord record, long cost, Integer status, String errMsg, Integer successCount, Integer failCount, String filePath) {
        LambdaUpdateWrapper<FileImportRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(FileImportRecord::getId, record.getId())
                .set(FileImportRecord::getCostTime, cost)
                .set(FileImportRecord::getImportStatus, status)
                .set(StrUtil.isNotBlank(errMsg), FileImportRecord::getErrMsg, errMsg)
                .set(Objects.nonNull(successCount), FileImportRecord::getSuccessCount, successCount)
                .set(Objects.nonNull(failCount), FileImportRecord::getFailCount, failCount)
                .set(StrUtil.isNotBlank(filePath), FileImportRecord::getErrFilePath, filePath);
        super.update(wrapper);
    }

    @Override
    public Page<FileImportRecordVo> queryFileImportCodePageList(Page<FileImportRecordVo> page, FileImportRecordQueryPo fileImportRecordQueryPo) {
        return baseMapper.queryFileImportCodePageList(page, fileImportRecordQueryPo);
    }
}

