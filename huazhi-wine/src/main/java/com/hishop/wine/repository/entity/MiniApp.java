package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 小程序表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Data
@NoArgsConstructor
@TableName("hishop_mini_app")
public class MiniApp implements Serializable {

    private static final long serialVersionUID = 1671412744896397312L;

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * app_id
     */
    private String appId;

    /**
     * 小程序原始id
     */
    private String originalId;

    /**
     * 小程序名称
     */
    private String appName;

    /**
     * app_secret
     */
    private String appSecret;

    /**
     * 微信小程序消息服务器配置的token
     */
    private String token;

    /**
     * 商户号id(弃用)
     */
    private String mchId;

    /**
     * 微信小程序消息服务器配置的EncodingAESKey
     */
    private String aesKey;

    /**
     * 消息格式，XML或者JSON
     */
    private String msgDataFormat;

    /**
     * 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     */
    private String envVersion;

    /**
     * 小程序和底部导航采用的模编码
     */
    private String settingModuleCode;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
