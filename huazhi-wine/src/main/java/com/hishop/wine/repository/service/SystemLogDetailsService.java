package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.SystemLogDetails;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 操作日志明细表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-09-15
 */

public interface SystemLogDetailsService extends IService<SystemLogDetails> {

    /**
     * 根据日志id查询日志明细
     *
     * @param logId 日志id
     * @return 日志明细
     */
    SystemLogDetails getByLogId(Long logId);

}