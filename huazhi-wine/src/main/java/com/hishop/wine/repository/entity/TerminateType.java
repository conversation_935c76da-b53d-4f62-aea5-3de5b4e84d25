package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 门店类型表(TerminateType)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-06 14:47:24
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_terminate_type")
public class TerminateType extends BaseEntity {

    /**
     * id
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 类型
     **/
    private String name;
}

