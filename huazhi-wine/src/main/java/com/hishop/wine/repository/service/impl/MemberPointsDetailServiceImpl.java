package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.MemberPointsDetailsMapper;
import com.hishop.wine.repository.dto.points.MemberPointsDetailDTO;
import com.hishop.wine.repository.dto.points.PointsModifiedSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.entity.MemberPointsDetails;
import com.hishop.wine.repository.param.MemberPointsParam;
import com.hishop.wine.repository.service.MemberPointsDetailService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**   
 * 会员积分表 服务实现类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Service
public class MemberPointsDetailServiceImpl extends ServiceImpl<MemberPointsDetailsMapper, MemberPointsDetails> implements MemberPointsDetailService {

    @Resource
    private MemberPointsDetailsMapper memberPointsDetailsMapper;

    @Override
    public Page<MemberPointsDetailDTO> pageDetail(Page<MemberPoints> pageInfo, MemberPointsParam param) {
        return memberPointsDetailsMapper.qryDetail(pageInfo, param);
    }

    @Override
    public List<MemberPointsDetails> getLastDetail(Long userId, Integer identityType, String moduleCode, Integer modifyType, Long lastId, Integer size) {
        LambdaQueryWrapper<MemberPointsDetails> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MemberPointsDetails::getUserId, userId)
                .eq(MemberPointsDetails::getIdentityType, identityType)
                .eq(StrUtil.isNotBlank(moduleCode), MemberPointsDetails::getModuleCode, moduleCode)
                .ge(MemberPointsDetails::getId, lastId)
                .orderByAsc(MemberPointsDetails::getId)
                .last("limit " + size);
        return super.list(queryWrapper);
    }

    @Override
    public Integer getUserPointsSummary(Long userId, Integer identityType, Long minId, Date maxCreateTime) {
        return memberPointsDetailsMapper.getUserIncreasePointsSummary(userId, identityType, minId, maxCreateTime);
    }

    @Override
    public List<PointsModifiedSummaryDTO> summaryPointsModified(Long userId, Integer identityType, Date startTime, Date endTime) {
        return memberPointsDetailsMapper.summaryPointsModified(userId, identityType, startTime, endTime);
    }
}