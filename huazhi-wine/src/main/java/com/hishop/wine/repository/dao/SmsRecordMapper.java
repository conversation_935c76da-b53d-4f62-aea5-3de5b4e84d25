package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.entity.SmsRecord;
import com.hishop.wine.repository.param.SmsRecordParam;
import org.apache.ibatis.annotations.Param;

/**   
 * 短信发送记录表 Mapper 接口
 * @author: HuBiao
 * @date: 2023-07-12
 */

public interface SmsRecordMapper extends BaseMapper<SmsRecord> {

    /**
     * 分页获取短信发送记录
     *
     * @param pageInfo 分页参数
     * @param param 筛选参数
     * @return 短信发送记录列表
     */
    Page<SmsRecord> qryPage(Page<SmsRecord> pageInfo, @Param("param") SmsRecordParam param);
}
