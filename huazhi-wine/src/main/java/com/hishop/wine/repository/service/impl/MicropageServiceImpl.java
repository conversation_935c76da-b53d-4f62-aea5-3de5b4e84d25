package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.repository.dao.MicropageMapper;
import com.hishop.wine.repository.entity.Micropage;
import com.hishop.wine.repository.service.MicropageService;
import org.springframework.stereotype.Service;

import java.util.Map;


@Service
public class MicropageServiceImpl extends ServiceImpl<MicropageMapper, Micropage> implements MicropageService {

    /**
     * 更新访问次数
     *
     * @param visitMap 微页面id和访问次数
     */
    @Override
    public void updateVisitCount(Map<Integer, Long> visitMap) {
        baseMapper.updateVisitCount(visitMap);
    }

    @Override
    public Long countFile(Integer status, Long categoryId) {
        return baseMapper.countFile(status, categoryId);
    }
}
