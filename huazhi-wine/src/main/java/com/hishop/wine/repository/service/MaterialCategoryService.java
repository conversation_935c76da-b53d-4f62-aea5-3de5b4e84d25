package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.MaterialCategory;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**   
 * @Description:资源分组表 服务类
 *
 * @author: <PERSON><PERSON><PERSON><PERSON>
 * @since: 2023-06-17
 */
public interface MaterialCategoryService extends IService<MaterialCategory> {

    void batchMove(Long targetId, List<Long> ids);

    List<MaterialCategory> listAllCategories(Integer materialType);

    List<Long> listChildCategoryIds(Long materialCategoryId);
}