package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.ProductCategory;
import com.hishop.wine.repository.param.ProductCategoryParam;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * 产品分类表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
public interface ProductCategoryService extends IService<ProductCategory> {

    /**
     * 分页获取产品分类
     *
     * @param pageInfo 分类参数
     * @param param    筛选参数
     * @return 分页分类列表
     */
    Page<ProductCategory> qryPage(Page<ProductCategory> pageInfo, ProductCategoryParam param);

    /**
     * 逻辑删除产品分类
     *
     * @param id 产品分类id
     */
    void logicDeleteById(Long id);
}