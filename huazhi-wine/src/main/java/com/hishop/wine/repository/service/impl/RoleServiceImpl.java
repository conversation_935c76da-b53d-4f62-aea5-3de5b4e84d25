package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.Role;
import com.hishop.wine.repository.dao.RoleMapper;
import com.hishop.wine.repository.service.RoleService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

import java.util.List;

/**
 * 角色表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Service
public class RoleServiceImpl extends ServiceImpl<RoleMapper, Role> implements RoleService {

    /**
     * 查询所有角色信息(包括用户数量)
     *
     * @return 角色信息
     */
    @Override
    public List<Role> listRoleIncludeUserNum() {
        return baseMapper.listRoleIncludeUserNum();
    }
}