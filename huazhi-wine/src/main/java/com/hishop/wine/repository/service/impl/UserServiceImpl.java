package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.repository.dao.UserMapper;
import com.hishop.wine.repository.dto.member.MemberDTO;
import com.hishop.wine.repository.dto.member.MemberSelectDTO;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.AdminParam;
import com.hishop.wine.repository.param.UserCombineQryParam;
import com.hishop.wine.repository.param.member.MemberParam;
import com.hishop.wine.repository.service.UserService;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    /**
     * 通过手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户信息
     */
    @Override
    public User getUserByMobile(String mobile) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, mobile));
    }

    /**
     * 根据用户名获取用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Override
    public User getUserByUsername(String username) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getUsername, username));
    }

    /**
     * 分页查询管理员列表
     *
     * @param page       分页参数
     * @param adminParam 管理员筛选参数
     * @return 管理员列表
     */
    @Override
    public Page<User> pageListAdmin(Page<User> page, AdminParam adminParam) {
        return baseMapper.pageListAdmin(page, adminParam);
    }

    @Override
    public Page<AdminVO> pageListAdminDep(Page<User> page, AdminParam adminParam) {
        return baseMapper.pageListAdminDep(page, adminParam);

    }

    @Override
    public List<Long> qryUserId(UserCombineQryParam param) {
        return baseMapper.qryUserId(param);
    }

    /**
     * 分页查询会员列表
     *
     * @param page  分页参数
     * @param param 筛选参数
     * @return 会员列表
     */
    @Override
    public Page<MemberDTO> pageListMember(Page<MemberDTO> page, MemberParam param) {
        return baseMapper.pageListMember(page, param);
    }

    /**
     * 查询会员列表
     *
     * @param param 筛选参数
     * @return 会员列表
     */
    @Override
    public List<MemberDTO> listMember(MemberParam param) {
        return baseMapper.pageListMember(param);
    }

    /**
     * 查询会员下拉选择
     *
     * @param memberParam 筛选值
     * @return 会员列表
     */
    @Override
    public List<MemberSelectDTO> listMemberSelect(MemberParam memberParam) {
        return baseMapper.listMemberSelect(memberParam);
    }

    @Override
    public List<User> listUserByUserIds(List<Long> userIds) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(User::getId, userIds);
        return super.list(wrapper);
    }

    /**
     * 根据账号获取用户信息
     *
     * @param account 账号(手机号/邮箱/用户名)
     * @return 用户信息
     */
    @Override
    public User getUserByAccount(String account) {
        return getOne(new LambdaQueryWrapper<User>().eq(User::getMobile, account)
                .or().eq(User::getEmail, account).or().eq(User::getUsername, account));
    }
}