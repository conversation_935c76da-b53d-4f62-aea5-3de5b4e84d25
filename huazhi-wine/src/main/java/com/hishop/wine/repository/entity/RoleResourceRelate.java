package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 角色资源关联表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@NoArgsConstructor
@TableName("hishop_role_resource")
public class RoleResourceRelate implements Serializable {

    private static final long serialVersionUID = 8895487964932949741L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 资源id
     */
    private Long resourceId;

    public static RoleResourceRelate of(Long roleId, Long resourceId) {
        RoleResourceRelate roleResourceRelate = new RoleResourceRelate();
        roleResourceRelate.setRoleId(roleId);
        roleResourceRelate.setResourceId(resourceId);
        return roleResourceRelate;
    }
}
