package com.hishop.wine.repository.param.log;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 操作日志表 数据库查询类
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Data
@Builder
public class SystemLogParam {


	private Long id;
    

	private String moduleCode;
    

	private String businessSector;
    

	private String operationName;
    

	private String businessKey;


	private String businessDesc;
    

	private String requestParam;
    

	private String response;
    

	private String primaryList;
    

	private String oldDataList;
    

	private String newDataList;
    

	private String changeDataList;
    

	private Long createBy;
    

	private Date createTime;


	private String operationUsername;


	private Date startTime;


	private Date endTime;
    

}
