package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.ContentModeration;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.param.moderation.ContentModerationParam;

import java.util.List;

/**
 * 内容审核表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */

public interface ContentModerationService extends IService<ContentModeration> {

    /**
     * 分页获取 内容审核表
     *
     * @author: HuBiao
     * @date: 2023-09-11
     */
    Page<ContentModeration> qryPage(Page<ContentModeration> pageInfo, ContentModerationParam param);

    /**
     * 获取 内容审核表 列表
     *
     * @author: HuBiao
     * @date: 2023-09-11
     */
    List<ContentModeration> qryList(ContentModerationParam param);

    /**
     * 更新审核状态
     *
     * @param id     内容审核id
     * @param status 审核状态
     */
    void updateStatus(Long id, String status);

    /**
     * 统计数量
     *
     * @param param 筛选参数
     * @return 数量
     */
    Long count(ContentModerationParam param);

    /**
     * 刷新审核状态
     *
     * @param id 内容审核id
     */
    void refreshStatus(Long id);
}