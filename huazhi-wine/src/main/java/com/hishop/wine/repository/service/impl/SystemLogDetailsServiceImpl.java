package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.wine.repository.entity.SystemLogDetails;
import com.hishop.wine.repository.dao.SystemLogDetailsMapper;
import com.hishop.wine.repository.service.SystemLogDetailsService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;

/**   
 * 操作日志明细表 服务实现类
 * @author: HuBiao
 * @date: 2023-09-15
 */

@Service
public class SystemLogDetailsServiceImpl extends ServiceImpl<SystemLogDetailsMapper, SystemLogDetails> implements SystemLogDetailsService  {

    /**
     * 根据日志id查询日志明细
     *
     * @param logId 日志id
     * @return 日志明细
     */
    @Override
    public SystemLogDetails getByLogId(Long logId) {
        return getOne(new LambdaQueryWrapper<SystemLogDetails>().eq(SystemLogDetails::getLogId, logId));
    }
}