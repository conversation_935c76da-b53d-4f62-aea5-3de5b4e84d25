package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.entity.Bill;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 收入账单表(Bill)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-29 15:17:19
 */
public interface BillMapper extends BaseMapper<Bill> {

    /**
     * 明细总收入
     * @param billQueryPo 入参
     * @return
     */
    BillCountDto pageListCount(@Param("param")BillQueryPo billQueryPo);
    /**
     * 月统计分页查询
     * @param billQueryPo 入参
     * @return
     */
    Page<BillGroupDto> monthPageList(Page<BillGroupDto> pageInfo, @Param("param")BillQueryPo billQueryPo);
    /**
     * 月统计总收入
     * @param billQueryPo 入参
     * @return
     */
    BillCountDto monthPageListCount(@Param("param")BillQueryPo billQueryPo);
    /**
     * 日统计分页查询
     * @param pageInfo 分页信息
     * @param billQueryPo 入参
     * @return
     */
    Page<BillGroupDto> dayPageList(Page<BillGroupDto> pageInfo, @Param("param")BillQueryPo billQueryPo);
    /**
     * 日统计总收入
     * @param billQueryPo 入参
     * @return
     */
    List<BillVo> exportList(@Param("param")BillQueryPo billQueryPo);

}

