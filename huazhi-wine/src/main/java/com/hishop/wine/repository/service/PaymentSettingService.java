package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.repository.dto.payment.PaymentSettingDTO;
import com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO;
import com.hishop.wine.repository.entity.PaymentSetting;

import java.util.List;

/**
 * 支付设置 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-07-18
 */

public interface PaymentSettingService extends IService<PaymentSetting> {

    /**
     * 获取支付设置列表
     *
     * @return 获取支付设置列表
     */
    List<PaymentSettingDTO> listAll();

    /**
     * 获取线下支付设置列表
     *
     * @return 获取线下支付设置列表
     */
    List<PaymentSettingOfflineDTO> listForOfflineSelect(String moduleCode);

    /**
     * 根据模块code和特殊code获取线下支付设置列表
     * @param moduleCode
     * @param specialCode
     * @return
     */
    List<PaymentSettingOfflineDTO> listForOfflineSelectBySpecialCode(String moduleCode, String specialCode);

    /**
     * 获取线下支付设置列表
     *
     * @return 获取线下支付设置列表
     */
    List<PaymentSettingOfflineDTO> listForOfflineSelectAll(String moduleCode);
}