package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品分类表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Data
@NoArgsConstructor
@TableName("hishop_product_category")
public class ProductCategory implements Serializable {

    private static final long serialVersionUID = 1670672863287164928L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 分类名称
     */
    private String categoryName;

    /**
     * 排序
     */
    private Integer orderNum;

    /**
     * 状态 0：禁用  1：正常
     */
    private Boolean status;

    /**
     * 逻辑删除 0-未删除 其余为已删除
     */
    private Long izDelete;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;


}
