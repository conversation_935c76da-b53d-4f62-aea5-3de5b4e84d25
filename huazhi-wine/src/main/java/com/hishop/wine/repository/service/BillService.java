package com.hishop.wine.repository.service;


import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.entity.Bill;

import java.util.List;

/**
 * 收入账单表(Bill)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-29 15:17:19
 */
public interface BillService extends IService<Bill> {

    /**
     * 分页查询收入账单表
     * @param pageInfo 分页信息
     * @param billQueryPo 查询参数
     * @return
     */
    Page<Bill> queryPage(Page<Bill> pageInfo, BillQueryPo billQueryPo);

    /**
     * 明细总收入
     * @param billQueryPo 入参
     * @return
     */
    BillCountVo pageListCount(BillQueryPo billQueryPo);

    /**
     * 月统计分页查询
     * @param billQueryPo 入参
     * @return
     */
    Page<BillGroupDto> monthPageList(Page<BillGroupDto> pageInfo, BillQueryPo billQueryPo);

    /**
     * 月统计总收入
     * @param billQueryPo 入参
     * @return
     */
    BillCountVo monthPageListCount(BillQueryPo billQueryPo);

    /**
     * 日统计分页查询
     * @param pageInfo 分页信息
     * @param billQueryPo 入参
     * @return
     */
    Page<BillGroupDto> dayPageList(Page<BillGroupDto> pageInfo, BillQueryPo billQueryPo);

    /**
     * 日统计总收入
     * @param billQueryPo 入参
     * @return
     */
    BillCountVo dayPageListCount(BillQueryPo billQueryPo);

    /**
     * 导出查询
     * @param param 入参
     * @return
     */
    public List<BillVo> exportList(BillExportPo param);
}

