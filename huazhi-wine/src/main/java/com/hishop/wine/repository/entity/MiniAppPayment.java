package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Date;

/**   
 * 小程序关联支付设置 实体类
 * @author: HuBiao
 * @date: 2023-07-18
 */

@Data
@NoArgsConstructor
@TableName("hishop_mini_app_payment")
public class MiniAppPayment implements Serializable {

	private static final long serialVersionUID = 1681264750452547584L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 小程序id
    */
	private String appId;
    
    /**
    * 支付设置id
    */
	private Long paymentId;
    

}
