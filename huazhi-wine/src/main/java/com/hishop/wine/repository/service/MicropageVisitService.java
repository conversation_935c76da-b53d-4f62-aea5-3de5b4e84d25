package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.MicropageVisit;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Date;
import java.util.List;

/**
 * 微页面访客记录表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-08-28
 */

public interface MicropageVisitService extends IService<MicropageVisit> {

    /**
     * 添加访问明细
     *
     * @param visitList 访问记录
     */
    void saveVisitLog(List<MicropageVisit> visitList);

}