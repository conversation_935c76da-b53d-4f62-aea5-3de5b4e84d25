package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.dto.points.MemberPointsDetailDTO;
import com.hishop.wine.repository.dto.points.PointsModifiedSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.entity.MemberPointsDetails;
import com.hishop.wine.repository.param.MemberPointsParam;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**   
 * 会员积分明细表 Mapper 接口
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

public interface MemberPointsDetailsMapper extends BaseMapper<MemberPointsDetails> {

    /**
     * 分页获取 会员积分明细
     * <AUTHOR>
     * @date 2023/6/25
     */
    Page<MemberPointsDetailDTO> qryDetail(Page<MemberPoints> pageInfo, @Param("param") MemberPointsParam param);

    /**
     * 汇总用户积分
     * <AUTHOR>
     * @date 2023/8/3
     */
    Integer getUserIncreasePointsSummary(@Param("userId") Long userId, @Param("identityType") Integer identityType,
                                            @Param("minId") Long minId, @Param("maxCreateTime") Date maxCreateTime);

    /**
     * 统计该时间段内的积分变动
     * @param userId 用户ID
     * @param identityType 身份类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 积分变动汇总
     */
    List<PointsModifiedSummaryDTO> summaryPointsModified(@Param("userId") Long userId, @Param("identityType") Integer identityType,
                                                         @Param("startTime") Date startTime, @Param("endTime") Date endTime);
}
