package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.Micropage;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;


public interface MicropageMapper  extends BaseMapper<Micropage> {

    Long countFile(@Param("status") Integer status, @Param("categoryId") Long categoryId);

    void updateVisitCount(@Param("param") Map<Integer, Long> visitMap);
}
