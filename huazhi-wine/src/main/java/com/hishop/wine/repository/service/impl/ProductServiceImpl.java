package com.hishop.wine.repository.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.enums.ProductTypeEnums;
import com.hishop.wine.repository.dao.ProductMapper;
import com.hishop.wine.repository.dto.ProductPageDTO;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.param.ProductParam;
import com.hishop.wine.repository.service.ProductService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;

/**
 * 产品表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */

@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements ProductService {

    /**
     * 分页查询产品列表
     *
     * @param pageInfo 分页信息
     * @param param    筛选参数
     * @return 产品列表
     */
    @Override
    public Page<ProductPageDTO> queryProductPage(Page<Product> pageInfo, ProductParam param) {
        Page<ProductPageDTO> pageResult = baseMapper.queryProductPage(pageInfo, param);

        // 拼接上产品类型名称
        pageResult.getRecords().forEach(productPageDTO -> {
            productPageDTO.setProductTypeName(ProductTypeEnums.getNameByType(productPageDTO.getProductType()));
        });
        return pageResult;
    }

    /**
     * 根据产品id的集合逻辑删除
     *
     * @param ids    id的集合
     * @param userId 操作人id
     */
    @Override
    public void logicDeleteByIds(List<Long> ids, Long userId) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        baseMapper.logicDeleteByIds(ids, userId);
    }

    /**
     * 批量添加产品
     *
     * @param productList 产品集合
     */
    @Override
    public void insertProductBatch(List<Product> productList) {
        if (CollectionUtils.isEmpty(productList)) {
            return;
        }
        baseMapper.insertBatchSomeColumn(productList);
    }

    @Override
    public List<Product> qryList(ProductParam param) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ObjectUtil.isNull(param.getFilterDelete()) || param.getFilterDelete(), Product::getIzDelete, 0)
                .eq(Objects.nonNull(param.getProductType()), Product::getProductType, param.getProductType())
                .in(CollUtil.isNotEmpty(param.getIdList()), Product::getId, param.getIdList())
                .in(CollUtil.isNotEmpty(param.getProductCodeList()), Product::getProductCode, param.getProductCodeList());
        return super.list(wrapper);
    }
}