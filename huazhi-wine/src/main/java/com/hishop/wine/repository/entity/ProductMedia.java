package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 产品媒体资源表 实体类
 *
 * @author: Hu<PERSON>iao
 * @date: 2023-06-19
 */
@Data
@NoArgsConstructor
@TableName("hishop_product_media")
public class ProductMedia implements Serializable {

    private static final long serialVersionUID = 1670672864478347264L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 产品id
     */
    private Long productId;

    /**
     * 媒体类型 1-图片 2-主图视频 3-详情图
     */
    private Integer mediaType;

    /**
     * 产品名称
     */
    private String url;

    /**
     * 视频封面图
     */
    private String cover;

    public static ProductMedia of(Long productId, Integer mediaType, String url, String cover) {
        ProductMedia productMedia = new ProductMedia();
        productMedia.setProductId(productId);
        productMedia.setMediaType(mediaType);
        productMedia.setUrl(url);
        productMedia.setCover(cover);
        return productMedia;
    }
}
