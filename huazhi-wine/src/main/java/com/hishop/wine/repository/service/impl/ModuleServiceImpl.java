package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.wine.repository.dto.module.ModuleWxParamDTO;
import com.hishop.wine.repository.entity.ModuleInfo;
import com.hishop.wine.repository.dao.ModuleMapper;
import com.hishop.wine.repository.service.ModuleService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 模块表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Service
public class ModuleServiceImpl extends ServiceImpl<ModuleMapper, ModuleInfo> implements ModuleService {

    /**
     * 查询模块关联微信参数
     *
     * @return 模块关联微信参数
     */
    @Override
    public List<ModuleWxParamDTO> listModuleWxParam() {
        return baseMapper.listModuleWxParam();
    }

    /**
     * 模块绑定小程序
     *
     * @param appId       小程序id
     * @param moduleCodes 模块编码的集合
     */
    @Override
    public void bindMiniApp(String appId, List<String> moduleCodes) {
        if (CollectionUtils.isEmpty(moduleCodes)) {
            return;
        }

        ModuleInfo moduleInfo = new ModuleInfo();
        moduleInfo.setAppId(appId);
        update(moduleInfo, new LambdaQueryWrapper<ModuleInfo>().in(ModuleInfo::getModuleCode, moduleCodes));
    }

    @Override
    public void unbindMiniApp(String appId) {
        ModuleInfo moduleInfo = new ModuleInfo();
        moduleInfo.setAppId("");
        this.update(moduleInfo, new LambdaQueryWrapper<ModuleInfo>().eq(ModuleInfo::getAppId, appId));
    }

}