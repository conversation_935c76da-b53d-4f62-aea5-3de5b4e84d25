package com.hishop.wine.repository.entity;


import com.baomidou.mybatisplus.annotation.*;
import lombok.NoArgsConstructor;
import lombok.EqualsAndHashCode;
import lombok.Data;
import com.hishop.common.pojo.entity.BaseEntity;

/**
 * 销售区域(SaleArea)表实体类
 *
 * <AUTHOR>
 * @since 2024-07-04 09:08:04
 */
@Data
@NoArgsConstructor
@EqualsAndHashCode(callSuper = true)
@TableName("hishop_sale_area")
public class SaleArea extends BaseEntity {

    /**
     * 主键id
     **/
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 销售维度id
     **/
    private Long saleDimId;
    /**
     * 名称
     **/
    private String name;
    /**
     * 编码
     **/
    private String code;
    /**
     * 父级id
     **/
    private Long parentId;
    /**
     * 完整编码
     **/
    private String fullCode;
    /**
     * 完整名称
     **/
    private String fullName;
}

