package com.hishop.wine.repository.dto;

import com.hishop.wine.common.enums.MaterialType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.models.auth.In;
import lombok.Data;

/**
 * @description: 资源分组
 * @author: tomliu
 * @create: 2022/06/23 16:24
 **/
@ApiModel("资源分组")
@Data
public class MaterialCategoryDTO extends BaseDTO<Long> {

    /**
     * 分组名称
     */
    @ApiModelProperty("分组名称")
    private String name;

    /**
     * 类型
     */
    @ApiModelProperty("分组类型")
    private Integer type;

    /**
     * 当前层级(从1开始)
     */
    @ApiModelProperty("当前层级(从1开始)")
    private Integer level;

    /**
     * 从顶级到当前级的完整路径(不包括当前分组)，以"|"分隔
     * <p>
     * 格式示例： |2|4|7|，其中2、4、7 为当前分组的上级分组
     * </p>
     */
    @ApiModelProperty("从顶级到当前级的完整路径(不包括当前分组),以\"|\"分隔")
    private String path;

    /**
     * 父级分组id
     */
    @ApiModelProperty("父级分组id")
    private Long parentId;

    /**
     * 分组下的资源文件总数
     */
    @ApiModelProperty("分组下的资源文件总数")
    private Integer total;
}
