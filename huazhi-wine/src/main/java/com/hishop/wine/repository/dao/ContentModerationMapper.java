package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.entity.ContentModeration;
import org.apache.ibatis.annotations.Param;

/**
 * 内容审核表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */

public interface ContentModerationMapper extends BaseMapper<ContentModeration> {

    /**
     * 刷新审核状态
     *
     * @param id 内容审核id
     */
    void refreshStatus(@Param("id") Long id);
}
