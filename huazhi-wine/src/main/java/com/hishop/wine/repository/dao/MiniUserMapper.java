package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.minUser.MiniUserBlacklistQueryPo;
import com.hishop.wine.model.po.minUser.MiniUserQueryPo;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import org.apache.ibatis.annotations.Param;

/**
 * 小程序用户表 Mapper 接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface MiniUserMapper extends BaseMapper<MiniUser> {

    Page<MiniUserVO> queryPage(Page<MiniUserVO> pageInfo, @Param("param") MiniUserQueryPo param);

    MiniUserVO getUserDetail(@Param("id")Long id);

    Page<MiniUserBlacklistVo> blacklistPage(Page<MiniUserBlacklistVo> pageInfo, @Param("param") MiniUserBlacklistQueryPo param);

    MiniUserVO getMiniUserByUserIdAndModuleCode(@Param("userId")Long userId, @Param("moduleCode")String moduleCode);

    Long getMiniUserCount();
}
