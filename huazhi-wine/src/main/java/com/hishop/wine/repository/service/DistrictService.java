package com.hishop.wine.repository.service;

import com.hishop.wine.repository.entity.District;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;


/**
 * 地区表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
public interface DistrictService extends IService<District> {

    /**
     * 批量查询(拼接sql)
     *
     * @param entityList 插入集合
     * @return 影响行数
     */
    Integer insertBatchSomeColumn(List<District> entityList);

    /**
     * 批量更新(拼接sql)
     *
     * @param entityList 更新集合
     * @return 影响行数
     */
    Integer updateBatchSomeColumn(List<District> entityList);

    /**
     * 更新下级名称
     *
     * @param id          组级id
     * @param oldFullName 旧的下级名称
     * @param newFullName 新的下级名称
     */
    void updateChildName(Integer id, String oldFullName, String newFullName);

    /**
     * 是否存在下级
     *
     * @param id id
     * @return 是否存在下级
     */
    Boolean hasChild(Integer id);
}