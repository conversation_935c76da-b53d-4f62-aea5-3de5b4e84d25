package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 微信二维码表 实体类
 * @author: LiGuoQiang
 * @date: 2023-06-20
 */

@Data
@NoArgsConstructor
@TableName("hishop_wechat_code")
public class WechatCode implements Serializable {

	private static final long serialVersionUID = 1670957470513557504L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 二维码类型。1：小程序码；2：公众号二维码
    */
	private Integer codeType;
    
    /**
    * 二维码来源。BASIC_SYSTEM：基础库；SCAN_MARKETING：扫码营销；FANS_CLUB：粉丝俱乐部
    */
	private String codeFrom;
    
    /**
    * 二维码唯一标识，通过该字段匹配获取
    */
	private String codeKey;
    
    /**
    * 二维码描述
    */
	private String codeDesc;
    
    /**
    * 二维码地址。云服务器地址，存相对路径
    */
	private String codeUrl;

    /**
     * 完整的小程序链接(可带参数)
     */
	private String page;
    
    /**
    * 是否删除
    */
	private Boolean izDelete;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 创建人ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 修改时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    
    /**
    * 修改人ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    

}
