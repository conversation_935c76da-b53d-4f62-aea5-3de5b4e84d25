package com.hishop.wine.repository.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * @description: DTO基类
 * @author: tomliu
 * @create: 2022/06/25 11:48
 **/
@Data
public class BaseDTO<IdType> implements Serializable {

    /**
     * 主题id
     */
    private IdType id;

    /**
     * 创建时间
     */
    private Date createTime = new Date();//当前时间为默认创建时间

    /**
     * 创建人
     */
    private Long createUser;
}
