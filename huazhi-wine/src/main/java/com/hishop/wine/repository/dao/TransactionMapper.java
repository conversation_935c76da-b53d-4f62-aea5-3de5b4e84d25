package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.hishop.wine.repository.dto.transaction.TransactionTotalDto;
import com.hishop.wine.repository.entity.Transaction;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 交易流水表 Mapper 接口
 *
 * @author: Hu<PERSON>ia<PERSON>
 * @date: 2023-06-28
 */
public interface TransactionMapper extends BaseMapper<Transaction> {

    /**
     * 重置退款信息
     *
     * @param transactionId 交易流水id
     */
    void resetRefundData(@Param("transactionId") Long transactionId);

    /**
     * 获取交易流水统计信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    TransactionTotalDto getTransactionTotal(@Param("startTime") String startTime, @Param("endTime")String endTime);

    /**
     * 获取每日交易流水统计信息
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     */
    List<TransactionTotalDto> queryTransactionTotalList(@Param("startTime") String startTime, @Param("endTime")String endTime);
}
