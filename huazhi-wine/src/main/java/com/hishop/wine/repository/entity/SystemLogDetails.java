package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**   
 * 操作日志明细表 实体类
 * @author: HuBiao
 * @date: 2023-09-15
 */

@Data
@NoArgsConstructor
@TableName("hishop_system_log_details")
public class SystemLogDetails implements Serializable {

	private static final long serialVersionUID = 1702518678167007232L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;
    
    /**
    * 日志id
    */
	private Long logId;
    
    /**
    * 主键id的集合
    */
	private String primaryList;
    
    /**
    * 调用前的值
    */
	private String oldDataList;
    
    /**
    * 调用后的值
    */
	private String newDataList;
    
    /**
    * 变更情况
    */
	private String changeDataList;
    

}
