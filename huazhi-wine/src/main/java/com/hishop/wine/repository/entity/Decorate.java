package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**   
 * 装修表 实体类
 * @author: chenpeng
 * @date: 2023-06-26
 */

@Data
@NoArgsConstructor
@TableName("hishop_decorate")
public class Decorate implements Serializable {

	private static final long serialVersionUID = 1673209270675382272L;
	
    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
	private Long id;

    /**
     * 小程序appId
     */
    private String appId;

    /**
     * 系统类型(扫码营销：scan_marketing,粉丝俱乐部：fans_club)
     */
    private String moduleCode;

    /**
    * 装修类型。前端自己定义关键字
    */
	private String decorateType;
    
    /**
    * 装修设置json串
    */
	private String settingJson;

    /**
     * 是否小程序默认使用装修
     */
	private Boolean izDefault;
    /**
    * 是否删除
    */
	private Boolean izDelete;
    
    /**
    * 创建者ID
    */
    @TableField(fill = FieldFill.INSERT)
	private Long createBy;
    
    /**
    * 创建时间
    */
    @TableField(fill = FieldFill.INSERT)
	private Date createTime;
    
    /**
    * 更新者ID
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Long updateBy;
    
    /**
    * 更新时间
    */
    @TableField(fill = FieldFill.INSERT_UPDATE)
	private Date updateTime;
    

}
