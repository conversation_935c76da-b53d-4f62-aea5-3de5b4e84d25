package com.hishop.wine.repository.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import com.hishop.wine.repository.dao.LogisticsCodeMapper;
import com.hishop.wine.repository.entity.LogisticsCode;
import com.hishop.wine.repository.service.LogisticsCodeService;
import org.springframework.stereotype.Service;

/**
 * 物流码管理表(LogisticsCode)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-08 11:51:56
 */
@Service
public class LogisticsCodeServiceImpl extends ServiceImpl<LogisticsCodeMapper, LogisticsCode> implements LogisticsCodeService {

    @Override
    public Page<LogisticsCodeVo> qryPage(Page<LogisticsCode> pageInfo, LogisticsCodeQueryPo param) {
        return this.baseMapper.queryPage(pageInfo, param);
    }
}

