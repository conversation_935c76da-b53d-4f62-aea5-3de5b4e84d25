package com.hishop.wine.repository.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.model.po.minUser.MiniUserBlacklistQueryPo;
import com.hishop.wine.model.po.minUser.MiniUserQueryPo;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import com.hishop.wine.repository.entity.MiniUser;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 小程序用户表 数据层服务类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface MiniUserService extends IService<MiniUser> {

    /**
     * 根据userId获取小程序用户信息
     *
     * <AUTHOR>
     * @date 2023/6/29
     */
    List<MiniUser> getMiniUser(List<Long> userIdList);

    /**
     * 获取小程序用户信息
     * @param userId 用户id
     * @param moduleCode 模块code
     * @return
     */
    MiniUserVO getMiniUserByUserIdAndModuleCode(Long userId, String moduleCode);

    /**
     * 根据userId获取小程序用户信息
     *
     * @param userId 用户id
     * @param appId  小程序appId
     * @return 小程序用户信息
     */
    MiniUser getMiniUser(Long userId, String appId);

    /**
     * 分页查询
     * @param pageInfo 分页信息
     * @param miniUserQueryPo 查询参数
     * @return
     */
    Page<MiniUserVO> pageList(Page<MiniUserVO> pageInfo, MiniUserQueryPo miniUserQueryPo);

    /**
     * 查询详情
     * @param id
     * @return
     */
    MiniUserVO getUserDetail(Long id);

    /**
     * 小程序用户黑名单分页查询
     * @param pageInfo 分页信息
     * @param miniUserBlacklistQueryPo 查询参数
     * @return
     */
    Page<MiniUserBlacklistVo> blacklistPage(Page<MiniUserBlacklistVo> pageInfo, MiniUserBlacklistQueryPo miniUserBlacklistQueryPo);

    /**
     * 获取小程序用户数量
     * @return 小程序用户数量
     */
    Long getMiniUserCount();
}