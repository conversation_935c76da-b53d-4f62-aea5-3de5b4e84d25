package com.hishop.wine.repository.dao;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.wine.repository.entity.SystemLog;
import com.hishop.wine.repository.param.log.SystemLogParam;
import org.apache.ibatis.annotations.Param;

/**   
 * 操作日志表 Mapper 接口
 * @author: HuBiao
 * @date: 2023-08-01
 */

public interface SystemLogMapper extends BaseMapper<SystemLog> {

    /**
     * 分页查询日志记录
     *
     * @param pageInfo 分页参数
     * @param param    筛选参数
     * @return 日志记录
     */
    Page<SystemLog> qryPage(Page<SystemLog> pageInfo, @Param("param") SystemLogParam param);
}
