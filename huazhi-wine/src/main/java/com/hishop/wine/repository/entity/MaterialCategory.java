package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 资源分组表 实体类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Data
@NoArgsConstructor
@TableName("hishop_material_category")
public class MaterialCategory implements Serializable {

    private static final long serialVersionUID = 1669986860180226048L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 父级id
     */
    private Long parentId;

    /**
     * 上级id的集合
     */
    private String parentIds;

    /**
     * 分组名称
     */
    private String name;

    /**
     * 分组类型
     */
    private Integer type;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 从顶级到当前级的完整路径(不包括当前分组)
     */
    private String path;

    /**
     * 当前分组下资源文件总数
     */
    private Integer total;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新者ID
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Long updateBy;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;

    /**
     * 查询资源总数
     */
    @TableField(exist = false)
    private Integer totalMaterial;


}
