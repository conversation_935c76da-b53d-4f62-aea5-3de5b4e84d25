package com.hishop.wine.repository.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.hishop.common.util.MysqlPlusUtil;
import com.hishop.wine.repository.dao.MemberPointsMapper;
import com.hishop.wine.repository.dto.points.MemberPointsDTO;
import com.hishop.wine.repository.dto.points.MemberPointsSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.param.MemberPointsParam;
import com.hishop.wine.repository.service.MemberPointsService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**   
 * 会员积分表 服务实现类
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Service
public class MemberPointsServiceImpl extends ServiceImpl<MemberPointsMapper, MemberPoints> implements MemberPointsService  {

    @Resource
    private MemberPointsMapper memberPointsMapper;

    @Override
    public Page<MemberPointsDTO> qryPage(Page<MemberPoints> pageInfo, MemberPointsParam param) {
        return memberPointsMapper.qryMemberPoints(pageInfo, param);
    }

    @Override
    public MemberPointsSummaryDTO summary(MemberPointsParam param) {
        return memberPointsMapper.summary(param);
    }

    @Override
    public MemberPoints getUserPoints(Long userId, Integer identityType) {
        LambdaQueryWrapper<MemberPoints> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberPoints::getUserId, userId)
                .eq(MemberPoints::getIdentityType, identityType);
        return super.getOne(wrapper);
    }

    @Override
    public List<MemberPoints> listAll() {
        return super.list();
    }

}