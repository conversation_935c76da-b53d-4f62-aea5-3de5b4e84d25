package com.hishop.wine.repository.param;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date : 2023/6/21
 */
@Data
public class AdminParam {

    /**
     * 搜索值
     */
    private String searchValue;

    /**
     * 角色id
     */
    private Long roleId;

    /**
     * 部门id
     */
    private String departmentId;


    /**
     * 查询部门置顶负责人
     */
    private Boolean headFlag;

    /**
     * 身份类型 1-管理员 2-消费者 3-经销商 4-终端 5-业务员
     */
    private Integer identityType;

    /**
     * 身份类型列表
     */
    private List<Integer> identityTypeList;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 状态
     */
    private Integer status;
}
