package com.hishop.wine.repository.service.impl;

import com.hishop.wine.repository.entity.ProductMedia;
import com.hishop.wine.repository.dao.ProductMediaMapper;
import com.hishop.wine.repository.service.ProductMediaService;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.util.CollectionUtils;
import java.util.List;

/**
 * 产品媒体资源表 服务实现类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Service
public class ProductMediaServiceImpl extends ServiceImpl<ProductMediaMapper, ProductMedia> implements ProductMediaService {

    /**
     * 根据产品Id删除
     *
     * @param productId 产品Id
     */
    @Override
    public void removeByProductId(Long productId) {
        remove(new LambdaQueryWrapper<ProductMedia>().eq(ProductMedia::getProductId, productId));
    }

    /**
     * 关联媒体资源
     *
     * @param productMediaList 关联关系集合
     */
    @Override
    public void insertProductMediaBatch(List<ProductMedia> productMediaList) {
        if (CollectionUtils.isEmpty(productMediaList)) {
            return;
        }
        baseMapper.insertBatchSomeColumn(productMediaList);
    }
}