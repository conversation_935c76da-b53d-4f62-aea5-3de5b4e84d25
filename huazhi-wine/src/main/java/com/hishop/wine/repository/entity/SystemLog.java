package com.hishop.wine.repository.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

/**
 * 操作日志表 实体类
 * @author: HuBiao
 * @date: 2023-08-02
 */

@Data
@NoArgsConstructor
@TableName("hishop_system_log")
public class SystemLog implements Serializable {

    private static final long serialVersionUID = 1686548412777693184L;

    /**
     * 主键id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 模块编码
     */
    private String moduleCode;

    /**
     * 业务模块
     */
    private String businessSector;

    /**
     * 业务描述
     */
    private String businessDesc;

    /**
     * 操作类型 CREATE/UPDATE/DELETE
     */
    private String operationType;

    /**
     * 操作名称
     */
    private String operationName;

    /**
     * 业务键值
     */
    private String businessKey;

    /**
     * 操作人账号
     */
    private String operatorUsername;

    /**
     * 操作人手机号
     */
    private String operatorMobile;

    /**
     * 请求路径
     */
    private String requestUrl;

    /**
     * 请求参数
     */
    private String requestParam;

    /**
     * 返回值
     */
    private String response;

    /**
     * 耗时
     */
    private Long costTime;

    /**
     * 跟踪号
     */
    private String traceNo;

    /**
     * 创建者ID
     */
    @TableField(fill = FieldFill.INSERT)
    private Long createBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;


}
