package com.hishop.wine.repository.param;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.Builder;
import java.util.Date;

/**   
 * 小程序用户表 数据库查询类
 * @author: HuBia<PERSON>
 * @date: 2023-06-21
 */

@Data
@Builder
public class MiniUserParam {


	private Long id;
    

	private Long userId;
    

	private String appId;
    

	private String registerModuleCode;
    

	private String unionId;
    

	private String openId;
    

	private Long createBy;
    

	private Date createTime;
    

	private Long updateBy;
    

	private Date updateTime;
    

}
