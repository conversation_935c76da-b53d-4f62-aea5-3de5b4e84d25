package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.rank.RankCreatePO;
import com.hishop.wine.model.po.rank.RankQueryPO;
import com.hishop.wine.model.po.rank.RankUpdatePO;
import com.hishop.wine.model.vo.rank.RankSelectVO;
import com.hishop.wine.model.vo.rank.RankVO;
import com.hishop.wine.repository.entity.Rank;
import com.hishop.wine.biz.RankBiz;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.rank.RankParam;
import com.hishop.wine.repository.service.RankService;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.repository.service.UserService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.List;

import cn.hutool.core.bean.BeanUtil;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.transaction.annotation.Transactional;


/**
 * 头衔表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Slf4j
@Service
public class RankBizImpl implements RankBiz {

    @Resource
    private RankService rankService;
    @Resource
    private UserService userService;

    /**
     * 新增头衔
     *
     * @param createPO 新增头衔参数
     * @return 头衔id
     */
    @Override
    public Long create(RankCreatePO createPO) {
        checkName(createPO.getModuleCode(), createPO.getRankName(), null);

        Rank entity = BeanUtil.copyProperties(createPO, Rank.class);
        rankService.save(entity);
        return entity.getId();
    }

    /**
     * 编辑头衔
     *
     * @param updatePO 编辑头衔参数
     */
    @Override
    public void update(RankUpdatePO updatePO) {
        Rank entity = rankService.getById(updatePO.getId());
        Assert.isTrue(ObjectUtil.isNotNull(entity), "头衔不存在");

        checkName(entity.getModuleCode(), updatePO.getRankName(), updatePO.getId());

        Rank updateEntity = BeanUtil.copyProperties(updatePO, Rank.class);
        updateEntity.setId(entity.getId());
        rankService.updateById(updateEntity);
    }

    /**
     * 删除头衔
     *
     * @param ids
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteByIdS(List<Long> ids) {
        checkRelateUser(ids);

        Rank updRank = new Rank();
        updRank.setIzDelete(Boolean.TRUE);
        rankService.update(updRank, new LambdaQueryWrapper<Rank>().in(Rank::getId, ids));
    }

    /**
     * 查询头衔
     *
     * @param id 头衔id
     * @return 头衔详情
     */
    @Override
    public RankVO detail(Long id) {
        Rank entity = rankService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(entity), "头衔不存在");

        return BeanUtil.copyProperties(entity, RankVO.class);
    }

    /**
     * 查询头衔列表
     *
     * @param qryPO 查询参数
     * @return 头衔列表
     */
    @Override
    public List<RankVO> list(RankQueryPO qryPO) {
        RankParam param = BeanUtil.copyProperties(qryPO, RankParam.class);
        List<Rank> dbList = rankService.qryList(param);
        return BeanUtil.copyToList(dbList, RankVO.class);
    }

    /**
     * 分页查询头衔列表
     *
     * @param pagePO 分页参数
     * @return 头衔列表
     */
    @Override
    public PageResult<RankVO> pageList(RankQueryPO pagePO) {
        RankParam param = BeanUtil.copyProperties(pagePO, RankParam.class);
        Page<Rank> dbPage = rankService.qryPage(pagePO.buildPage(), param);
        return PageResultHelper.transfer(dbPage, RankVO.class);
    }

    /**
     * 修改头衔状态
     *
     * @param ids    头衔id的集合
     * @param status 头衔状态 true-启用 false-禁用
     */
    @Override
    public void changeStatus(List<Long> ids, Boolean status) {
        Rank updRank = new Rank();
        updRank.setStatus(status);
        rankService.update(updRank, new LambdaQueryWrapper<Rank>().in(Rank::getId, ids));
    }

    /**
     * 获取头衔下拉
     *
     * @param needAll 是否需要全部
     * @return 获取头衔下拉
     */
    @Override
    public List<RankSelectVO> listForSelect(Boolean needAll) {
        List<Rank> dbList = rankService.list(new LambdaQueryWrapper<Rank>()
                .eq(Rank::getIzDelete, Boolean.FALSE)
                .eq(ObjectUtil.isNull(needAll) || !needAll, Rank::getStatus, Boolean.TRUE));
        return BeanUtil.copyToList(dbList, RankSelectVO.class);
    }

    /**
     * 检测头衔名称是否重复
     *
     * @param moduleCode 模块编码
     * @param rankName   头衔名称
     * @param rankId     头衔id
     */
    private void checkName(String moduleCode, String rankName, Long rankId) {
        Long count = rankService.count(new LambdaQueryWrapper<Rank>()
                .eq(Rank::getModuleCode, moduleCode)
                .eq(Rank::getRankName, rankName)
                .eq(Rank::getIzDelete, Boolean.FALSE)
                .ne(ObjectUtil.isNotNull(rankId), Rank::getId, rankId));
        Assert.isTrue(count == 0, "头衔名称已存在");
    }

    /**
     * 检测是否已经关联了用户
     *
     * @param ids 用户id的集合
     */
    private void checkRelateUser(List<Long> ids) {
        ids.forEach(id -> {
            Rank entity = rankService.getById(id);
            Assert.isTrue(ObjectUtil.isNotNull(entity), "头衔不存在");

            long count = userService.count(new LambdaQueryWrapper<User>().eq(User::getRankId, id));
            Assert.isTrue(count == 0, String.format("头衔【%s】已关联用户，无法进行该操作", entity.getRankName()));
        });
    }
}