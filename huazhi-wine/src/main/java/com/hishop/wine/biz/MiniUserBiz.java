package com.hishop.wine.biz;

import cn.binarywang.wx.miniapp.bean.WxMaRunStepInfo;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.basic.MiniUserSavePO;
import com.hishop.wine.model.po.login.UserMiniAuthLoginPO;
import com.hishop.wine.model.po.login.UserMiniDefaultLoginPO;
import com.hishop.wine.model.po.login.UserMiniMobileLoginPO;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.minUser.*;
import com.hishop.wine.model.po.miniApp.MiniEncryptDataPO;
import com.hishop.wine.model.vo.basic.MiniUserDetailVO;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.login.MiniLoginUserVO;
import com.hishop.wine.model.vo.login.MiniLoginVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;

import java.util.List;

/**
 * 小程序用户表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
public interface MiniUserBiz {

    /**
     * 微信授权登录(会自动注册)
     *
     * @param authLoginPO 登录参数
     * @return 登录结果
     */
    MiniLoginVO wxAuthLogin(UserMiniAuthLoginPO authLoginPO);

    /**
     * 微信授权登录(会自动注册)
     *
     * @param authLoginPO 登录参数
     * @return 登录结果
     */
    MiniLoginVO wxAuthLogin2(UserMiniAuthLoginPO authLoginPO);

    /**
     * 手机号登录(会自动注册)
     *
     * @param mobileLoginPO   登录参数
     * @param checkVerifyCode 是否校验验证码
     * @return 登录结果
     */
    MiniLoginVO mobileLogin(UserMiniMobileLoginPO mobileLoginPO, Boolean checkVerifyCode);

    /**
     * 微信静默登录
     *
     * @param defaultLoginPO 静默登录参数
     * @return 登录返回值
     */
    MiniLoginVO wxDefaultLogin(UserMiniDefaultLoginPO defaultLoginPO);

    /**
     * 刷新token
     *
     * @param refreshTokenParam 刷新token 参数
     * @return 小程序登录信息
     */
    MiniLoginVO refreshToken(UserRefreshTokenPO refreshTokenParam);

    /**
     * 根据userId列表获取小程序用户信息列表
     *
     * <AUTHOR>
     * @date 2023/6/29
     */
    List<MiniUserVO> getMiniUser(List<Long> userIdList);

    /**
     * 根据userId与模块code获取小程序用户信息列表
     * @param userId 用户id
     * @param moduleCode 模块code
     * @return
     */
    MiniUserVO getMiniUserByUserIdAndModuleCode(Long userId, String moduleCode);

    /**
     * 发送小程序登录验证码
     *
     * @param mobile 手机号
     */
    void sendForLogin(String mobile);

    /**
     * 保存用户信息
     *
     * @param miniUserSavePO 保存用户信息参数
     */
    void saveUserData(MiniUserSavePO miniUserSavePO);

    /**
     * 获取用户协议/隐私政策
     *
     * @param type 1:用户协议 2:隐私政策
     * @return 协议内容
     */
    String getAgreement(Integer type);

    /**
     * 获取微信步数
     *
     * <AUTHOR>
     * @date 2023/7/25
     */
    List<WxMaRunStepInfo> getRunStep(MiniEncryptDataPO encryptDataPo);

    /**
     * 获取小程序用户信息
     *
     * @return 小程序用户信息
     */
    MiniLoginUserVO getMiniUserInfo();

    /**
     * 分页查询
     * @param miniUserQueryPo 查询条件
     * @return
     */
    PageResult<MiniUserVO> pageList(MiniUserQueryPo miniUserQueryPo);

    /**
     * 查询详情
     * @param id id
     * @return
     */
    MiniUserDetailVO getUserDetail(Long id);

    /**
     * 备注名
     * @param miniUserRemarkNamePo 入参
     */
    void remarkName(MiniUserRemarkNamePo miniUserRemarkNamePo);

    /**
     * 批量处理黑名单
     * @param miniUserBlacklistPo 入参
     */
    void blackList(MiniUserBlacklistPo miniUserBlacklistPo);

    /**
     * 打标入参
     * @param miniUserTagsPo 入参
     */
    void tags(MiniUserTagsPo miniUserTagsPo);

    /**
     * 分页查询
     * @param miniUserBlacklistQueryPo 查询条件
     * @return
     */
    PageResult<MiniUserBlacklistVo> blacklistPage(MiniUserBlacklistQueryPo miniUserBlacklistQueryPo);

    /**
     * 删除标签
     * @param minUserDelTagPo 入参
     */
    void delTag(MinUserDelTagPo minUserDelTagPo);

    /**
     *
     * @param authLoginPO
     * @return
     */
    String getPhoneByWx(UserMiniAuthLoginPO authLoginPO);
}