package com.hishop.wine.biz.excel.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeImportPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class LogisticsCodeErrWrapper implements DataWrapper<LogisticsCodeImportPo> {

    private final List<LogisticsCodeImportPo> dataList;

    public LogisticsCodeErrWrapper(List<LogisticsCodeImportPo> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<LogisticsCodeImportPo> getDataList() {
        return dataList;
    }

}
