package com.hishop.wine.biz.export.getter;

import cn.hutool.core.bean.BeanUtil;
import com.hishop.common.export.context.DataContext;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.getter.DefaultExportGetter;
import com.hishop.common.export.model.BizType;
import com.hishop.common.response.PageResult;
import com.hishop.wine.biz.MemberBiz;
import com.hishop.wine.biz.ProductBiz;
import com.hishop.wine.biz.export.enums.BizTypeEnum;
import com.hishop.wine.biz.export.model.ProductEO;
import com.hishop.wine.biz.export.wrapper.MemberWrapper;
import com.hishop.wine.biz.export.wrapper.ProductWrapper;
import com.hishop.wine.model.po.member.MemberQueryPO;
import com.hishop.wine.model.po.product.ProductQueryPO;
import com.hishop.wine.model.vo.member.MemberVO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;

/**
 * 产品导出数据获取
 *
 * @author: HuBiao
 * @date: 2023-07-05
 */
@Service
public class MemberGetter extends DefaultExportGetter<MemberQueryPO> {

    @Resource
    private MemberBiz memberBiz;

    @Override
    public DataContext dataContext(MemberQueryPO param) {
        param.setPageNo(1);
        param.setPageSize(Integer.MAX_VALUE);
        PageResult<MemberVO> result = memberBiz.pageList(param);
        DataWrapper<MemberVO> wrapper = new MemberWrapper(result.getList());
        return DataContext.valueOf(Collections.singletonList(wrapper));
    }

    @Override
    public BizType bizType() {
        return BizTypeEnum.MEMBER;
    }

    @Override
    public String fileName() {
        return BizTypeEnum.MEMBER.desc();
    }
}
