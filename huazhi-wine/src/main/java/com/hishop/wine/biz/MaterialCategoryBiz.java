package com.hishop.wine.biz;

import com.hishop.wine.model.po.material.MaterialCategoryCreatePO;
import com.hishop.wine.repository.dto.MaterialCategoryDTO;
import com.hishop.wine.repository.dto.MaterialCategoryTreeDTO;
import com.hishop.wine.common.enums.MaterialType;

import java.util.List;

/**
 * 资源分组表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
public interface MaterialCategoryBiz {

    /**
     * 创建分组
     *
     * @param parentId 上级分组id
     * @param name     分组名称
     * @param type     分组类型
     */
    void create(MaterialCategoryCreatePO materialCategoryCreatePO);

    /**
     * 更新分组
     *
     * @param id       分组id
     * @param parentId 上级分组id
     * @param name     分组名称
     */
    void update(Long id, Long parentId, String name);

    /**
     * 删除分组
     *
     * @param ids            待删除的分组id集合
     * @param deleteMaterial 是否删除分组下的资源;true 删除对应分组下的资源，false 表示不删除资源并标记这些资源的所属分组为"未分组(id为0)"
     */
    void delete(Boolean deleteMaterial, List<Long> ids);

    /**
     * 批量移动
     *
     * @param targetId 目标分组id
     * @param ids      待移动的分组ids集合
     */
    void batchMove(Long targetId, List<Long> ids);

    /**
     * 获取所有资源分组
     *
     * @param materialType 资源库类型
     * @return 以树型结构返回
     */
    List<MaterialCategoryTreeDTO> getAllMaterialCategories(Integer materialType);

    /**
     * 获取指定id的资源分组信息
     *
     * @param id 分组id
     * @return 成功返回对应的分组信息，否则返回null
     */
    MaterialCategoryDTO getById(Long id);

    /**
     * 更新资源数量
     *
     * @param id    分组编号
     * @param total 资源总数
     */
    void updateMaterialCount(Long id, Integer total);

    /**
     * 获取1 2 级分组
     * @param materialType
     * @return
     */
    List<MaterialCategoryTreeDTO> listParent(Integer materialType);

    List<MaterialCategoryTreeDTO> getListByIds(Integer materialType, List<Long> cateIds);

    List<MaterialCategoryTreeDTO> getListLessThanLevel(Integer materialType, int maxLevel, List<Long> cateIds);
}