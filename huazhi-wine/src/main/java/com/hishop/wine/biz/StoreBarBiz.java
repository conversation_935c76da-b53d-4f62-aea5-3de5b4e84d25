package com.hishop.wine.biz;

import com.hishop.wine.model.po.storebar.StoreBarPO;
import com.hishop.wine.model.po.storebar.StoreBarQueryPO;
import com.hishop.wine.model.vo.storeBar.StoreBarVO;

public interface StoreBarBiz {
    /**
     * 添加底部导航
     *
     * @param storeBarPO 导航对象
     * @param xcxSelf    是否小程序独立设置
     * @return
     */
    boolean create(StoreBarPO storeBarPO, boolean xcxSelf);

    /**
     * 修改底部导航
     *
     * @param storeBarPO
     * @return
     */
    boolean update(StoreBarPO storeBarPO);

    /**
     * 查询底部导航详情
     *
     * @param storeBarQueryPO 底部导航查询参数
     * @return 底部导航详情
     */
    StoreBarVO detail(StoreBarQueryPO storeBarQueryPO);
}
