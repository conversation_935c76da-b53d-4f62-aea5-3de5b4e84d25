package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.assist.manager.MicroPageVisitManager;
import com.hishop.wine.biz.MicropageBiz;
import com.hishop.wine.enums.MicropageEnum;
import com.hishop.wine.model.po.micropage.*;
import com.hishop.wine.model.vo.micropage.MicropageDetailVO;
import com.hishop.wine.model.vo.micropage.MicropageVO;
import com.hishop.wine.repository.dao.MicropageCategoryMapper;
import com.hishop.wine.repository.dao.MicropageMapper;
import com.hishop.wine.repository.entity.Micropage;
import com.hishop.wine.repository.entity.MicropageVisit;
import com.hishop.wine.repository.service.MicropageService;
import com.hishop.wine.repository.service.MicropageVisitService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
@AllArgsConstructor
public class MicropageBizImpl implements MicropageBiz {

    @Resource
    private MicropageService micropageService;
    @Resource
    private MicropageCategoryMapper micropageCategoryMapper;
    @Resource
    private MicropageVisitService micropageVisitService;

    private final MicropageMapper mapper;


    @Override
    public boolean create(MicropageCreatePO micropageCreatePO) {
        Micropage model = BeanUtil.copyProperties(micropageCreatePO, Micropage.class);
        model.setStatus(ObjectUtil.defaultIfNull(model.getStatus(), MicropageEnum.MicropageStatus.draft.getCode()));
        mapper.insert(model);
        return true;
    }

    @Override
    public boolean update(MicropageUpdatePO micropageCreatePO) {
        Micropage oldmodel = micropageService.getById(micropageCreatePO.getId());
        if (ObjectUtil.isNull(oldmodel)) {
            throw new BusinessException(String.format("微页面[%s]不存在", micropageCreatePO.getId()));
        }

        Micropage model = BeanUtil.copyProperties(micropageCreatePO, Micropage.class);
        model.setStatus(ObjectUtil.defaultIfNull(model.getStatus(), MicropageEnum.MicropageStatus.draft.getCode()));
        return micropageService.updateById(model);
    }

    @Override
    public MicropageDetailVO detail(Long id) {
        Micropage model = mapper.selectById(id);
        Assert.isTrue(ObjectUtil.isNotNull(model), String.format("微页面[%s]不存在", id));
        MicropageDetailVO dtoModel = BeanUtil.copyProperties(model, MicropageDetailVO.class);
        return dtoModel;
    }

    @Override
    public MicropageDetailVO miniDetail(Long id) {
        MicropageDetailVO result = detail(id);
        pushVisitQueue(id);
        return result;
    }

    @Override
    public boolean changeCategory(MicropageChangeCategoryPO micropageChangeCategoryPO) {
        UpdateWrapper<Micropage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", micropageChangeCategoryPO.getIds())
                .set("category_id", micropageChangeCategoryPO.getCategoryId());
        return mapper.update(null, updateWrapper) > 0;
    }

    @Override
    public boolean deletes(MicropageDeletePO micropageDeletePO) {
        Micropage micropage = new Micropage();
        micropage.setIzDelete(Boolean.TRUE);
        return micropageService.update(micropage, new LambdaQueryWrapper<Micropage>().in(Micropage::getId, micropageDeletePO.getIds()));
    }

    @Override
    public PageResult<MicropageVO> pageList(MicropageQueryPO pagePo) {
        // 没传分类 则返回空
        if (ObjectUtil.isNull(pagePo.getCategoryId())) {
            return PageResultHelper.defaultEmpty(pagePo);
        }

        UpdateWrapper<Micropage> queryWrapper = new UpdateWrapper<>();
        // 未分组
        if (pagePo.getCategoryId().equals(MicropageEnum.Category.UNGROUPED.getValue())) {
            queryWrapper.eq("category_id", pagePo.getCategoryId());
        }
        // 草稿箱
        if (pagePo.getCategoryId().equals(MicropageEnum.Category.DRAFT.getValue())) {
            queryWrapper.eq("status", MicropageEnum.MicropageStatus.draft.getCode());
        }
        // 具体的分组
        if (ObjectUtil.isNotNull(pagePo.getCategoryId()) && pagePo.getCategoryId() >= 0) {
            List<Long> ids = micropageCategoryMapper.recursionChildIds(pagePo.getCategoryId());
            ids.add(pagePo.getCategoryId());
            queryWrapper.in("category_id", ids);
        }

        if (StrUtil.isNotEmpty(pagePo.getName())) {
            queryWrapper.like("name", pagePo.getName());
        }
        queryWrapper.eq("iz_delete", Boolean.FALSE);
        Page<Micropage> page = new Page<>(pagePo.getPageNo(), pagePo.getPageSize());
        Page<Micropage> result = mapper.selectPage(page, queryWrapper);

        return PageResultHelper.transfer(result, MicropageVO.class);
    }

    /**
     * 添加访问记录
     *
     * @param visitList 访问记录
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveVisit(List<MicropageVisit> visitList) {
        // 添加访问明细
        micropageVisitService.saveVisitLog(visitList);

        // 添加汇总数据
        Map<Integer, Long> visitMap = visitList.stream().collect(Collectors.groupingBy(MicropageVisit::getMicropageId, Collectors.counting()));
        micropageService.updateVisitCount(visitMap);
    }

    /**
     * 添加访问数据
     *
     * @param id 微页面id
     */
    private void pushVisitQueue(Long id) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        MicropageVisit param = new MicropageVisit();
        param.setMicropageId(id.intValue());
        param.setUserId(ObjectUtil.isNull(loginUser) ? 0 : loginUser.getUserId());
        param.setVisitCount(1);
        param.setCreateTime(new Date());
        param.setUpdateTime(new Date());

        SpringUtil.getBean(MicroPageVisitManager.class).put(param);
    }
}
