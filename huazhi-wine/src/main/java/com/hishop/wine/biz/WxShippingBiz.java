package com.hishop.wine.biz;


import com.hishop.wine.model.po.wxShipping.request.GetOrderRequest;
import com.hishop.wine.model.po.wxShipping.request.UploadShippingRequest;
import com.hishop.wine.model.po.wxShipping.response.DeliveryListResponse;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
public interface WxShippingBiz {

    Boolean isTradeManaged(String appId);

    Boolean uploadShipping(UploadShippingRequest request);

    Boolean getOrder(GetOrderRequest request);

    DeliveryListResponse getDeliveryList(String appId);

    Boolean uploadShippingByAppId(UploadShippingRequest request);

}
