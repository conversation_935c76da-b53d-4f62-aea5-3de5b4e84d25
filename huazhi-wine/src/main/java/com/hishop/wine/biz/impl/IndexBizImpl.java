package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.entity.BaseEntity;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.biz.IndexBiz;
import com.hishop.wine.biz.MiniAppBiz;
import com.hishop.wine.biz.ModuleBiz;
import com.hishop.wine.biz.WechatCodeBiz;
import com.hishop.wine.common.enums.AuditStatus;
import com.hishop.wine.common.enums.IndexTrendEnum;
import com.hishop.wine.enums.WechatEnum;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.vo.index.*;
import com.hishop.wine.model.vo.miniApp.MiniAppVO;
import com.hishop.wine.model.vo.module.ModuleVO;
import com.hishop.wine.repository.dto.transaction.TransactionTotalDto;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @description: 首页业务逻辑接口实现
 * @author: chenzw
 * @date: 2024/6/25 11:22
 */
@Service
@RequiredArgsConstructor
public class IndexBizImpl implements IndexBiz {

    private final TransactionService transactionService;

    private final CommonMenusService commonMenusService;

    private final ResourceService resourceService;

    private final AppRecommendService appRecommendService;

    @Value("${hishop.jiukeduo.website}")
    private String jiukeduoWebsite;

    private final ModuleBiz moduleBiz;

    private final MiniAppBiz miniAppBiz;

    private final WechatCodeBiz wechatCodeBiz;

    private final MiniUserService miniUserService;

    private final DealerService dealerService;

    private final TerminateService terminateService;

    @Override
    public IndexDataOverviewVo getDataOverview() {
        IndexDataOverviewVo indexDataOverviewVo = new IndexDataOverviewVo();
        indexDataOverviewVo.setUpdateTime(DateUtil.date());
        //今日支付汇总
        TransactionTotalDto todayTotal = transactionService.getTransactionTotal(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.date())), DateUtil.formatDateTime(DateUtil.date()));
        indexDataOverviewVo.setPayUserCountToday(todayTotal.getPayUserCount());
        indexDataOverviewVo.setPayOrderCountToday(todayTotal.getPayOrderCount());
        indexDataOverviewVo.setPayOrderAmountToday(todayTotal.getPayOrderAmount());

        indexDataOverviewVo.setCustomerPriceToday(todayTotal.getPayUserCount() != 0 ? todayTotal.getPayOrderAmount().divide(new BigDecimal(todayTotal.getPayUserCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);

        //昨日支付汇总
        TransactionTotalDto yesterdayTotal = transactionService.getTransactionTotal(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.yesterday())), DateUtil.formatDateTime(DateUtil.endOfDay(DateUtil.yesterday())));
        indexDataOverviewVo.setPayUserCountYesterday(yesterdayTotal.getPayUserCount());
        indexDataOverviewVo.setPayOrderCountYesterday(yesterdayTotal.getPayOrderCount());
        indexDataOverviewVo.setPayOrderAmountYesterday(yesterdayTotal.getPayOrderAmount());
        indexDataOverviewVo.setCustomerPriceYesterday(yesterdayTotal.getPayUserCount() != 0 ? yesterdayTotal.getPayOrderAmount().divide(new BigDecimal(yesterdayTotal.getPayUserCount()), 2, RoundingMode.HALF_UP) : BigDecimal.ZERO);
        return indexDataOverviewVo;
    }

    @Override
    public List<IndexCommonMenuVo> queryCommonMenuList() {
        Long userId = LoginUserUtil.getLoginUser().getUserId();
        List<CommonMenus> commonMenusList = commonMenusService.list(new LambdaQueryWrapper<CommonMenus>()
                .eq(CommonMenus::getUserId, userId).eq(BaseEntity::getIzDelete, DeleteFlagEnums.NO.getCode())
                .orderByAsc(CommonMenus::getId));

        List<IndexCommonMenuVo> result = Lists.newArrayList();
        if (CollectionUtil.isNotEmpty(commonMenusList)) {
            List<Resource> resources = resourceService.list(new LambdaQueryWrapper<Resource>().in(Resource::getId, commonMenusList.stream()
                            .map(CommonMenus::getMenuId)
                            .collect(Collectors.toList()))
                    .eq(Resource::getIzDelete, DeleteFlagEnums.NO.getCode()));
            Map<Long, Resource> resourceMap = resources.stream().collect(Collectors.toMap(Resource::getId, Function.identity()));
            for (CommonMenus commonMenus : commonMenusList) {
                Resource resource = resourceMap.get(commonMenus.getMenuId());
                if (resource != null) {
                    IndexCommonMenuVo indexCommonMenuVo = BeanUtil.copyProperties(resource, IndexCommonMenuVo.class);
                    indexCommonMenuVo.setId(commonMenus.getMenuId());
                    result.add(indexCommonMenuVo);
                }
            }
        }
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void addCommonMenu(IdBatchPO<Long> idBatchPo) {
        Long userId = LoginUserUtil.getLoginUser().getUserId();

        List<CommonMenus> commonMenusList = commonMenusService.list(new LambdaQueryWrapper<CommonMenus>()
                .eq(CommonMenus::getUserId, userId).eq(BaseEntity::getIzDelete, DeleteFlagEnums.NO.getCode())
                .orderByDesc(BaseEntity::getUpdateTime));

        List<Long> addMenuIds = idBatchPo.getIds();
        if (CollectionUtil.isNotEmpty(commonMenusList)) {
            commonMenusService.remove(new LambdaQueryWrapper<CommonMenus>()
                    .in(CommonMenus::getMenuId, commonMenusList.stream()
                            .map(CommonMenus::getMenuId)
                            .collect(Collectors.toList())).eq(CommonMenus::getUserId, userId));
        }

        if (CollectionUtil.isNotEmpty(addMenuIds)) {
            List<CommonMenus> addCommonMenus = addMenuIds.stream().map(menuId -> {
                CommonMenus commonMenus = new CommonMenus();
                commonMenus.setMenuId(menuId);
                commonMenus.setUserId(userId);
                commonMenus.setUpdateBy(userId);
                commonMenus.setUpdateTime(new Date());
                commonMenus.setCreateBy(userId);
                commonMenus.setCreateTime(new Date());
                return commonMenus;
            }).collect(Collectors.toList());
            commonMenusService.saveBatch(addCommonMenus);
        }
    }

    @Override
    public List<IndexBusinessTrendVo> queryIndexBusinessTrendList(IndexTrendEnum indexTrendEnum) {

        List<IndexBusinessTrendVo> result = Lists.newArrayList();

        Date startTime;
        Date endTime;

        switch (indexTrendEnum) {
            case SEVEN_DAYS:
                startTime = DateUtil.offsetDay(DateUtil.yesterday(), -7);
                endTime = DateUtil.endOfDay(DateUtil.yesterday());
                break;
            case THIRTY_DAYS:
                startTime = DateUtil.offsetDay(DateUtil.yesterday(), -30);
                endTime = DateUtil.endOfDay(DateUtil.yesterday());
                break;
            case THIS_MONTH:
                startTime = DateUtil.beginOfMonth(new Date());
                endTime = DateUtil.endOfMonth(new Date());
                break;
            default:
                throw new BusinessException("请求参数错误");
        }

        List<TransactionTotalDto> transactionTotalList = transactionService.queryTransactionTotalList(DateUtil.formatDateTime(startTime), DateUtil.formatDateTime(endTime));

        Map<String, TransactionTotalDto> map = MapUtil.newHashMap(false);
        if (CollectionUtil.isNotEmpty(transactionTotalList)) {
            map = transactionTotalList.stream().collect(Collectors.toMap(TransactionTotalDto::getDateTime, Function.identity()));
        }

        List<DateTime> dateTimes = DateUtil.rangeToList(startTime, endTime, DateField.DAY_OF_YEAR);
        for (DateTime dateTime : dateTimes) {
            TransactionTotalDto transactionTotalDto = map.get(DateUtil.formatDate(dateTime));
            IndexBusinessTrendVo indexBusinessTrendVo = new IndexBusinessTrendVo();
            indexBusinessTrendVo.setDateTime(DateUtil.formatDate(dateTime));
            indexBusinessTrendVo.setPayOrderCount(transactionTotalDto != null ? transactionTotalDto.getPayOrderCount() : 0);
            indexBusinessTrendVo.setPayUserCount(transactionTotalDto != null ? transactionTotalDto.getPayUserCount() : 0);
            indexBusinessTrendVo.setPayOrderAmount(transactionTotalDto != null ? transactionTotalDto.getPayOrderAmount() : BigDecimal.ZERO);
            result.add(indexBusinessTrendVo);
        }
        return result;
    }

    @Override
    public IndexAppWechatVo getIndexAppWechatList() {
        IndexAppWechatVo indexAppWechatVo = new IndexAppWechatVo();
        indexAppWechatVo.setMoreUrl(jiukeduoWebsite);

        //应用推荐
        List<AppRecommend> recommendList = appRecommendService.list(new LambdaQueryWrapper<AppRecommend>()
                .eq(AppRecommend::getIzDelete, DeleteFlagEnums.NO.getCode()));

        List<ModuleVO> listAuthModule = moduleBiz.listAuthModule();

        if (CollectionUtils.isEmpty(listAuthModule)) {
            List<AppRecommendVo> appRecommendVoList = BeanUtil.copyToList(recommendList, AppRecommendVo.class);
            indexAppWechatVo.setAppRecommendList(CollectionUtil.sub(appRecommendVoList, 0, 2));
        } else {
            List<String> list = listAuthModule.stream().map(ModuleVO::getName).collect(Collectors.toList());
            List<AppRecommend> appRecommendList = recommendList.stream().filter(appRecommend -> !list.contains(appRecommend.getName())).collect(Collectors.toList());
            List<AppRecommendVo> appRecommendVoList = BeanUtil.copyToList(appRecommendList, AppRecommendVo.class);
            indexAppWechatVo.setAppRecommendList(CollectionUtil.sub(appRecommendVoList, 0, 2));
        }

        //小程序列表
        List<MiniAppVO> miniAppVoList = miniAppBiz.list();
        List<IndexMiniAppVo> miniAppList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(miniAppVoList)) {
            WechatCodeCreatePO wechatCodeCreatePo = new WechatCodeCreatePO();
            wechatCodeCreatePo.setCodeType(WechatEnum.WxCodeTypeEnum.MINI_APP.getCode());
            wechatCodeCreatePo.setCodeFrom(WechatEnum.WxCodeFromEnum.BASIC_SYSTEM.name());
            for (MiniAppVO miniAppVo : miniAppVoList) {
                if (StringUtils.isNotBlank(miniAppVo.getAppId())) {
                    wechatCodeCreatePo.setAppId(miniAppVo.getAppId());
                    wechatCodeCreatePo.setEnvVersion(miniAppVo.getEnvVersion());
                    miniAppList.add(IndexMiniAppVo.builder()
                            .appName(miniAppVo.getAppName())
                            .qrCode(wechatCodeBiz.genMaCode(wechatCodeCreatePo))
                            .build());
                }
            }
        }
        indexAppWechatVo.setMiniAppList(miniAppList);
        return indexAppWechatVo;
    }

    @Override
    public IndexCustomDataVo getIndexCustomData() {
        Long userCount = miniUserService.getMiniUserCount();
        IndexCustomDataVo result = IndexCustomDataVo.builder()
                .userCount(userCount).dealerCount(0L).terminateCount(0L)
                .build();

        long dealerCount = dealerService.count(new LambdaQueryWrapper<Dealer>()
                .eq(Dealer::getIzEnable, Boolean.TRUE)
                .eq(Dealer::getIzDelete, Boolean.FALSE));
        result.setDealerCount(dealerCount);

        long terminateCount = terminateService.count(new LambdaQueryWrapper<Terminate>()
                .eq(Terminate::getIzEnable, Boolean.TRUE)
                .eq(Terminate::getAuditStatus, AuditStatus.PASS.getType())
                .eq(Terminate::getIzDelete, Boolean.FALSE));
        result.setTerminateCount(terminateCount);
        return result;
    }
}
