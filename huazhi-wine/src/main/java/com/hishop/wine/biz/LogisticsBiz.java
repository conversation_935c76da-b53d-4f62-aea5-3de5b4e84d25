package com.hishop.wine.biz;

import com.alibaba.fastjson.JSONObject;
import com.hishop.wine.model.po.logistics.LogisticsQueryPO;

/**
 * 物流查询
 *
 * <AUTHOR>
 * @date : 2023/8/25
 */
public interface LogisticsBiz {

    /**
     * 物流查询
     *
     * @param queryPO 查询参数
     * @return 物流信息
     */
    JSONObject query(LogisticsQueryPO queryPO);

    /**
     * 查询余额
     *
     * @return 余额
     */
    Integer getBalance();

}
