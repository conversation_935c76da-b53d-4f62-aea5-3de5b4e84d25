package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.po.dealer.DealerSavePo;
import com.hishop.wine.model.po.dealer.DealerUpdateStatusPo;
import com.hishop.wine.model.vo.dealer.DealerDetailVo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * @description: 经销商业务接口
 * @author: chenzw
 * @date: 2024/7/4 15:54
 */
public interface DealerBiz {

    /**
     * 经销商分页列表
     * @param pagePo 查询条件
     * @return PageResult<DealerVo>
     */
    PageResult<DealerVo> pageList(DealerQueryPo pagePo);

    /**
     * 保存经销商信息
     * @param savePo 请求参数
     */
    void saveDealer(DealerSavePo savePo);

    /**
     * 删除经销商信息
     * @param ids 请求参数
     */
    void batchDelete(List<Long> ids);

    /**
     * 经销商详情
     * @param id 请求参数
     * @return DealerDetailVo
     */
    DealerDetailVo detail(Long id);

    /**
     * 更新状态
     * @param dealerUpdateStatusPo 请求参数
     */
    void batchUpdateStatus(DealerUpdateStatusPo dealerUpdateStatusPo);

    /**
     * 导入经销商
     * @param file 文件
     */
    void importDealer(MultipartFile file);

/**
     * 查询经销商列表
     * @param codeList 请求参数
     * @return Object
     */
    List<DealerDetailVo> queryListByCode(List<String> codeList);
}
