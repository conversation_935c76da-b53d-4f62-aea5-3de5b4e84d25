package com.hishop.wine.biz;


import com.hishop.wine.model.vo.wechat.WebQrCodeInitParamVO;
import com.hishop.wine.repository.entity.WechatWebUser;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

/**
 * 微信网页应用用户表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-10
 */

public interface WechatWebUserBiz {

    /**
     * 查询生成小程序码参数
     *
     * @param type 码类型 1-绑定码 2-登陆码
     * @return 生成小程序码参数
     */
    WebQrCodeInitParamVO getQrCodeParam(Integer type);

    /**
     * 绑定微信用户
     *
     * @param code  微信扫码获取的code
     * @param state 校验回调参数
     * @return 微信昵称
     */
    String bind(String code, String state);

    /**
     * 解绑微信
     */
    void unBind();

    /**
     * 获取微信网页用户信息
     *
     * @param code 微信扫码参数
     * @return 微信用户信息
     */
    WechatWebUser getWechatWebUser(String code);

    /**
     * 校验凭证
     *
     * @param state 扫码校验
     */
    void checkState(String state);

    /**
     * 获取微信网页用户信息
     *
     * @param userId 用户id
     * @return 微信用户信息
     */
    WechatWebUser getWechatWebUser(Long userId);
}