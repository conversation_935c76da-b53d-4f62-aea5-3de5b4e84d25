package com.hishop.wine.biz;

import com.hishop.wine.model.po.sms.SendSmsPO;

import java.util.List;

/**
 * 短信业务逻辑
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
public interface SmsBiz {

    /**
     * 查询短信余额
     *
     * @return 短信余额
     */
    Integer getBalance();

    /**
     * 发送短信
     *
     * @param sendSmsPO 发送短信参数
     */
    void sendSms(SendSmsPO sendSmsPO);

    /**
     * 公共发送短信验证码
     *
     * @param mobile 手机号
     * @param key    验证key
     * @param tag    验证码标识
     */
    void sendSmsCode(String mobile, String key, String tag);

    /**
     * 公共校验短信验证码
     *
     * @param mobile    手机号
     * @param key       验证key
     * @param inputCode 验证码
     */
    void checkSmsCode(String mobile, String key, String inputCode);

    /**
     * 移除短信验证码
     *
     * @param mobile 手机号
     * @param key 验证Key
     */
    void removeSmsCode(String mobile, String key);
}
