package com.hishop.wine.biz.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.biz.BaseAddressBiz;
import com.hishop.wine.biz.DistrictBiz;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.enums.DistrictLevelEnum;
import com.hishop.wine.model.po.address.BaseAddressQueryPO;
import com.hishop.wine.model.po.delivery.DeliveryAddressCreatePO;
import com.hishop.wine.model.po.delivery.DeliveryAddressUpdatePO;
import com.hishop.wine.model.po.delivery.DeliveryWxCreatePO;
import com.hishop.wine.model.vo.address.BaseAddressSimpleVO;
import com.hishop.wine.model.vo.basic.DistrictDetailVO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressSimpleVO;
import com.hishop.wine.model.vo.delivery.DeliveryAddressVO;
import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;
import com.hishop.wine.repository.entity.DeliveryAddress;
import com.hishop.wine.biz.DeliveryAddressBiz;
import com.hishop.wine.repository.entity.District;
import com.hishop.wine.repository.entity.LogisticsCompany;
import com.hishop.wine.repository.service.BaseAddressService;
import com.hishop.wine.repository.service.DeliveryAddressService;
import com.hishop.wine.repository.service.DistrictService;
import com.hishop.wine.repository.service.LogisticsCompanyService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;


/**
 * 收货地址表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-29
 */

@Slf4j
@Service
public class DeliveryAddressBizImpl implements DeliveryAddressBiz {

    @Resource
    private DeliveryAddressService deliveryAddressService;
    @Resource
    private DistrictBiz districtBiz;
    @Resource
    private DistrictService districtService;
    @Resource
    private BaseAddressBiz baseAddressBiz;

    //收货地址默认商家userId为0
    private static final int MERCHANT_USERID = 0;

    /**
     * 创建收货地址
     *
     * @param createPO 创建收货地址参数
     * @return 收货地址id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(DeliveryAddressCreatePO createPO) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();

        // 获取具体的地区信息
        DistrictDetailVO districtDetail = districtBiz.getDistrictDetail(createPO.getStreetId());
        Assert.isTrue(districtDetail.getLevel() >= DistrictLevelEnum.AREA.getLevel(), "请至少选择三级地区");

        DeliveryAddress entity = BeanUtil.copyProperties(createPO, DeliveryAddress.class);
        BeanUtil.copyProperties(districtDetail, entity);
        entity.setUserId(loginUser.getUserId());
        entity.setIdentityId(loginUser.getIdentity().getIdentityId());
        deliveryAddressService.save(entity);

        // 如果是标记为默认地址
        if (ObjectUtil.isNotNull(entity.getIzDefault()) && entity.getIzDefault()) {
            setDefault(entity.getId());
        }
        return entity.getId();
    }

    /**
     * 编辑收货地址
     *
     * @param updatePO 编辑收货地址参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(DeliveryAddressUpdatePO updatePO) {
        checkAndGetDeliveryAddress(updatePO.getId());

        // 获取具体的地区信息
        DistrictDetailVO districtDetail = districtBiz.getDistrictDetail(updatePO.getStreetId());
        Assert.isTrue(districtDetail.getLevel() >= DistrictLevelEnum.AREA.getLevel(), "请至少选择三级地区");

        DeliveryAddress updateEntity = BeanUtil.copyProperties(updatePO, DeliveryAddress.class);
        BeanUtil.copyProperties(districtDetail, updateEntity);
        deliveryAddressService.updateById(updateEntity);

        // 如果是标记为默认地址
        if (ObjectUtil.isNotNull(updatePO.getIzDefault()) && updatePO.getIzDefault()) {
            setDefault(updatePO.getId());
        }
    }

    /**
     * 删除收货地址
     *
     * @param id 收货地址id
     */
    @Override
    public void deleteById(Long id) {
        checkAndGetDeliveryAddress(id);
        deliveryAddressService.removeById(id);
    }

    /**
     * 查询收货地址详情
     *
     * @param id 收货地址id
     * @return 收货地址详情
     */
    @Override
    public DeliveryAddressVO getById(Long id) {
        DeliveryAddress entity = deliveryAddressService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(entity), "收货地址不存在");
        return BeanUtil.copyProperties(entity, DeliveryAddressVO.class);
    }

    /**
     * 查修收货地址列表
     *
     * @return 收货地址列表
     */
    @Override
    public List<DeliveryAddressSimpleVO> list() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        List<DeliveryAddress> dbAddressList = deliveryAddressService.list(new LambdaQueryWrapper<DeliveryAddress>()
                .eq(DeliveryAddress::getUserId, loginUser.getUserId()).orderByDesc(DeliveryAddress::getIzDefault));

        List<DeliveryAddressVO> deliveryAddressVOS = BeanUtil.copyToList(dbAddressList, DeliveryAddressVO.class);
        return deliveryAddressVOS.stream().map(DeliveryAddressVO::simpleBuilder).collect(Collectors.toList());
    }

    @Override
    public List<DeliveryAddressSimpleVO> listByMerchant() {
        List<BaseAddressSimpleVO> addressList = baseAddressBiz.list(new BaseAddressQueryPO());

        List<DeliveryAddressSimpleVO> simpleList = new ArrayList<>();
        addressList.forEach(address -> {
            DeliveryAddressSimpleVO simpleVO = BeanUtil.copyProperties(address, DeliveryAddressSimpleVO.class);
            simpleVO.setConsignee(address.getContacts());
            simpleVO.setConsigneePhone(address.getContactsPhone());
            simpleVO.setIzDefault(address.getIzDefaultReceiveAddress());
            simpleList.add(simpleVO);
        });
        return simpleList;
    }

    /**
     * 根据id列表查询收货地址列表
     *
     * @param ids 收货地址id列表
     * @return 收货地址列表
     */
    @Override
    public List<DeliveryAddressVO> getByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return new ArrayList<>();
        }

        List<DeliveryAddress> dbAddressList = deliveryAddressService.list(new LambdaQueryWrapper<DeliveryAddress>().in(DeliveryAddress::getId, ids));
        return BeanUtil.copyToList(dbAddressList, DeliveryAddressVO.class);
    }

    /**
     * 设置为默认地址
     *
     * @param id 地址id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setDefault(Long id) {
        DeliveryAddress deliveryAddress = checkAndGetDeliveryAddress(id);
        Long userId = deliveryAddress.getUserId();

        // 将之前的默认地址设置为非默认
        DeliveryAddress updAddress = new DeliveryAddress();
        updAddress.setIzDefault(Boolean.FALSE);
        deliveryAddressService.update(updAddress, new LambdaQueryWrapper<DeliveryAddress>()
                .eq(DeliveryAddress::getUserId, userId).eq(DeliveryAddress::getIzDefault, Boolean.TRUE));

        updAddress.setIzDefault(Boolean.TRUE);
        updAddress.setId(id);
        deliveryAddressService.updateById(updAddress);
    }

    /**
     * 获取默认收货地址
     *
     * @param userId 用户id
     * @return 默认收货地址
     */
    @Override
    public DeliveryAddressVO getDefaultAddress(Long userId) {
        DeliveryAddress address = deliveryAddressService.getOne(new LambdaQueryWrapper<DeliveryAddress>()
                .eq(DeliveryAddress::getUserId, userId)
                .eq(DeliveryAddress::getIzDefault, Boolean.TRUE)
                .last("limit 1"));
        if (ObjectUtil.isNull(address)) {
            address = deliveryAddressService.getOne(new LambdaQueryWrapper<DeliveryAddress>()
                    .eq(DeliveryAddress::getUserId, userId)
                    .last("limit 1"));
        }
        if (ObjectUtil.isNull(address)) {
            return null;
        }
        return BeanUtil.copyProperties(address, DeliveryAddressVO.class);
    }

    /**
     * 保存微信收货地址
     *
     * @param deliveryWxCreatePO 微信收货地址参数
     * @return 收货地址id
     */
    @Override
    public Long createWxDeliveryAddress(DeliveryWxCreatePO deliveryWxCreatePO) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        DeliveryAddress deliveryAddress = new DeliveryAddress();
        deliveryAddress.setConsignee(deliveryWxCreatePO.getUserName());
        deliveryAddress.setConsigneePhone(deliveryWxCreatePO.getTelNumber());
        deliveryAddress.setProvince(deliveryWxCreatePO.getProvinceName());
        deliveryAddress.setCity(deliveryWxCreatePO.getCityName());
        deliveryAddress.setArea(deliveryWxCreatePO.getCountyName());
        deliveryAddress.setStreet(deliveryWxCreatePO.getStreetName());
        deliveryAddress.setAddress(deliveryWxCreatePO.getDetailInfoNew());
        deliveryAddress.setUserId(loginUser.getUserId());
        deliveryAddress.setIdentityId(loginUser.getIdentity().getIdentityId());
        // 匹配省
        List<String> districtNameList = Arrays.asList(deliveryAddress.getProvince(), deliveryAddress.getCity(),
                deliveryAddress.getArea(), deliveryAddress.getStreet());
        List<Integer> districtIdList = new ArrayList<>();
        for (int i = 0 ; i < districtNameList.size(); i ++) {
            Integer parentId = i == 0 ? BasicConstants.ROOT_DISTRICT_PARENT_ID : districtIdList.get(i - 1);
            District district = districtService.getOne(new LambdaQueryWrapper<District>()
                    .eq(District::getParentId, parentId).eq(District::getName, districtNameList.get(i)));
            districtIdList.add(ObjectUtil.isNotNull(district) ? district.getId() : 0);
        }
        deliveryAddress.setProvinceId(districtIdList.get(0));
        deliveryAddress.setCityId(districtIdList.get(1));
        deliveryAddress.setAreaId(districtIdList.get(2));
        deliveryAddress.setStreetId(districtIdList.get(3));
        //根据省市区街道id以及userId查询是否存在地址
        DeliveryAddress dbAddress = deliveryAddressService.getExist(deliveryAddress);
        if (dbAddress != null) {
            return dbAddress.getId();
        }
        deliveryAddressService.save(deliveryAddress);
        return deliveryAddress.getId();
    }

    /**
     * 检测并获取收货地址
     *
     * @param id 收货地址id
     * @return 收获地址信息
     */
    private DeliveryAddress checkAndGetDeliveryAddress(Long id) {
        DeliveryAddress entity = deliveryAddressService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(entity), "收货地址不存在");
        Assert.isTrue(LoginUserUtil.getLoginUser().getUserId().equals(entity.getUserId()), "无权操作");
        return entity;
    }
}