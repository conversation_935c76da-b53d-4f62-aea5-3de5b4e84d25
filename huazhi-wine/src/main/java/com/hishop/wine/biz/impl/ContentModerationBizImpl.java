package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.generator.SnowflakeGenerator;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.moderation.handler.Moderation;
import com.hishop.moderation.model.*;
import com.hishop.moderation.support.ImageModerationConfig;
import com.hishop.moderation.support.ModerationConfig;
import com.hishop.moderation.support.TextModerationConfig;
import com.hishop.moderation.support.VideoModerationConfig;
import com.hishop.mq.api.MQ;
import com.hishop.nfs.api.NFS;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.biz.ContentModerationBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.enums.ModerationEnum;
import com.hishop.wine.model.po.moderation.ContentModerationCallbackPO;
import com.hishop.wine.model.po.moderation.ContentModerationCreatePO;
import com.hishop.wine.model.vo.setting.ContentReviewSettingVO;
import com.hishop.wine.repository.entity.ContentModeration;
import com.hishop.wine.repository.entity.ContentModerationInfo;
import com.hishop.wine.repository.param.moderation.ContentModerationInfoParam;
import com.hishop.wine.repository.param.moderation.ContentModerationParam;
import com.hishop.wine.repository.service.ContentModerationInfoService;
import com.hishop.wine.repository.service.ContentModerationService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**
 * 内容审核表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-09-11
 */
@Slf4j
@Service
public class ContentModerationBizImpl implements ContentModerationBiz {

    @Resource
    private ContentModerationService contentModerationService;
    @Resource
    private ContentModerationInfoService contentModerationInfoService;
    @Resource
    private BasicSettingBiz basicSettingBiz;
    @Resource
    private SnowflakeGenerator snowflakeGenerator;
    @Resource
    private Moderation moderation;
    @Resource
    private NFS nfs;
    @Resource
    private MQ mq;

    private static final Integer QUERY_ONCE_LIMIT = 100;
    private static final String DEFAULT_REGION = "cn-east-3";

    private static final String NOT_FOUND_ERROR_CODE = "AIS.0030";

    private static final String VIDEO_EXPIRED_ERROR_CODE = "AIS.0035";

    /**
     * 创建审核记录
     *
     * @param createPO 发起审核参数
     * @return 审核id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long create(ContentModerationCreatePO createPO) {
        // 检测审核的内容至少要有一个
        createPO.check();

        ContentModerationParam param = ContentModerationParam.builder()
                .bizType(createPO.getBizType().name())
                .bizCode(createPO.getBizCode())
                .statusList(Arrays.asList(ModerationEnum.Status.WAITING.name(), ModerationEnum.Status.AUDITING.name())).build();
        Long count = contentModerationService.count(param);
        Assert.isTrue(count == 0, "该内容已经在审核中, 请勿重复提交");

        // 保存主表记录
        ModerationEnum.BizType bizType = createPO.getBizType();
        ContentModeration entity = BeanUtil.copyProperties(createPO, ContentModeration.class);
        entity.setId(snowflakeGenerator.next());
        entity.setModuleCode(bizType.getModuleCode());
        entity.setBizType(bizType.name());
        entity.setStatus(ModerationEnum.Status.WAITING.name());
        contentModerationService.save(entity);

        // 保存明细表记录
        List<ContentModerationInfo> infoList = new ArrayList<>();
        infoList.addAll(buildModerationInfo(entity.getId(), ModerationEnum.Type.TEXT.name(), createPO.getTextList()));
        infoList.addAll(buildModerationInfo(entity.getId(), ModerationEnum.Type.IMAGE.name(), createPO.getImageList()));
        infoList.addAll(buildModerationInfo(entity.getId(), ModerationEnum.Type.VIDEO.name(), createPO.getVideoList()));
        infoList.addAll(buildModerationInfo(entity.getId(), ModerationEnum.Type.AUDIO.name(), createPO.getAudioList()));
        contentModerationInfoService.saveBatch(infoList);
        return entity.getId();
    }

    /**
     * 发起内容审核
     */
    @Override
    public void launchModeration() {
        ContentReviewSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.CONTENT_REVIEW_SETTING);
        // 如果未开启审批, 则不执行
        if (!setting.getEnable()) {
            return;
        }

        // 构建配置信息
        ModerationConfig config = buildModerationConfig(setting);

        // 循环检测待审核的数据
        ContentModerationInfoParam param = buildDefaultParam(ModerationEnum.Status.WAITING.name());
        List<ContentModerationInfo> infoList = contentModerationInfoService.list(param);
        ContentModerationBiz biz = SpringUtil.getBean(ContentModerationBiz.class);
        while (!CollectionUtils.isEmpty(infoList)) {
            infoList.forEach(info -> {
                biz.launchModeration(config, info);
            });

            infoList = contentModerationInfoService.list(param);
        }
    }

    /**
     * 发起审核
     *
     * @param config 审核配置
     * @param info   审核明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void launchModeration(ModerationConfig config, ContentModerationInfo info) {
        ModerationEnum.Type type = ModerationEnum.Type.valueOf(info.getModerationType());
        String moderationData = info.getModerationData();

        // 发起审批获取结果
        ModerationResult result = null;

        //当前审核数据类型不为文本时且值为空，直接审核失败
        if (StringUtils.isEmpty(moderationData) && !ModerationEnum.Type.TEXT.name().equals(info.getModerationType())) {
            info.setStatus(ModerationEnum.Status.FAIL.name());
            info.setRemark("审核失败, 审核数据为空");
        } else {
            switch (type) {
                case TEXT:
                    result = moderation.checkText(BeanUtil.copyProperties(config, TextModerationConfig.class), TextData.valueOf(moderationData));
                    break;
                case IMAGE:
                    ImageData imageData = ImageData.valueOf(nfs.getFileUrl(moderationData));
                    result = moderation.checkImage(BeanUtil.copyProperties(config, ImageModerationConfig.class), Arrays.asList(imageData));
                    break;
                case VIDEO:
                    VideoData videoData = new VideoData();
                    videoData.setUrl(nfs.getFileUrl(moderationData));
                    result = moderation.checkVideo(BeanUtil.copyProperties(config, VideoModerationConfig.class), videoData);
                    break;
                default:
                    info.setStatus(ModerationEnum.Status.FAIL.name());
                    info.setRemark("审核失败, 不支持的媒体类型: " + type.getDesc());
                    break;
            }

            if (result != null) {
                // 接口调用成功
                if (result.isSuccess()) {
                    // 如果是文本类型, 是同步审核, 直接更新审核结果
                    if (ModerationEnum.Type.TEXT.name().equals(info.getModerationType())) {
                        info.setStatus(SuggestionType.PASS.equals(result.getSuggestion())
                                ? ModerationEnum.Status.SUCCESS.name() : ModerationEnum.Status.FAIL.name());
                        info.setSuggestion(result.getSuggestion().name());
                        info.setMessage(result.getMessage());
                        info.setModerationResult(StrUtil.emptyIfNull(JSONUtil.toJsonStr(result.getData())));
                    } else {
                        info.setStatus(ModerationEnum.Status.AUDITING.name());
                        info.setTraceNo(result.getTraceNo());
                    }
                } else {
                    if (NOT_FOUND_ERROR_CODE.equals(result.getErrorCode())) {
                        info.setStatus(ModerationEnum.Status.FAIL.name());
                        info.setRemark("审核失败, 审核数据不存在");
                    } else {
                        // 接口调用失败, 下次定时任务再进行重试
                        log.error("发起审核失败, traceNo: {}, message: {}", result.getTraceNo(), result.getMessage());
                    }
                }
            }
        }

        // 更新审核记录
        info.setUpdateTime(new Date());
        contentModerationInfoService.updateById(info);

        // 检查主审核记录的状态
        checkMainStatus(info.getModerationId());
    }

    /**
     * 检查异步审核结果
     */
    @Override
    public void queryModerationResult() {
        ContentReviewSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.CONTENT_REVIEW_SETTING);
        // 如果未开启审批, 则不执行
        if (!setting.getEnable()) {
            return;
        }

        // 构建配置信息
        ModerationConfig config = buildModerationConfig(setting);

        // 构建查询参数
        ContentModerationInfoParam param = buildDefaultParam(ModerationEnum.Status.AUDITING.name());
        // 循环检测审核中的数据
        List<ContentModerationInfo> infoList = contentModerationInfoService.list(param);
        ContentModerationBiz biz = SpringUtil.getBean(ContentModerationBiz.class);
        while (!CollectionUtils.isEmpty(infoList)) {
            infoList.forEach(info -> {
                biz.queryModerationResult(config, info);
            });

            infoList = contentModerationInfoService.list(param);
        }
    }

    /**
     * 检查异步审核结果
     *
     * @param config 审核配置
     * @param info   审核明细
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void queryModerationResult(ModerationConfig config, ContentModerationInfo info) {
        ModerationEnum.Type type = ModerationEnum.Type.valueOf(info.getModerationType());

        // 查询异步审核结果
        ModerationResult result = null;
        switch (type) {
            case IMAGE:
                result = moderation.queryImageResult(config, info.getTraceNo());
                break;
            case VIDEO:
                result = moderation.queryVideoResult(config, info.getTraceNo());
                break;
            default:
                info.setStatus(ModerationEnum.Status.FAIL.name());
                info.setRemark("查询审核失败, 不支持的媒体类型: " + type.getDesc());
                break;
        }

        // 接口调用成功
        if (result.isSuccess()) {
            switch (result.getJobStatus()) {
                case WAITING:
                    log.info("审核任务暂未执行完成, traceNo: {}", info.getTraceNo());
                    break;
                case SUCCESS:
                    info.setStatus(ModerationEnum.Status.SUCCESS.name());
                    info.setSuggestion(result.getSuggestion().name());
                    break;
                case FAIL:
                    info.setStatus(ModerationEnum.Status.FAIL.name());
                    info.setRemark("华为云审核任务执行失败, 请重试");
                    break;
                default:
                    info.setStatus(ModerationEnum.Status.FAIL.name());
                    info.setRemark("华为云审核任务执行失败, 未知的任务状态: " + result.getJobStatus().name());
                    break;
            }
            info.setModerationResult(JSONUtil.toJsonStr(result.getData()));
        } else {

            if (NOT_FOUND_ERROR_CODE.equals(result.getErrorCode())) {
                info.setStatus(ModerationEnum.Status.FAIL.name());
                info.setRemark("查询审核结果失败, 审核数据不存在");
            } else if ((VIDEO_EXPIRED_ERROR_CODE.equals(result.getErrorCode()) && ModerationEnum.Type.VIDEO.name().equals(info.getModerationType()))) {
                info.setStatus(ModerationEnum.Status.FAIL.name());
                info.setRemark("查询审核结果失败, 审核数据已过期");
            } else {
                // 接口调用失败, 下次定时任务再进行重试
                log.error("查询审核结果失败, traceNo: {}, message: {}", info.getTraceNo(), result.getMessage());
            }
        }

        // 更新审核记录
        info.setMessage(StrUtil.emptyIfNull(result.getMessage()));
        info.setUpdateTime(new Date());
        contentModerationInfoService.updateById(info);

        // 检查主审核记录的状态
        checkMainStatus(info.getModerationId());
    }

    /**
     * 构建审核明细记录
     *
     * @param moderationId   审核id
     * @param moderationType 审核类型
     * @param dataList       审核数据集合
     * @return 审核明细记录
     */
    private List<ContentModerationInfo> buildModerationInfo(Long moderationId, String moderationType, List<String> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            return Collections.emptyList();
        }

        return dataList.stream().map(data -> {
            ContentModerationInfo info = new ContentModerationInfo();
            info.setModerationId(moderationId);
            info.setModerationType(moderationType);
            info.setModerationData(data);
            info.setStatus(ModerationEnum.Status.WAITING.name());
            return info;
        }).collect(Collectors.toList());
    }

    /**
     * 检查主审核记录的状态
     * 注意: 如果有并发的情况, 此方法会存在隐患
     *
     * @param moderationId 审核Id
     */
    private void checkMainStatus(Long moderationId) {
        // 更新主审核记录的状态
        contentModerationService.refreshStatus(moderationId);

        // 回调业务系统
        callBackBizSystem(moderationId);
    }

    /**
     * 回调业务系统
     *
     * @param moderationId 审核Id
     */
    private void callBackBizSystem(Long moderationId) {
        // 获取最新的审核记录
        ContentModeration moderation = contentModerationService.getById(moderationId);

        ModerationEnum.BizType bizType = ModerationEnum.BizType.getEnum(moderation.getBizType());
        if (ObjectUtil.isNull(bizType) || StrUtil.isEmpty(bizType.getCallBackTopic())) {
            return;
        }

        String status = moderation.getStatus();
        if (ModerationEnum.Status.SUCCESS.name().equals(status) || ModerationEnum.Status.FAIL.name().equals(status)) {
            ContentModerationCallbackPO callback = new ContentModerationCallbackPO();
            callback.setBizType(bizType);
            callback.setBizCode(moderation.getBizCode());
            callback.setStatus(status);
            if (ModerationEnum.Status.SUCCESS.name().equals(status)) {
                callback.setIzSuccess(Boolean.TRUE);
            } else {
                callback.setIzSuccess(Boolean.FALSE);
                // 查询失败原因
                callback.setMessage(queryFailReason(moderationId));
            }
            mq.publish(bizType.getCallBackTopic(), callback);
        }
    }

    /**
     * 查询失败原因
     *
     * @param moderationId 审核Id
     * @return 失败原因
     */
    private String queryFailReason(Long moderationId) {
        return contentModerationInfoService.list(new LambdaQueryWrapper<ContentModerationInfo>()
                        .eq(ContentModerationInfo::getModerationId, moderationId)
                        .ne(ContentModerationInfo::getMessage, StrUtil.EMPTY))
                .stream().map(ContentModerationInfo::getMessage)
                .collect(Collectors.joining(";"));
    }

    /**
     * 构建内容审核配置类
     *
     * @param setting 配置信息
     */
    private ModerationConfig buildModerationConfig(ContentReviewSettingVO setting) {
        // 创建审批配置
        ModerationConfig config = new ModerationConfig();
        config.setRegion(ObjectUtil.defaultIfNull(setting.getRegion(), DEFAULT_REGION));
        config.setAccessKey(setting.getAppKey());
        config.setSecreteKey(setting.getAppSecret());
        return config;
    }

    /**
     * 构建默认的审核明细查询参数
     *
     * @param status 审核状态
     * @return 审核明细查询参数
     */
    private ContentModerationInfoParam buildDefaultParam(String status) {
        LinkedHashMap<String, Boolean> sortField = new LinkedHashMap<>();
        sortField.put("updateTime", true);
        return ContentModerationInfoParam.builder()
                .status(status)
                .maxLimit(QUERY_ONCE_LIMIT)
                .sortField(sortField)
                .build();
    }

}