package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.logisticsCode.BatchLogisticsCodeStatusPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeFeignPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeStatusPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;

import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * <AUTHOR>
 * @Date 2024/07/09/ $
 * @description:
 */
public interface LogisticsCodeBiz {

    /**
     * 获取库中已存在的箱码
     * @param codes 箱码
     * @return 存在的箱码
     */
    Set<String> getCaseCodes(Set<String> codes);
    /**
     * 获取库中已存在的盒码
     * @param codes 盒码
     * @return 存在的盒码
     */
    Set<String> getBoxCodes(Set<String> codes);

    /**
     * 获取库中已存在的产品码
     * @param codes 产品码
     * @return 存在的产品码
     */
    Map<String, Long> getProductCode(Set<String> codes);

    /**
     * 获取库中已存在的瓶码
     * @param codes 瓶码
     * @return 存在的瓶码
     */
    Set<String> getBottleCode(Set<String> codes);

    /**
     * 分页查询
     * @param queryPo 查询条件
     * @return 分页结果
     */
    PageResult<LogisticsCodeVo> pageList(LogisticsCodeQueryPo queryPo);

    /**
     * 删除物流码
     * @param ids 物流码id
     */
    void deleteByIds(List<Long> ids);

    /**
     * 批量更新物流码状态
     * @param codeStatusPo 物流码状态
     */
    void batchUpdateStatus(LogisticsCodeStatusPo codeStatusPo);

    /**
     * 获取物流码信息
     * @param code 物流码
     * @return 物流码信息
     */
    LogisticsCodeVo getLogisticsCodeInfo(String code);

    /**
     * 获取库中已存在的物流码
     * @param codes 物流码
     * @return 物流码信息
     */
    Set<String> getCodes(Set<String> codes);

    /**
     * 批量更新物流码使用状态
     * @param po 物流码状态
     * @return 已使用的物流码
     */
    Set<String> batchUpdateLogisticsCodesStatus(BatchLogisticsCodeStatusPo po);

    /**
     * 根据批次id获取物流码信息
     * @param po 入参
     * @return 物流码信息
     */
    List<LogisticsCodeVo> getLogisticsCodeByFileImportIds(LogisticsCodeFeignPo po);

    /**
     * 批量更新物流码未使用状态
     * @param po 物流码状态
     */
    void batchUpdateLogisticsCodesStatusNoUsed(BatchLogisticsCodeStatusPo po);
}
