package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.biz.TagsBiz;
import com.hishop.wine.model.po.TagsCreatePO;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.po.TagsUpdatePO;
import com.hishop.wine.model.po.minUser.MinUserDelTagPo;
import com.hishop.wine.model.po.minUser.MiniUserTagsPo;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.dto.MyTagsDto;
import com.hishop.wine.repository.dto.TagsCountDTO;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.Tags;
import com.hishop.wine.repository.entity.UserTags;
import com.hishop.wine.repository.service.MiniUserService;
import com.hishop.wine.repository.service.TagsService;
import com.hishop.wine.repository.service.UserTagsService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;


/**   
 * 标签表 业务逻辑实现类
 * @author: chenpeng
 * @date: 2023-07-17
 */

@Slf4j
@Service
public class TagsBizImpl implements TagsBiz {

    @Resource
    private TagsService tagsService;

    @Resource
    private MiniUserService miniUserService;

    @Resource
    private UserTagsService userTagsService;

    @Override
    public void create(TagsCreatePO createPO) {

        long count = tagsService.count(new LambdaQueryWrapper<Tags>().eq(Tags::getTagName, createPO.getTagName()));
        if(count > 0) {
            Assert.isTrue(false, "标签名称已经存在");
        }
        Tags entity = BeanUtil.copyProperties(createPO, Tags.class);
        tagsService.save(entity);
    }

    @Override
    public void deleteById(Long id) {
        tagsService.removeById(id);
    }

    @Override
    public TagsVO detail(Long id) {
        Tags entity = tagsService.getById(id);
        if(entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        return BeanUtil.copyProperties(entity, TagsVO.class);
    }

    @Override
    public List<TagsVO> list() {
        List<Tags> dbList = tagsService.list();
        return BeanUtil.copyToList(dbList, TagsVO.class);
    }

    @Override
    public PageResult<TagsVO> pageList(TagsQueryPO pagePO) {
        Page<Tags> page = pagePO.buildPage();
        Page<Tags> tagsPage = tagsService.pageList(page, pagePO);
        return PageResultHelper.transfer(tagsPage, TagsVO.class);
    }

    @Override
    public void update(TagsUpdatePO tagsUpdatePO) {
        Tags entity = BeanUtil.copyProperties(tagsUpdatePO, Tags.class);
        tagsService.updateById(entity);
    }

    @Override
    public MyTagsDto getMyList(TagsQueryPO pagePO) {
        MiniUser miniUser = miniUserService.getById(pagePO.getId());
        MyTagsDto dto = new MyTagsDto();
        Integer num = 30;
        List<TagsVO> myTags = tagsService.getMyList(miniUser.getUserId());
        List<Long> tagIds = myTags.stream().map(TagsVO::getId).collect(Collectors.toList());
        List<TagsVO> topTags = tagsService.getTopList(num, tagIds, pagePO.getTagName());
        dto.setMyTags(myTags);
        dto.setTopTags(topTags);
        return dto;
    }

    @Override
    public MyTagsDto getBatchList(TagsQueryPO pagePO) {
        MyTagsDto dto = new MyTagsDto();
        Integer num = 30;
        List<TagsVO> topTags = tagsService.getTopList(num, Lists.newArrayList(), pagePO.getTagName());
        dto.setMyTags(Lists.newArrayList());
        dto.setTopTags(topTags);
        return dto;
    }

    @Override
    public MyTagsDto getDetailList(TagsQueryPO pagePO) {
        MiniUser miniUser = miniUserService.getById(pagePO.getId());
        MyTagsDto dto = new MyTagsDto();
        Integer num = 30;
        List<TagsVO> myTags = tagsService.getMyList(miniUser.getUserId());
        List<Long> tagIds = myTags.stream().map(TagsVO::getId).collect(Collectors.toList());
        List<TagsVO> topTags = tagsService.getTopList(num, tagIds, pagePO.getTagName());
        dto.setMyTags(Lists.newArrayList());
        dto.setTopTags(topTags);
        return dto;
    }

    @Override
    public void tags(MiniUserTagsPo miniUserTagsPo) {
        // 分单打与批量 两种情况
        if(miniUserTagsPo.getIdList().size() > 1) {
            Set<Long> userSet = miniUserService.list(new LambdaQueryWrapper<MiniUser>().in(MiniUser::getId, miniUserTagsPo.getIdList())).stream().map(MiniUser::getUserId).collect(Collectors.toSet());
            Map<Long, List<Long>> userTagsIdMap = userTagsService.list(new LambdaQueryWrapper<UserTags>().in(UserTags::getUserId, userSet))
                    .stream().collect(Collectors.groupingBy(UserTags::getUserId, Collectors.mapping(UserTags::getTagId, Collectors.toList())));
            // 查出所有选中标签的信息
            Map<Long, String> map = tagsService.list(new LambdaQueryWrapper<Tags>().in(Tags::getId, miniUserTagsPo.getTagIdList())).stream().map(tags -> {
                return new HashMap.SimpleEntry<>(tags.getId(), tags.getTagName());
            }).collect(Collectors.toMap(HashMap.SimpleEntry::getKey, HashMap.SimpleEntry::getValue));
            // 统计选中客户已有的标签数量
            Map<Long, Integer> countMap = userTagsService.userTagsCount(userSet).stream().collect(Collectors.toMap(TagsCountDTO::getUserId, TagsCountDTO::getNum));
            // 按userId组装数据
            Map<Long, List<UserTags>> userTagsMap = Maps.newHashMap();
            userSet.forEach(userId -> {
                List<UserTags> userTagsList = userTagsMap.get(userId) != null ? userTagsMap.get(userId) : Lists.newArrayList();
                userTagsMap.put(userId, userTagsList);
                List<Long> userTagIds = userTagsIdMap.get(userId) != null ? userTagsIdMap.get(userId) : Lists.newArrayList();
                miniUserTagsPo.getTagIdList().forEach(tagId -> {
                    if (!userTagIds.contains(tagId)) {
                        UserTags userTags = new UserTags();
                        userTags.setUserId(userId);
                        userTags.setTagId(tagId);
                        userTags.setTagName(StringUtils.isNotBlank(map.get(tagId)) ? map.get(tagId) : "");
                        userTags.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
                        userTags.setCreateTime(new Date());
                        userTags.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
                        userTags.setUpdateTime(new Date());
                        userTagsList.add(userTags);
                    }
                });
            });
            // 组成列表批量插入
            List<UserTags> utList = Lists.newArrayList();
            boolean flag = false;
            // 遍历userTagsMap
            for(Long userId : userTagsMap.keySet()) {
                Integer oldNum = countMap.get(userId) != null ? countMap.get(userId) : 0;
                Integer newNum = userTagsMap.get(userId).size();
                if(oldNum + newNum > 15) {
                    flag = true;
                } else {
                    utList.addAll(userTagsMap.get(userId));
                }
            }
            if(utList.size() > 0) {
                userTagsService.saveBatch(utList);
            }
            if(flag) {
                log.error("标签最多可添加15个，部分客户的部分标签未添加成功，请逐个核实修改客户标签");
                throw new BusinessException("标签最多可添加15个，部分客户的部分标签未添加成功，请逐个核实修改客户标签");
            }
        } else {
            // 查出所有选中标签的信息
            Map<Long, String> map = tagsService.list(new LambdaQueryWrapper<Tags>().in(Tags::getId, miniUserTagsPo.getTagIdList())).stream().map(tags -> {
                return new HashMap.SimpleEntry<>(tags.getId(), tags.getTagName());
            }).collect(Collectors.toMap(HashMap.SimpleEntry::getKey, HashMap.SimpleEntry::getValue));
            // 查到小程序用户
            MiniUser miniUser = miniUserService.getById(miniUserTagsPo.getIdList().get(0));
            // 清除之前的旧标签
            userTagsService.remove(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, miniUser.getUserId()));
            List<UserTags> utList = Lists.newArrayList();
            miniUserTagsPo.getTagIdList().forEach(tagId -> {
                UserTags userTags = new UserTags();
                userTags.setUserId(miniUser.getUserId());
                userTags.setTagId(tagId);
                userTags.setTagName(StringUtils.isNotBlank(map.get(tagId)) ? map.get(tagId) : "");
                userTags.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
                userTags.setCreateTime(new Date());
                userTags.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
                userTags.setUpdateTime(new Date());
                utList.add(userTags);
            });
            if(utList.size() > 0) {
                userTagsService.saveBatch(utList);
            }
        }
    }

    @Override
    public void detailTags(MiniUserTagsPo miniUserTagsPo) {
        // 查出所有选中标签的信息
        Map<Long, String> map = tagsService.list(new LambdaQueryWrapper<Tags>().in(Tags::getId, miniUserTagsPo.getTagIdList())).stream().map(tags -> {
            return new HashMap.SimpleEntry<>(tags.getId(), tags.getTagName());
        }).collect(Collectors.toMap(HashMap.SimpleEntry::getKey, HashMap.SimpleEntry::getValue));
        // 查到小程序用户
        MiniUser miniUser = miniUserService.getById(miniUserTagsPo.getIdList().get(0));
        Set<Long> userSet = Sets.newHashSet();
        userSet.add(miniUser.getUserId());
        Map<Long, Integer> countMap = userTagsService.userTagsCount(userSet).stream().collect(Collectors.toMap(TagsCountDTO::getUserId, TagsCountDTO::getNum));
        Integer oldNum = countMap.get(miniUser.getUserId()) != null ? countMap.get(miniUser.getUserId()) : 0;
        if(oldNum + miniUserTagsPo.getTagIdList().size() > 15) {
            log.error("标签最多只可添加15个");
            throw new BusinessException("标签最多只可添加15个");
        }
        List<UserTags> utList = Lists.newArrayList();
        miniUserTagsPo.getTagIdList().forEach(tagId -> {
            UserTags userTags = new UserTags();
            userTags.setUserId(miniUser.getUserId());
            userTags.setTagId(tagId);
            userTags.setTagName(StringUtils.isNotBlank(map.get(tagId)) ? map.get(tagId) : "");
            userTags.setCreateBy(LoginUserUtil.getLoginUser().getUserId());
            userTags.setCreateTime(new Date());
            userTags.setUpdateBy(LoginUserUtil.getLoginUser().getUserId());
            userTags.setUpdateTime(new Date());
            utList.add(userTags);
        });
        if(utList.size() > 0) {
            userTagsService.saveBatch(utList);
        }
    }

    @Override
    public void delTag(MinUserDelTagPo minUserDelTagPo) {
        MiniUser miniUser = miniUserService.getById(minUserDelTagPo.getId());
        userTagsService.remove(new LambdaQueryWrapper<UserTags>().eq(UserTags::getUserId, miniUser.getUserId()).eq(UserTags::getTagId, minUserDelTagPo.getTagId()));
    }

}