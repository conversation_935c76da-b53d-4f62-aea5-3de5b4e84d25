package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.annotation.RedisLock;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.log.model.OperationRecord;
import com.hishop.wine.biz.SystemLogBiz;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.model.po.log.SystemLogQueryPO;
import com.hishop.wine.model.vo.log.SystemLogDetailVO;
import com.hishop.wine.model.vo.log.SystemLogVO;
import com.hishop.wine.repository.entity.SystemLog;
import com.hishop.wine.repository.entity.SystemLogDetails;
import com.hishop.wine.repository.param.log.SystemLogParam;
import com.hishop.wine.repository.service.SystemLogDetailsService;
import com.hishop.wine.repository.service.SystemLogService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**
 * 操作日志表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-08-01
 */

@Slf4j
@Service
public class SystemLogBizImpl implements SystemLogBiz {

    @Resource
    private SystemLogService systemLogService;
    @Resource
    private SystemLogDetailsService systemLogDetailsService;

    /**
     * 分页查询日志记录
     *
     * @param pagePO 筛选参数
     * @return 日志记录
     */
    @Override
    public PageResult<SystemLogVO> pageList(SystemLogQueryPO pagePO) {
        SystemLogParam param = BeanUtil.copyProperties(pagePO, SystemLogParam.class);
        Page<SystemLog> dbPage = systemLogService.qryPage(pagePO.buildPage(), param);
        return PageResultHelper.transfer(dbPage, SystemLogVO.class, vo -> {
            vo.setModuleName(ModuleEnums.getDesc(vo.getModuleCode()));
        });
    }

    /**
     * 日志详情
     *
     * @param id 日志id
     * @return 日志详情
     */
    @Override
    public SystemLogDetailVO detail(Long id) {
        SystemLog log = systemLogService.getById(id);
        Assert.isTrue(ObjectUtil.isNotNull(log), "日志不存在");
        SystemLogDetailVO vo = BeanUtil.copyProperties(log, SystemLogDetailVO.class, "changeDataList");

        SystemLogDetails details = systemLogDetailsService.getByLogId(id);
        vo.setChangeDataList(JSONArray.parseArray(details.getChangeDataList()));
        return vo;
    }

    /**
     * 记录操作日志
     *
     * @param record 操作日志
     */
    @Override
    @RedisLock(name = "operation_log", key = "#record.traceNo")
    @Transactional(rollbackFor = Exception.class)
    public void createOperationLog(OperationRecord record) {
        // 幂等校验
        long count = systemLogService.count(new LambdaQueryWrapper<SystemLog>().eq(SystemLog::getTraceNo, record.getTraceNo()));
        if (count > 0) {
            return;
        }

        // 保存主表记录
        SystemLog systemLog = new SystemLog();
        systemLog.setModuleCode(record.getModuleCode());
        systemLog.setBusinessSector(record.getBusinessSector());
        systemLog.setOperationType(record.getOperationType().name());
        systemLog.setOperationName(record.getOperationName());
        systemLog.setBusinessKey(record.getBusinessKey());
        systemLog.setBusinessDesc(record.getBusinessDesc());
        systemLog.setRequestUrl(record.getRequestUrl());
        systemLog.setRequestParam(JSONUtil.toJsonStr(record.getRequestParam()));
        systemLog.setResponse(JSONUtil.toJsonStr(record.getResponse()));
        systemLog.setCostTime(record.getCostTime());
        systemLog.setCreateBy(record.getCreateBy());
        systemLog.setCreateTime(record.getCreateTime());
        systemLog.setOperatorUsername(record.getOperatorUsername());
        systemLog.setOperatorMobile(record.getOperatorMobile());
        systemLog.setTraceNo(record.getTraceNo());
        systemLogService.save(systemLog);

        // 保存明细表
        SystemLogDetails details = new SystemLogDetails();
        details.setLogId(systemLog.getId());
        details.setPrimaryList(JSONUtil.toJsonStr(record.getPrimaryList()));
        details.setOldDataList(JSONUtil.toJsonStr(record.getOldDataList()));
        details.setNewDataList(JSONUtil.toJsonStr(record.getNewDataList()));
        details.setChangeDataList(JSONUtil.toJsonStr(record.getChangeDataList()));
        systemLogDetailsService.save(details);
    }
}