package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseEnum;
import com.hishop.wine.biz.StoreBarBiz;
import com.hishop.wine.model.po.storebar.StoreBarPO;
import com.hishop.wine.model.po.storebar.StoreBarQueryPO;
import com.hishop.wine.model.vo.storeBar.StoreBarVO;
import com.hishop.wine.repository.dao.StoreBarMapper;
import com.hishop.wine.repository.entity.StoreBar;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@AllArgsConstructor
public class StoreBarBizImpl implements StoreBarBiz {

    private final StoreBarMapper mapper;
    @Override
    public boolean create(StoreBarPO storeBarPO,boolean xcxSelf) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getBarJson()), "导航json内容不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getBgColor()), "背景色不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getFontColor()), "字体色不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getSelectColor()), "选中色不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getAppId()) && xcxSelf, "小程序独立设置导航时，需提供小程序appId");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getModuleCode()) && !xcxSelf, "模块设置时，需提供模块编码");


        StoreBar model = BeanUtil.copyProperties(storeBarPO, StoreBar.class);
        mapper.insert(model);
        return true;
    }

    @Override
    public boolean update(StoreBarPO storeBarPO) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getBarJson()), "导航json内容不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getBgColor()), "背景色不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getFontColor()), "字体色不能为空");
        Preconditions.checkArgument(!Strings.isNullOrEmpty(storeBarPO.getSelectColor()), "选中色不能为空");
        Preconditions.checkArgument(storeBarPO.getId() > 0, "请传入正常的id");


        LambdaUpdateWrapper<StoreBar> updateWrapper=new LambdaUpdateWrapper<>();
        updateWrapper.eq(StoreBar::getId,storeBarPO.getId())
                .set(StoreBar::getBarJson,storeBarPO.getBarJson())
                .set(StoreBar::getBgColor,storeBarPO.getBgColor())
                .set(StoreBar::getFontColor,storeBarPO.getFontColor())
                .set(StoreBar::getSelectColor,storeBarPO.getSelectColor())
                .set(StoreBar::getUpdateBy, DateTime.now());

        mapper.update(null,updateWrapper);
        return true;
    }

    /**
     * 查询底部导航详情
     *
     * @param storeBarQueryPO 底部导航查询参数
     * @return 底部导航详情
     */
    @Override
    public StoreBarVO detail(StoreBarQueryPO storeBarQueryPO) {
        StoreBar storeBar = mapper.selectOne(new LambdaQueryWrapper<StoreBar>()
                .eq(StoreBar::getModuleCode, storeBarQueryPO.getModuleCode()).eq(StoreBar::getAppId, storeBarQueryPO.getAppId()));
        if(storeBar == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }

        return BeanUtil.copyProperties(storeBar, StoreBarVO.class);
    }
}
