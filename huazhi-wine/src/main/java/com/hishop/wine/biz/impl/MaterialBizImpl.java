package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.OrderItem;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.base.Preconditions;
import com.google.common.base.Strings;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.RedisUtil;
import com.hishop.nfs.api.NFS;
import com.hishop.wine.biz.MaterialCategoryBiz;
import com.hishop.wine.repository.dto.MaterialCategoryDTO;
import com.hishop.wine.repository.dto.MaterialDTO;
import com.hishop.wine.model.po.material.MaterialQueryPO;
import com.hishop.wine.common.enums.MaterialStatus;
import com.hishop.wine.common.enums.MaterialType;
import com.hishop.wine.repository.dao.MaterialMapper;
import com.hishop.wine.repository.entity.Material;
import com.hishop.wine.biz.MaterialBiz;
import com.hishop.wine.repository.entity.Micropage;
import com.hishop.wine.repository.service.MaterialCategoryService;
import com.hishop.common.exception.BusinessException;
import lombok.AllArgsConstructor;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 资源库表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class MaterialBizImpl implements MaterialBiz {


    private final MaterialCategoryService materialCategoryService;

    private final MaterialCategoryBiz materialCategoryBiz;

    private final NFS nfs;

    private final MaterialMapper mapper;

    private final static String MATERIAL_DIR_PATH_FORMAT = "resources/%s/%s";

    /**
     * 上传有效时间(分钟)
     */
    private final static Integer DEFAULT_UPLOAD_EXPIRE_MINS = 10;

    private final static Long[] MATERIAL_SIZE_LIME = new Long[]{
            //图片 10M
            10 * 1024 * 1024L,
            //视频 1G
            1 * 1024 * 1024 * 1024L,
            //音频 6M
            6 * 1024 * 1024L
    };

    /**
     * 格式限制列表
     */
    private final static String[][] MATERIALS_EXT_LIMIT = new String[][]{
            // 图片允许的格式
            {".jpg", ".gif", ".png", ".bmp", ".jpeg"},
            //视频允许的格式
            {".mp4", ".mov", ".m4v", ".flv", ".x-flv", ".mkv", ".wmv", ".avi", ".rmvb", ".3gp"},
            //音频允许的格式
            {".amr", ".mp3", ".mpeg"}
    };


    @Override
    @Transactional
    public MaterialDTO requestMaterialUpload(@NonNull Long materialCategoryId, @NonNull String title, String filePath, String bannerPath, Long fileSize, Integer duration, boolean izAudio) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(title), "标题不能为空");
        Preconditions.checkArgument(title.indexOf(".") > 0, "标题必须包含扩展名");

        if (materialCategoryId != 0) {
            //检查分组是否存在
            MaterialCategoryDTO materialCategory = materialCategoryBiz.getById(materialCategoryId);
            if (materialCategory == null) {
                throw new BusinessException(String.format("资源库分组[%s]不存在", materialCategoryId));
            }
            //检查类型是否在允许的范围内
            checkMaterialFortmat(title, materialCategory.getType());
        }
        String jobId = "";
        String outFilePath = "";
        String path = "output/" + LocalDate.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")) + "/";
        String uuid =UUID.randomUUID().toString().replace("-", "");
        //提交转码任务
        /*if (izAudio) {
            outFilePath = path + uuid + ".mp3";
            jobId = nfs.submitTransCodeJobByAudio(filePath, outFilePath);
        } else {
            outFilePath = path + uuid + ".mp4";
            jobId = nfs.submitTransCodeJob(filePath, outFilePath);
        }*/

        if(StringUtils.isEmpty(bannerPath)){
            bannerPath=path+uuid+"_0.jpg";
        }

        //创建资源文件记录
        //并记录jobId
        Material material = new Material();
        material.setPath(filePath);
        material.setStatus(MaterialStatus.TRANSCODING);//设置状态为转码中
//        material.setBannerPath(path + "0.jpg");
        material.setTitle(title);
        material.setType(2);
        material.setMaterialCategoryId(materialCategoryId);
        material.setJobId(jobId);
        material.setBannerPath(bannerPath);
        material.setSize(fileSize);
        material.setDuration(duration);
//        material.setStatus(MaterialStatus.UPLOADING);
        mapper.insert(material);
        return BeanUtil.copyProperties(material, MaterialDTO.class);
    }


    @Override
    public MaterialDTO requestImageCreate(Long materialCategoryId, String title, String imagePath, Long fileSize) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(title), "标题不能为空");
        Preconditions.checkArgument(title.indexOf(".") > 0, "标题必须包含扩展名");

        //检查分组是否存在
        MaterialCategoryDTO materialCategory = materialCategoryBiz.getById(materialCategoryId);
        if (materialCategory == null) {
            throw new BusinessException(String.format("资源库分组[%s]不存在", materialCategoryId));
        }

        //检查类型是否在允许的范围内
        checkMaterialFortmat(title, materialCategory.getType());

        //创建资源文件记录
        Material material = new Material();
        material.setPath(imagePath);
        material.setTitle(title);
        material.setType(materialCategory.getType());
        material.setMaterialCategoryId(materialCategoryId);
        material.setStatus(MaterialStatus.NORMAL);
        material.setSize(fileSize);
        mapper.insert(material);
        return BeanUtil.copyProperties(material, MaterialDTO.class);
    }

    @Override
    public void uploadSuccess(String filePath, Long size, Integer duration) {
        Material material = getMaterialFromTemporarySave(filePath);
        if (material != null) {
            //更新状态和文件大小
            material.setSize(size);
            if (MaterialType.VIDEO.getValue().equals(material.getType()) || MaterialType.AUDIO.getValue().equals(material.getType())) {
                material.setDuration(duration);
            }
            //视频类型的资源需要进行转码
            if (MaterialType.VIDEO.getValue().equals(material.getType())) {
                //设置状态为转码中
                material.setStatus(MaterialStatus.TRANSCODING);
                String jobId = nfs.submitTransCodeJob(filePath, filePath);
                material.setJobId(jobId);
            } else {
                material.setStatus(MaterialStatus.NORMAL);
            }
            mapper.insert(material);
            updateCountByMaterialCategoryId(material.getMaterialCategoryId());
            removeMaterialTemporarySave(filePath);
        } else {
            throw new BusinessException(String.format("资源文件[%s]不存在", filePath));
        }
    }

    @Override
    public PageResult<MaterialDTO> getMaterials(@NonNull MaterialQueryPO materialQueryPO) {
        Preconditions.checkArgument(materialQueryPO.getMaterialCategoryId() != null && materialQueryPO.getMaterialCategoryId() >= -3, "分组id必须为大于等于-3的正整数");

        List<Long> childIds = materialCategoryService.listChildCategoryIds(materialQueryPO.getMaterialCategoryId());
        if (CollectionUtils.isEmpty(childIds)) {
            return PageResultHelper.defaultEmpty(materialQueryPO);
        }

        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Material::getMaterialCategoryId, childIds);
        queryWrapper.in(Material::getStatus, MaterialStatus.NORMAL, MaterialStatus.TRANSCODING, MaterialStatus.ERROR);
        if (!Strings.isNullOrEmpty(materialQueryPO.getTitleKeywords())) {
            queryWrapper.like(Material::getTitle, materialQueryPO.getTitleKeywords());
        }
        Page<Material> page = new Page<>();
        page.setSize(materialQueryPO.getPageSize());
        page.setCurrent(materialQueryPO.getPageNo());
        page.addOrder(new OrderItem("id", false));
        Page<Material> materialPage = mapper.selectPage(page, queryWrapper);
        List<MaterialDTO> materialDTOS = new ArrayList<>();
        materialPage.getRecords().forEach(material -> {
            MaterialDTO materialDTO = new MaterialDTO();
            BeanUtils.copyProperties(material, materialDTO);
            materialDTOS.add(materialDTO);
        });
        return PageResultHelper.transfer(materialPage, MaterialDTO.class);
    }

    @Override
    public void updateMaterial(@NonNull Long id, @NonNull Long materialCategoryId, @NonNull String title) {
        Material material = mapper.selectById(id);
        if (material == null) {
            throw new BusinessException(String.format("资源文件[%s]不存在", id));
        }

        boolean materialCategoryChanged = !material.getMaterialCategoryId().equals(materialCategoryId);
        Long oldMaterialCatergoryId = null;
        if (materialCategoryChanged) {
            oldMaterialCatergoryId = material.getMaterialCategoryId();
            MaterialCategoryDTO materialCategory = materialCategoryBiz.getById(materialCategoryId);
            if (materialCategory == null) {
                throw new BusinessException(String.format("资源库分组[%s]不存在", materialCategoryId));
            }
            if (!materialCategory.getType().equals(material.getType())) {
                //新分组类型与原类型不一致时，抛出异常
                throw new BusinessException(String.format("资源库分组[%s]类型与资源类型[%s]不匹配", materialCategoryId, material.getType()));
            }
            material.setMaterialCategoryId(materialCategoryId);
        }

        Preconditions.checkArgument(!Strings.isNullOrEmpty(title), "标题不能为空");
        boolean titleChanged = !material.getTitle().equals(title);
        if (titleChanged) {
            material.setTitle(title);
        }

        mapper.updateById(material);
        //更新分组资源数量
        if (materialCategoryChanged) {
            updateCountByMaterialCategoryId(oldMaterialCatergoryId);
            updateCountByMaterialCategoryId(materialCategoryId);
        }
    }

    @Override
    public void changeMaterialCategory(@NonNull Long newMaterialCategoryId, @NonNull Collection<Long> ids) {
        Preconditions.checkArgument(ids.size() > 0, "id集合不能为空集合");

        MaterialCategoryDTO newMaterialCategory = materialCategoryBiz.getById(newMaterialCategoryId);
        if (newMaterialCategory == null) {
            throw new BusinessException(String.format("资源库分组[%s]不存在", newMaterialCategoryId));
        }

        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Material::getId, ids);
        List<Integer> materialTypes = mapper.getMaterialTypesByIds(ids);

        if (materialTypes.stream().distinct().count() != 1) {
            throw new BusinessException("资源文件类型不一致");
        }
        Integer oldType = materialTypes.get(0);
        if (!oldType.equals(newMaterialCategory.getType())) {
            throw new BusinessException(String.format("资源库分组类型不匹配。期望类型：%s，实际类型:%s", oldType, newMaterialCategory.getType()));
        }

        mapper.updateMaterialCategoryIdByIds(newMaterialCategoryId, ids);
        updateCountByMaterialCategoryId(newMaterialCategoryId);
    }

    @Override
    public void deleteMaterialsByIds(List<Long> ids, boolean izRemoveFile) {
        List<Material> materials = getMaterialsByIds(ids);
        mapper.deleteBatchIds(materials);
        materials.forEach(material -> deleteNFSFileByMaterial(material));
        List<Long> materialCategoryIds = materials.stream().map(p -> p.getMaterialCategoryId()).distinct().collect(Collectors.toList());
        materialCategoryIds.forEach(materialCategoryId -> {
            updateCountByMaterialCategoryId(materialCategoryId);
        });

        /*if (izRemoveFile) {
            for (Material item : materials) {
                nfs.delete(item.getPath());
            }

        }*/
    }

    @Override
    public void deleteMaterialsByMaterialCategoryId(Long materialCategoryId) {
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Material::getMaterialCategoryId, materialCategoryId);
        List<Material> materials = mapper.selectList(queryWrapper);
        mapper.delete(queryWrapper);
        materials.forEach(material -> deleteNFSFileByMaterial(material));
    }

    @Override
    public void updateCountByMaterialCategoryId(Long materialCategoryId) {
        Integer total = mapper.getCountByMaterialCategoryId(materialCategoryId);
        materialCategoryBiz.updateMaterialCount(materialCategoryId, total);
    }

    @Override
    public void setTranscodeStatus(String jobId, Boolean success) {
        Material material = getMaterialByJobId(jobId);
        if (material == null) {
            throw new BusinessException(String.format("资源文件转码任务[%s]不存在", jobId));
        }
        UpdateWrapper<Material> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("job_id", jobId);


        if (success) {
            updateWrapper.set("status", MaterialStatus.NORMAL);
        } else {
            updateWrapper.set("status", MaterialStatus.ERROR);
        }
        mapper.update(null, updateWrapper);
    }

    private String getTemporaryMaterialCacheKey(String path) {
        return String.format("DATA:METERIAL:%s", path);
    }

    /**
     * 临时保存资源
     *
     * @param material 待设置的资源
     */
    private void temporarySaveMaterial(Material material, Integer timeOutSeconds) {
        String key = getTemporaryMaterialCacheKey(material.getPath());
        String json = JSON.toJSONString(material);
        RedisUtil.set(key, json, timeOutSeconds);
    }


    /**
     * 移除临时资源
     *
     * @param path 临时资源的文件路径
     */
    private void removeMaterialTemporarySave(String path) {
        String key = getTemporaryMaterialCacheKey(path);
        RedisUtil.del(key);
    }

    private Material getMaterialFromTemporarySave(String path) {
        String key = getTemporaryMaterialCacheKey(path);
        String json = RedisUtil.get(key);
        if (Strings.isNullOrEmpty(json)) {
            return null;
        } else {
            return JSON.parseObject(json, Material.class);
        }
    }

    private Material getMaterialByJobId(String jobId) {
        Preconditions.checkArgument(!Strings.isNullOrEmpty(jobId), "转码任务id不能为空");
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Material::getJobId, jobId);
        Material material = mapper.selectOne(queryWrapper);
        return material;
    }

    private void deleteNFSFileByMaterial(Material material) {
        try {
            //删除文件
            /*nfs.delete(material.getPath());
            if (material.getType().equals(MaterialType.VIDEO)) {
                //删除banner图
                nfs.delete(material.getBannerPath());
            }*/
        } catch (Exception ex) {
            log.warn("删除资源文件失败", ex);
        }
    }

    private List<Material> getMaterialsByIds(Collection<Long> ids) {
        LambdaQueryWrapper<Material> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Material::getId, ids);
        return mapper.selectList(queryWrapper);
    }

    private String getNewPathByType(Integer type, String name) {
        String ext = name.substring(name.lastIndexOf('.'));
        String fileName = UUID.randomUUID().toString().replaceAll("-", "").concat(ext);
        String path = String.format(MATERIAL_DIR_PATH_FORMAT, MaterialType.getByValue(type).name().toLowerCase(), fileName);
        return path;
    }

    /**
     * 根据资源类型获取大小限制
     *
     * @param type 资源类型
     * @return 返回对应的大小限制
     */
    private Long getSizeLimitByResourceType(Integer type) {
        return MATERIAL_SIZE_LIME[type - 1];
    }

    /**
     * 根据资源文件名称检查资源文件格式是否在允许的范围内
     *
     * @param title 资源文件名称
     * @param type  资源文件的类型
     */
    private void checkMaterialFortmat(String title, Integer type) {
        String ext = title.substring(title.lastIndexOf('.')).toLowerCase();
        String[] allowExts = MATERIALS_EXT_LIMIT[type - 1];
        if (Arrays.stream(allowExts).noneMatch(p -> p.equals(ext))) {
            throw new BusinessException(String.format("文件格式不正确。仅支持以下几个类型：%s", StrUtil.join("、", allowExts)));
        }
    }

}