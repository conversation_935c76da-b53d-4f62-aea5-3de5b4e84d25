package com.hishop.wine.biz.excel.listener;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.excel.read.DefaultReadListener;
import com.hishop.wine.model.po.terminate.TerminateImportPo;

/**
 * 门店导入监听器
 * <AUTHOR>
 * @date 2023/7/10
 */
public class TerminateReadListener extends DefaultReadListener<TerminateImportPo> {

    @Override
    protected String checkData(TerminateImportPo data) {
        String errMsg = "";
        if (StrUtil.isBlank(data.getCode())) {
            errMsg += "门店编码不能为空;";
        }
        if (StrUtil.isBlank(data.getName())) {
            errMsg += "门店名称不能为空;";
        }
        if (StrUtil.isBlank(data.getDutyName())) {
            errMsg += "负责人姓名不能为空;";
        }
        if (StrUtil.isBlank(data.getPhone())) {
            errMsg += "负责人手机号不能为空;";
        }
        if (!PhoneUtil.isMobile(data.getPhone())) {
            errMsg += "负责人手机号不正确;";
        }

        return errMsg;
    }

}
