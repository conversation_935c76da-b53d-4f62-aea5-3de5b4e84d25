package com.hishop.wine.biz.excel.handler;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.excel.context.BizContent;
import com.hishop.wine.biz.excel.dealer.AbstractImportHandler;
import com.hishop.wine.biz.excel.wrapper.TerminalErrWrapper;
import com.hishop.wine.common.enums.AuditStatus;
import com.hishop.wine.common.enums.FileImportType;
import com.hishop.wine.common.enums.TerminateSource;
import com.hishop.wine.common.enums.TerminateSquare;
import com.hishop.wine.model.po.terminate.TerminateImportPo;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import com.hishop.wine.utils.AddressResolutionUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
@Service
public class TerminateImportHandler extends AbstractImportHandler<TerminateImportPo> {

    @Resource
    private DealerService dealerService;

    @Resource
    private DistrictService districtService;

    @Resource
    private UserService userService;

    @Resource
    private TerminateService terminateService;

    @Resource
    private TerminateTypeService terminateTypeService;


    @Override
    public BizType bizType() {
        return FileImportType.TERMINATE_IMPORT;
    }

    @Override
    public void checkExistsAndSetValue(ReadResult<TerminateImportPo> importResult, BizContent bizContent, FileImportRecord record) {
        List<TerminateImportPo> importList = importResult.getDataList();

        // 获取所有的门店编码
        List<String> importCodeList = importList.stream()
                .map(TerminateImportPo::getCode)
                .collect(Collectors.toList());

        List<Terminate> terminateCodeList = terminateService.list(new LambdaQueryWrapper<Terminate>().in(Terminate::getCode, importCodeList));
        Map<String, String> terminateCodeMap = CollectionUtils.isEmpty(terminateCodeList) ? MapUtil.newHashMap(false) : terminateCodeList.stream()
                .collect(Collectors.toMap(Terminate::getCode, Terminate::getCode));


        // 获取所有的门店名称
        List<String> importNameList = importList.stream()
                .map(TerminateImportPo::getName)
                .collect(Collectors.toList());

        List<Terminate> terminateNameList = terminateService.list(new LambdaQueryWrapper<Terminate>().in(Terminate::getName, importNameList));

        Map<String, String> terminateNameMap = CollectionUtils.isEmpty(terminateNameList) ? MapUtil.newHashMap(false) : terminateNameList.stream()
                .collect(Collectors.toMap(Terminate::getName, Terminate::getCode));


        // 获取所有的门店负责人手机号码
        List<String> phoneList = importList.stream()
                .map(TerminateImportPo::getPhone)
                .collect(Collectors.toList());

        List<Terminate> terminatePhoneList = terminateService.list(new LambdaQueryWrapper<Terminate>().in(Terminate::getPhone, phoneList));

        Map<String, String> terminatePhoneMap = CollectionUtils.isEmpty(terminatePhoneList) ? MapUtil.newHashMap(false) : terminatePhoneList.stream()
                .collect(Collectors.toMap(Terminate::getPhone, Terminate::getCode));


        // 获取所有的业务员手机号码
        List<String> userPhoneList = importList.stream()
                .map(TerminateImportPo::getBusinessPhone)
                .collect(Collectors.toList());

        Map<String, Long> businessUserMap = MapUtil.newHashMap(false);
        if (CollectionUtils.isNotEmpty(userPhoneList)) {
            List<User> userList = userService.list(new LambdaQueryWrapper<User>().in(User::getMobile, userPhoneList));
            businessUserMap = CollectionUtils.isEmpty(userList) ? MapUtil.newHashMap(false) : userList.stream()
                    .collect(Collectors.toMap(User::getMobile, User::getId));
        }

        // 获取所有的经销商手机号码
        List<String> dealerPhoneList = importList.stream()
                .map(TerminateImportPo::getDealerPhone)
                .collect(Collectors.toList());

        Map<String, Long> dealerMap = MapUtil.newHashMap(false);
        if (CollectionUtils.isNotEmpty(dealerPhoneList)) {
            List<Dealer> dealerList = dealerService.list(new LambdaQueryWrapper<Dealer>().in(Dealer::getPhone, dealerPhoneList));
            dealerMap = CollectionUtils.isEmpty(dealerList) ? MapUtil.newHashMap(false) : dealerList.stream()
                    .collect(Collectors.toMap(Dealer::getPhone, Dealer::getId));
        }

        List<TerminateType> terminateTypes = terminateTypeService.list(new LambdaQueryWrapper<TerminateType>().eq(TerminateType::getIzDelete, Boolean.FALSE));

        List<District> provinceList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 1));

        List<District> cityList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 2));

        List<District> districtList = districtService.list(new LambdaQueryWrapper<District>().eq(District::getLevel, 3));


        Map<String, District> provinceMap = CollectionUtils.isEmpty(provinceList) ? MapUtil.newHashMap(false) : provinceList.stream()
                .collect(Collectors.toMap(District::getName, item -> item));

        Map<String, String> uniqueMap = MapUtil.newHashMap(false);

        Map<String, Long> finalBusinessUserMap = businessUserMap;

        Map<String, Long> finalDealerMap = dealerMap;

        for (TerminateImportPo item : importList) {
            String errMsg = item.getErrMsg();
            if (errMsg == null) {
                errMsg = "";
            } else {
                continue;
            }

            if (terminateCodeMap.containsKey(item.getCode())) {
                errMsg += "门店编号已存在;";
            }

            if (terminateNameMap.containsKey(item.getName())) {
                errMsg += "门店名称已存在;";
            }

            if (terminatePhoneMap.containsKey(item.getPhone())) {
                errMsg += "门店手机号已存在;";
            }

            if (uniqueMap.containsKey(item.getCode())) {
                errMsg += "门店编号重复;";
            } else {
                uniqueMap.put(item.getCode(), item.getCode());
            }

            if (uniqueMap.containsKey(item.getName())) {
                errMsg += "门店名称重复;";
            } else {
                uniqueMap.put(item.getName(), item.getName());
            }

            if (uniqueMap.containsKey(item.getPhone())) {
                errMsg += "门店手机号已存在;";
            } else {
                uniqueMap.put(item.getPhone(), item.getPhone());
            }

            if (StringUtils.isNotBlank(item.getBusinessPhone())) {
                if (finalBusinessUserMap.containsKey(item.getBusinessPhone())) {
                    item.setBusinessUserId(finalBusinessUserMap.get(item.getBusinessPhone()));
                } else {
                    errMsg += "所属业务员手机号不存在;";
                }
            }

            if (StringUtils.isNotBlank(item.getDealerPhone())) {
                if (finalDealerMap.containsKey(item.getDealerPhone())) {
                    item.setDealerId(finalDealerMap.get(item.getDealerPhone()));
                } else {
                    errMsg += "所属经销商手机号不存在;";
                }
            }

            if (StringUtils.isNotBlank(item.getType())) {
                TerminateType terminateType = terminateTypes.stream().filter(type -> type.getName().equals(item.getType())).findFirst().orElse(null);
                if (terminateType != null) {
                    item.setTerminateTypeId(terminateType.getId());
                } else {
                    errMsg += "门店类型不存在;";
                }
            }

            if (StringUtils.isNotBlank(item.getSquareDesc())){
                TerminateSquare terminateSquare = TerminateSquare.getTerminateSquareEnumByDesc(item.getSquareDesc());
                if (terminateSquare != null) {
                    item.setSquare(terminateSquare.getType());
                } else {
                    errMsg += "门店面积不匹配;";
                }
            }


            if (StringUtils.isNotBlank(item.getRegion())) {
                //获取省市区地址
                Map<String, String> addressResolution = AddressResolutionUtil.addressResolution(item.getRegion());

                if (addressResolution.isEmpty()) {
                    errMsg += "门店地址不匹配;";
                } else {
                    if (StringUtils.isBlank(addressResolution.get(AddressResolutionUtil.PROVINCE_NAME))) {
                        if (AddressResolutionUtil.DIRECTLY_ADMINISTERED_CITIES.contains(addressResolution.get(AddressResolutionUtil.CITY_NAME))) {
                            addressResolution.put(AddressResolutionUtil.PROVINCE_NAME, addressResolution.get(AddressResolutionUtil.CITY_NAME).replace("市", ""));
                        } else {
                            errMsg += "门店地址不匹配;";
                        }
                    } else {
                        //获取省市区地址
                        item.setProvinceName(addressResolution.get(AddressResolutionUtil.PROVINCE_NAME));
                        if (provinceMap.get(item.getProvinceName()) != null) {
                            item.setProvinceId(provinceMap.get(item.getProvinceName()).getId());
                            //获取市
                            cityList.stream().filter(city -> city.getParentId().equals(item.getProvinceId()) && city.getName().equals(addressResolution.get(AddressResolutionUtil.CITY_NAME))).findFirst().ifPresent(city -> {
                                item.setCityId(city.getId());
                                item.setCityName(city.getName());
                            });
                            //获取区县
                            districtList.stream().filter(district -> district.getParentId().equals(item.getCityId()) && district.getName().equals(addressResolution.get(AddressResolutionUtil.DISTRICT_NAME))).findFirst().ifPresent(district -> {
                                item.setDistrictId(district.getId());
                                item.setDistrictName(district.getName());
                            });
                            //设置地址
                            item.setAddress(addressResolution.get(AddressResolutionUtil.ADDRESS_NAME));
                        }
                    }
                }
            }
            item.setErrMsg(errMsg);
        }
    }

    @Override
    public void saveImportData(List<TerminateImportPo> successList, FileImportRecord record) {
        List<Terminate> dbList = BeanUtil.copyToList(successList, Terminate.class);
        dbList.forEach(item -> {
            item.setAuditStatus(AuditStatus.PASS.getType());
            item.setSource(TerminateSource.IMPORT.getType());
        });
        terminateService.saveBatch(dbList);
    }

    @Override
    protected DataWrapper<TerminateImportPo> wrapData(List<TerminateImportPo> errList) {
        return new TerminalErrWrapper(errList);
    }
}
