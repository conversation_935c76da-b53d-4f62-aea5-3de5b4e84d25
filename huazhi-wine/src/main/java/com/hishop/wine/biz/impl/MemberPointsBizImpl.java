package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.MysqlPlusUtil;
import com.hishop.mq.api.MQ;
import com.hishop.wine.assist.lock.LockAssist;
import com.hishop.wine.assist.points.MemberPointsChangeAssist;
import com.hishop.wine.assist.points.PointsCacheAssist;
import com.hishop.wine.biz.MemberPointsBiz;
import com.hishop.wine.biz.SmsBiz;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.enums.SmsEnum;
import com.hishop.wine.enums.UserEnum;
import com.hishop.wine.enums.points.MemberPointsEnum;
import com.hishop.wine.enums.points.PointsBizType;
import com.hishop.wine.enums.points.PointsMallBizType;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.points.MemberPointsQueryPO;
import com.hishop.wine.model.po.sms.SendSmsPO;
import com.hishop.wine.model.po.user.UserPO;
import com.hishop.wine.model.vo.points.MemberPointsDetailsVO;
import com.hishop.wine.model.vo.points.MemberPointsSummaryVO;
import com.hishop.wine.model.vo.points.MemberPointsVO;
import com.hishop.wine.repository.dto.points.MemberPointsDTO;
import com.hishop.wine.repository.dto.points.MemberPointsDetailDTO;
import com.hishop.wine.repository.dto.points.MemberPointsSummaryDTO;
import com.hishop.wine.repository.dto.points.PointsModifiedSummaryDTO;
import com.hishop.wine.repository.entity.MemberPoints;
import com.hishop.wine.repository.entity.MemberPointsDetails;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.MemberPointsParam;
import com.hishop.wine.repository.service.MemberPointsDetailService;
import com.hishop.wine.repository.service.MemberPointsService;
import com.hishop.wine.repository.service.UserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;


/**
 * 会员积分表 业务逻辑实现类
 *
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

@Slf4j
@Service
public class MemberPointsBizImpl implements MemberPointsBiz {

    @Resource
    private MemberPointsService memberPointsService;
    @Resource
    private MemberPointsDetailService memberPointsDetailService;
    @Resource
    private MemberPointsChangeAssist memberPointsChangeAssist;
    @Resource
    private UserService userService;
    @Resource
    private PointsCacheAssist pointsCacheAssist;
    @Resource
    private LockAssist lockAssist;
    @Resource
    private MQ mq;
    @Resource
    private SmsBiz smsBiz;

    private final static Map<String, String> MEMBER_POINTS_MAPPING = new HashMap<>();
    private final String DEFAULT_MEMBER_POINTS_SQL = "ORDER BY hu.create_time DESC";
    private final String CLEAR_POINTS_NOTICE_TEMPLATE = "尊敬的会员，您有%s积分在%s即将过期，逾期未兑换的积分将自动清零，请及时兑换";

    static {
        MEMBER_POINTS_MAPPING.put("registerTime", "hu.create_time");
        MEMBER_POINTS_MAPPING.put("totalPoints", "hmp.total_points");
        MEMBER_POINTS_MAPPING.put("availablePoints", "hmp.available_points");
        MEMBER_POINTS_MAPPING.put("consumedPoints", "hmp.consumed_points");
        MEMBER_POINTS_MAPPING.put("expiredPoints", "hmp.expired_points");
    }

    @Override
    public PageResult<MemberPointsVO> pageList(MemberPointsQueryPO pagePo) {
        MemberPointsParam param = BeanUtil.copyProperties(pagePo, MemberPointsParam.class);

        // 处理排序sql
        String orderSql = MysqlPlusUtil.getOrderSql(pagePo.getSortList(), MEMBER_POINTS_MAPPING);
        if (StrUtil.isEmpty(orderSql)) {
            orderSql = DEFAULT_MEMBER_POINTS_SQL;
        }
        param.setSortSql(orderSql);

        Page<MemberPointsDTO> dbPage = memberPointsService.qryPage(pagePo.buildPage(), param);
        pointsCacheAssist.initAllUser();
        return PageResultHelper.transfer(dbPage, MemberPointsVO.class, vo -> {
            // 用户不关心冻结积分，所以可用积分和消耗积分处理一下
            int frozenPoints = ObjectUtil.defaultIfNull(vo.getFrozenPoints(), 0);
            int availablePoints = ObjectUtil.defaultIfNull(vo.getAvailablePoints(), 0) - frozenPoints;
            int consumedPoints = ObjectUtil.defaultIfNull(vo.getConsumedPoints(), 0) + frozenPoints;
            vo.setAvailablePoints(availablePoints);
            vo.setTotalPoints(ObjectUtil.defaultIfNull(vo.getTotalPoints(), 0));
            vo.setExpiredPoints(ObjectUtil.defaultIfNull(vo.getExpiredPoints(), 0));
            vo.setConsumedPoints(consumedPoints);
            vo.setIdentityTypeDesc(UserEnum.IdentityType.getDesc(vo.getIdentityType()));
        });
    }

    @Override
    public MemberPointsSummaryVO summary(MemberPointsQueryPO pagePo) {
        MemberPointsParam param = BeanUtil.copyProperties(pagePo, MemberPointsParam.class);
        MemberPointsSummaryDTO summaryDTO = memberPointsService.summary(param);
        pointsCacheAssist.initAllUser();
        return BeanUtil.copyProperties(summaryDTO, MemberPointsSummaryVO.class);
    }

    @Override
    public PageResult<MemberPointsDetailsVO> pageDetail(MemberPointsQueryPO pagePo) {
        MemberPointsParam param = BeanUtil.copyProperties(pagePo, MemberPointsParam.class);
        Page<MemberPointsDetailDTO> dbPage = memberPointsDetailService.pageDetail(pagePo.buildPage(), param);
        if (CollUtil.isEmpty(dbPage.getRecords())) {
            return PageResultHelper.defaultEmpty(pagePo);
        }
        pointsCacheAssist.initAllUser();
        Set<Long> userIdSet = dbPage.getRecords().stream().map(MemberPointsDetailDTO::getUserId).collect(Collectors.toSet());
        List<User> userList = userService.listUserByUserIds(new ArrayList<>(userIdSet));
        Map<Long, User> userMap = userList.stream().collect(Collectors.toMap(User::getId, user -> user));
        return PageResultHelper.transfer(dbPage, MemberPointsDetailsVO.class,
                vo -> {
                    vo.setModifiedTypeDesc(MemberPointsEnum.ModifiedType.getDesc(vo.getModifiedType()));
                    vo.setFromSystemDesc(ModuleEnums.getDesc(vo.getModuleCode()));
                    vo.setIdentityTypeDesc(UserEnum.IdentityType.getDesc(vo.getIdentityType()));
                    User user = userMap.get(vo.getUserId());
                    vo.setNickName(user.getNickName());
                    vo.setIcon(user.getIcon());
                    vo.setBizTypeDesc(PointsBizType.getBizType(vo.getModuleCode()).getDesc(vo.getBizType()));
                });
    }

    @Override
    public void change(ChangePointsPO pointsPo) {
        memberPointsChangeAssist.changePoints(pointsPo);
        pointsCacheAssist.cacheUserPoints(pointsPo.getUserId(), pointsPo.getIdentityType());
    }

    @Override
    public MemberPointsVO getUserPoints(Long userId, Integer identityType) {
        MemberPoints memberPoints = memberPointsService.getUserPoints(userId, identityType);
        User user = userService.getById(userId);
        if (memberPoints == null) {
            memberPoints = new MemberPoints();
            memberPoints.setUserId(userId);
            memberPoints.setIdentityType(identityType);
            memberPoints.setTotalPoints(0);
            memberPoints.setAvailablePoints(0);
            memberPoints.setConsumedPoints(0);
            memberPoints.setExpiredPoints(0);
            memberPoints.setFrozenPoints(0);
        }
        MemberPointsVO vo = BeanUtil.copyProperties(memberPoints, MemberPointsVO.class);
        vo.setIdentityTypeDesc(IdentityTypeEnums.getNameByType(identityType));
        vo.setRegisterTime(user.getCreateTime());
        vo.setNickName(user.getUsername());
        vo.setUserPhone(user.getMobile());
        pointsCacheAssist.cacheUserPoints(userId, identityType);
        return vo;
    }

    /**
     * 根据业务参数查询积分明细
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 积分明细
     */
    @Override
    public MemberPointsDetailsVO getByBiz(Integer bizType, String bizCode) {
        MemberPointsDetails detail = memberPointsDetailService.getOne(new LambdaQueryWrapper<MemberPointsDetails>()
                .eq(MemberPointsDetails::getBizType, bizType).eq(MemberPointsDetails::getBizCode, bizCode));
        if (ObjectUtil.isNull(detail)) {
            return null;
        }
        return BeanUtil.copyProperties(detail, MemberPointsDetailsVO.class);
    }

    /**
     * 初始化会员积分
     *
     * @param userId       用户id
     * @param identityType 用户身份类型
     */
    @Override
    public void initMemberPoints(Long userId, Integer identityType) {
        User user = userService.getById(userId);

        // 已经存在则无需初始化
        long count = memberPointsService.count(new LambdaQueryWrapper<MemberPoints>()
                .eq(MemberPoints::getUserId, userId).eq(MemberPoints::getIdentityType, identityType));
        if (count > 0) {
            return;
        }

        MemberPoints memberPoints = new MemberPoints();
        memberPoints.setUserId(userId);
        memberPoints.setIdentityType(identityType);
        memberPoints.setUserPhone(user.getMobile());
        memberPoints.setAvailablePoints(0);
        memberPoints.setExpiredPoints(0);
        memberPoints.setFrozenPoints(0);
        memberPoints.setTotalPoints(0);
        memberPoints.setConsumedPoints(0);
        memberPointsService.save(memberPoints);
    }

    @Override
    public void clearExpirePoints(List<UserPO> userList, Date deadlineTime) {
        // 此接口具有幂等性， 可以不做幂等校验
        if (CollectionUtils.isEmpty(userList)) {
            userList = BeanUtil.copyToList(memberPointsService.listAll(), UserPO.class);
        }
        if (CollUtil.isEmpty(userList)) {
            return;
        }

        List<UserPO> failUserList = new ArrayList<>();
        log.info("【积分清零】清除过期积分，共有 {} 条用户数据", userList.size());
        MemberPointsBiz service = SpringUtil.getBean(MemberPointsBiz.class);
        userList.forEach(user -> {
            // 此处需要加扣减积分的相同的锁 因为积分消费流水指针暂时没有维护, 暂时采用这种方式
            try {
                boolean locked = lockAssist.lockChangePoints(user.getUserId(), 2, 5, TimeUnit.SECONDS);
                if (!locked) {
                    throw new RuntimeException("系统繁忙，请稍后再试");
                }

                // 执行正真的清零逻辑
                service.clearExpirePoints(user, deadlineTime);
                log.error("【积分清零】清理过期积分成功， 当前用户id：{}， 身份类型：{}", user.getUserId(), user.getIdentityType());
            } catch (Exception e) {
                e.printStackTrace();
                failUserList.add(user);
                log.error("【积分清零】清理过期积分失败， 当前用户id：{}， 身份类型：{}", user.getUserId(), user.getIdentityType());
            }
        });
        pointsCacheAssist.initAllUser();

        // 存在执行失败的用户, 打印日志
        if (failUserList.size() > 0) {
            log.error("【积分清零】清理过期部分用户执行失败，失败用户数：{}, 明细： {}", failUserList.size(), failUserList);
            // 可以发送mq 重试
        }
    }

    /**
     * 清除过期积分
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void clearExpirePoints(UserPO user, Date deadlineTime) {
        MemberPoints mp = memberPointsService.getUserPoints(user.getUserId(), user.getIdentityType());
        if (ObjectUtil.isNull(mp)) {
            return;
        }
        String id = IdUtil.fastUUID();

        // 计算过期积分
        Integer clearPoints = calClearPoints(mp, deadlineTime, new Date(), "【积分清零】");
        // 执行清零操作, 如果为负数, 表示上期结余在本期已经全部消耗完成
        if (clearPoints > 0) {
            // Integer expirePoints = memberPointsDetailService.getUserPointsSummary(mp.getUserId(), mp.getIdentityType(), mp.getConsumedLastDetailId(), finalDeadlineTime);
            ChangePointsPO pointsPo = new ChangePointsPO();
            pointsPo.setUserId(mp.getUserId());
            pointsPo.setIdentityType(mp.getIdentityType());
            pointsPo.setModifiedType(MemberPointsEnum.ModifiedType.CLEAR.getCode());
            pointsPo.setModifiedPoints(clearPoints * -1);
            pointsPo.setModuleCode(ModuleEnums.fans_club.name());
            pointsPo.setBizType(PointsMallBizType.CLEAR.getCode());
            pointsPo.setModifiedRemark("积分清零");
            pointsPo.setBizCode(id);
            pointsPo.setTraceNo(id);
            pointsPo.setExpireTime(null);
            pointsPo.setCreateBy(0L);
            memberPointsChangeAssist.changePoints(pointsPo);
        }
    }

    /**
     * 通知清除过期积分
     *
     * @param deadlineTime 过期时间
     */
    @Override
    public void noticeClearPoints(List<UserPO> userList, Date deadlineTime) {
        //  此接口需要做幂等 防止重复通知 暂时没做
        if (CollectionUtils.isEmpty(userList)) {
            userList = BeanUtil.copyToList(memberPointsService.listAll(), UserPO.class);
        }
        if (CollUtil.isEmpty(userList)) {
            return;
        }

        List<UserPO> failUserList = new ArrayList<>();
        log.info("【积分清零通知】，共有 {} 条用户数据", userList.size());
        MemberPointsBiz service = SpringUtil.getBean(MemberPointsBiz.class);
        userList.forEach(user -> {
            try {
                // 执行正真的清零逻辑
                service.noticeClearPoints(user, deadlineTime);
                log.error("【积分清零通知】执行成功， 当前用户id：{}， 身份类型：{}", user.getUserId(), user.getIdentityType());
            } catch (Exception e) {
                e.printStackTrace();
                failUserList.add(user);
                log.error("【积分清零通知】执行失败， 当前用户id：{}， 身份类型：{}", user.getUserId(), user.getIdentityType());
            }
        });

        // 存在执行失败的用户, 打印日志
        if (failUserList.size() > 0) {
            log.error("【积分清零通知】部分用户通知失败，失败用户数：{}, 明细： {}", failUserList.size(), failUserList);
            // 可以发送mq 重试
        }
    }

    /**
     * 积分清零通知
     *
     * @param user         用户信息
     * @param deadlineTime 过期时间
     */
    @Override
    public void noticeClearPoints(UserPO user, Date deadlineTime) {
        MemberPoints mp = memberPointsService.getUserPoints(user.getUserId(), user.getIdentityType());
        if (ObjectUtil.isNull(mp)) {
            return;
        }
        // 计算过期积分
        Integer clearPoints = calClearPoints(mp, deadlineTime, new Date(), "【积分清零通知】");
        if (clearPoints > 0) {
            LocalDate lastDayOfYear = LocalDate.now().with(TemporalAdjusters.lastDayOfYear());
            String content = String.format(CLEAR_POINTS_NOTICE_TEMPLATE, clearPoints, lastDayOfYear.format(DateTimeFormatter.ofPattern("MM月dd日")));
            smsBiz.sendSms(SendSmsPO.of(SmsEnum.Channel.NOTICE.getValue(), content, Arrays.asList(mp.getUserPhone())));
        }
    }


    /**
     * 计算需要清零的积分
     *
     * @return 需要清零的积分
     */
    private Integer calClearPoints(MemberPoints mp, Date deadlineTime, Date now, String logPre) {
        List<PointsModifiedSummaryDTO> modifiedList = memberPointsDetailService.summaryPointsModified(mp.getUserId(), mp.getIdentityType(), deadlineTime, now);
        Map<Integer, Integer> modifiedMap = modifiedList.stream().collect(Collectors.toMap(PointsModifiedSummaryDTO::getModifiedType, item -> item.getPoints()));

        // 计算出本期内的积分变动, 根据积分变动计算出上级结余积分
        Integer addPoints = modifiedMap.getOrDefault(MemberPointsEnum.ModifiedType.INCREASE.getCode(), 0);
        Integer reducePoints = modifiedMap.getOrDefault(MemberPointsEnum.ModifiedType.DECREASE.getCode(), 0)
                + modifiedMap.getOrDefault(MemberPointsEnum.ModifiedType.CLEAR.getCode(), 0);
        // 上期结余积分 = 当前剩余积分 + 减少的积分 - 增加的积分
        Integer surplusPoints = mp.getAvailablePoints() + reducePoints * -1 - addPoints;
        // 需要清零的积分 = 上期结余积分 - 本期消耗的积分
        Integer clearPoints = surplusPoints + reducePoints;
        // 如果需要清零的积分大于0, 则表示存在上期未消耗完的积分, 可以执行清零操作
        log.info(logPre + " 用户 userId: {}, identityType:{} 的积分变动情况: 增加积分 {}, 减少积分 {}, 上期结余积分 {}, 需要清零的积分 {}",
                mp.getUserId(), mp.getIdentityType(), addPoints, reducePoints, surplusPoints, clearPoints);
        return clearPoints;
    }
}