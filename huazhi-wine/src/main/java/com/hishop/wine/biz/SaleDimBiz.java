package com.hishop.wine.biz;

import com.hishop.wine.model.po.SaleDimPo;
import com.hishop.wine.model.vo.sale.SaleDimVo;

import java.util.List;

/**
 * @description: 销售维度业务接口
 * @author: chenzw
 * @date: 2024/7/4 09:16
 */
public interface SaleDimBiz {

    /**
     * 查询销售维度列表
     * @return 销售维度列表
     */
    List<SaleDimVo> querySaleDimList();

    /**
     * 更新销售维度
     * @param saleDimPo 销售维度
     */
    void updateSaleDim(SaleDimPo saleDimPo);
}
