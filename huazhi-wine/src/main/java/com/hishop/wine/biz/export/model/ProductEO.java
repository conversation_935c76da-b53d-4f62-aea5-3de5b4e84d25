package com.hishop.wine.biz.export.model;

import com.alibaba.excel.annotation.ExcelProperty;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * 产品导出eo
 *
 * @author: HuBiao
 * @date: 2023-07-05
 */
@Data
public class ProductEO {

    @ExcelProperty("产品名称")
    private String productName;

    @ExcelProperty("产品图片")
    private String productImg;

    @ExcelProperty("产品编码")
    private String productCode;

    @ExcelProperty("市场价")
    private BigDecimal marketPrice;

    @ExcelProperty("产品类型")
    private String productTypeName;

    @ExcelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
