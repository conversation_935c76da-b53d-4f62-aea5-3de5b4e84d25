package com.hishop.wine.biz;

import com.hishop.common.pojo.page.PageParam;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.terminate.TerminateTypeSavePo;
import com.hishop.wine.model.vo.terminate.TerminateTypeVo;

/**
 * @description: 门店类型业务接口类
 * @author: chenzw
 * @date: 2024/7/6 14:49
 */
public interface TerminateTypeBiz {
    /**
     * 保存门店类型
     * @param terminateTypeSavePo 门店类型保存参数
     */
    void save(TerminateTypeSavePo terminateTypeSavePo);

    /**
     * 删除门店类型
     * @param id 门店类型id
     */
    void deleteById(Long id);

    /**
     * 获取门店类型列表
     * @param pageParam 分页参数
     * @return 门店类型列表
     */
    PageResult<TerminateTypeVo> pageList(PageParam pageParam);
}
