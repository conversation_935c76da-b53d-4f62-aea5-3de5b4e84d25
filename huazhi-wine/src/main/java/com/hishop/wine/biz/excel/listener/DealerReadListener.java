package com.hishop.wine.biz.excel.listener;

import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.excel.read.DefaultReadListener;
import com.hishop.wine.model.po.dealer.DealerImportPo;

/**
 * 经销商导入监听器
 * <AUTHOR>
 * @date 2023/7/10
 */
public class DealerReadListener extends DefaultReadListener<DealerImportPo> {

    @Override
    protected String checkData(DealerImportPo data) {
        String errMsg = "";
        if (StrUtil.isBlank(data.getDealerCode())) {
            errMsg += "经销商编码不能为空;";
        }
        if (StrUtil.isBlank(data.getDealerName())) {
            errMsg += "经销商名称不能为空;";
        }
        if (StrUtil.isBlank(data.getRegion())) {
            errMsg += "经销商详细地址不能为空;";
        }
        if (StrUtil.isBlank(data.getDutyName())) {
            errMsg += "负责人姓名不能为空;";
        }
        if (StrUtil.isBlank(data.getPhone())) {
            errMsg += "手机号不能为空;";
        }
        if (!PhoneUtil.isMobile(data.getPhone())) {
            errMsg += "负责人手机号不正确;";
        }

        return errMsg;
    }

}
