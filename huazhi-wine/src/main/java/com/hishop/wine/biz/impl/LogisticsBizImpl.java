package com.hishop.wine.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.logistics.api.LogisticsClient;
import com.hishop.logistics.api.ShipperCode;
import com.hishop.wine.biz.LogisticsBiz;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.model.po.logistics.LogisticsQueryPO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Collections;

/**
 * 物流查询
 *
 * <AUTHOR>
 * @date : 2023/8/25
 */
@Slf4j
@Service
public class LogisticsBizImpl implements LogisticsBiz {

    @Resource
    private LogisticsClient logisticsClient;

    /**
     * 物流查询
     *
     * @param queryPO 查询参数
     * @return 物流信息
     */
    @Override
    public JSONObject query(LogisticsQueryPO queryPO) {
        String redisKey = String.format(BasicCacheConstants.LOGISTICS_QUERY_CACHE, queryPO.getShipperCode(), queryPO.getLogisticCode());

        return RedisUtil.cache(redisKey, () -> {
            try {
                JSONObject result = logisticsClient.query(queryPO.getShipperCode(), queryPO.getLogisticCode(),queryPO.getPhoneLastNumber());
                Collections.reverse(result.getJSONArray("traces"));
                // 将物流公司编码转换为中文
                ShipperCode shipperCodeEnum = ShipperCode.getShipperCode(result.getString("shipperCode"));
                if (ObjectUtil.isNotNull(shipperCodeEnum)) {
                    result.put("shipperCode", shipperCodeEnum.getName());
                }
                return result;
            } catch (Exception e) {
                log.error("查询物流轨迹失败，LogisticsQueryPO：{}", JSON.toJSONString(queryPO), e);
                throw new BusinessException("查询物流轨迹失败");
            }
        });
    }

    /**
     * 查询余额
     *
     * @return 余额
     */
    @Override
    public Integer getBalance() {
        return logisticsClient.getBalance();
    }
}
