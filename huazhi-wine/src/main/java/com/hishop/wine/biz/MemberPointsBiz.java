package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.points.MemberPointsQueryPO;
import com.hishop.wine.model.po.user.UserPO;
import com.hishop.wine.model.vo.points.MemberPointsDetailsVO;
import com.hishop.wine.model.vo.points.MemberPointsSummaryVO;
import com.hishop.wine.model.vo.points.MemberPointsVO;

import java.util.Date;
import java.util.List;

/**
 * 会员积分 业务逻辑接口
 *
 * @author: LiGuoQiang
 * @date: 2023-06-25
 */

public interface MemberPointsBiz {

    /**
     * 分页获取会员积分列表
     *
     * <AUTHOR>
     * @date 2023/6/25
     */
    PageResult<MemberPointsVO> pageList(MemberPointsQueryPO pagePo);

    /**
     * 获取用户积分汇总
     *
     * <AUTHOR>
     * @date 2023/6/25
     */
    MemberPointsSummaryVO summary(MemberPointsQueryPO pagePo);

    /**
     * 分页查询会员积分明细
     *
     * <AUTHOR>
     * @date 2023/6/25
     */
    PageResult<MemberPointsDetailsVO> pageDetail(MemberPointsQueryPO pagePo);

    /**
     * 发放积分
     *
     * <AUTHOR>
     * @date 2023/6/25
     */
    void change(ChangePointsPO pagePo);

    /**
     * 获取用户积分
     *
     * <AUTHOR>
     * @date 2023/6/26
     */
    MemberPointsVO getUserPoints(Long userId, Integer identityType);

    /**
     * 根据业务参数查询积分明细
     *
     * @param bizType 业务类型
     * @param bizCode 业务编码
     * @return 积分明细
     */
    MemberPointsDetailsVO getByBiz(Integer bizType, String bizCode);

    /**
     * 初始化会员积分
     *
     * @param userId       用户id
     * @param identityType 用户身份类型
     */
    void initMemberPoints(Long userId, Integer identityType);

    /**
     * 清除过期积分
     *
     * @param userList     用户信息
     * @param deadlineTime 过期时间
     */
    void clearExpirePoints(List<UserPO> userList, Date deadlineTime);

    /**
     * 清除过期积分
     *
     * <AUTHOR>
     * @date 2023/8/3
     */
    void clearExpirePoints(UserPO user, Date deadlineTime);

    /**
     * 积分清零通知
     *
     * @param userList     用户信息
     * @param deadlineTime 过期时间
     */
    void noticeClearPoints(List<UserPO> userList, Date deadlineTime);

    /**
     * 积分清零通知
     *
     * @param user         用户信息
     * @param deadlineTime 过期时间
     */
    void noticeClearPoints(UserPO user, Date deadlineTime);
}