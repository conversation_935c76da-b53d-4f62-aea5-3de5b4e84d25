package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.collect.Lists;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseEnum;
import com.hishop.wine.biz.DepartmentBiz;
import com.hishop.wine.constants.DepartmentConstants;
import com.hishop.wine.convert.DepartmentConvert;
import com.hishop.wine.model.po.*;
import com.hishop.wine.model.vo.department.DepartmentVO;
import com.hishop.wine.repository.entity.Department;
import com.hishop.wine.repository.entity.DepartmentEmployee;
import com.hishop.wine.repository.service.DepartmentEmployeeService;
import com.hishop.wine.repository.service.DepartmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**   
 * @Description: 部门表 业务逻辑实现类
 * @Author: chenpeng
 * @since: 2023-04-25 10:50:00
 */

@Slf4j
@Service
public class DepartmentBizImpl implements DepartmentBiz {

    @Resource
    private DepartmentService departmentService;
    @Resource
    private DepartmentEmployeeService departmentEmployeeService;

    @Resource
    private DepartmentConvert departmentConvert;


    @Override
    public void create(DepartmentCreatePO departmentCreatePO) {
        if (null == departmentCreatePO.getParentId()) {
            departmentCreatePO.setParentId(DepartmentConstants.DEFAULT_PARENT_ID);
        }
        Department existingDepartment = departmentService.getByNameAndParent(departmentCreatePO.getParentId(), departmentCreatePO.getDepartmentName());
        if (existingDepartment != null) {
            throw new BusinessException("该部门已存在");
        }
        Department entity = departmentConvert.convertCreatePOToEntity(departmentCreatePO);
        //生成部门编码
        this.generateCode(entity);
        departmentService.save(entity);
    }

    @Override
    public void update(DepartmentUpdatePO departmentUpdatePO) {
        Department entity = departmentService.getById(departmentUpdatePO.getId());
        if(entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND.getMsg());
        }
        Department existingDepartment = departmentService.getByNameAndParent(entity.getParentId(),
                departmentUpdatePO.getDepartmentName());
        if (existingDepartment != null && !existingDepartment.getId().equals(departmentUpdatePO.getId())) {
            throw new BusinessException("该部门已存在");
        }
        entity.setDepartmentName(departmentUpdatePO.getDepartmentName());
        departmentService.updateById(entity);
    }

    @Override
    public void deleteById(Long id) {
        Integer formId = departmentService.getMaxFormIdByParentId(id);
        if (formId != null) {
            throw new BusinessException("该部门下存在子部门，不能删除");
        }
        // 检查该部门下是否有员工
        long count = departmentEmployeeService.countByDepartmentId(id);
        if (count > 0) {
            throw new BusinessException("该部门下存在子账号，不能删除");
        }
        // 如果没有员工，进行逻辑删除
        departmentService.logicalDeleteById(id);
    }

    @Override
    public List<DepartmentVO> tree() {
        // 获取所有部门的信息
        List<Department> departments = departmentService.listAll();
        List<DepartmentVO> departmentVOS = departmentConvert.convertEntityToVOList(departments);
        // 构建树形结构
        List<DepartmentVO> tree = this.buildTree(departmentVOS);
        return tree;
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void setHeader(DepartmentEmployeeHeaderPO headerPO) {
        DepartmentEmployee departmentEmployee = departmentEmployeeService.getByDepartmentIdAndUserId(headerPO.getDepartmentId(), headerPO.getUserId());
        if (departmentEmployee == null) {
            throw new BusinessException("当前部门不存在该用户");
        }
        //如果当前用户已经是负责人，不处理
        if (departmentEmployee.getIzHead()) {
            return;
        }
        DepartmentEmployee currentHeader = departmentEmployeeService.getHeaderByDepartmentId(headerPO.getDepartmentId());
        if (currentHeader != null) {
            // 如果已存在其他负责人，需要将其他负责人设置为false
            currentHeader.setIzHead(false);
            departmentEmployeeService.updateById(currentHeader);
        }
        departmentEmployee.setIzHead(true);
        departmentEmployeeService.setHeader(departmentEmployee);
    }

    @Override
    public DepartmentVO getDepartmentUser(Long userId) {
        LambdaQueryWrapper<DepartmentEmployee> queryWrapper=new LambdaQueryWrapper<>();
        queryWrapper.eq(DepartmentEmployee::getUserId, userId);
        queryWrapper.eq(DepartmentEmployee::getIzDelete,false);
        queryWrapper.last("limit 1");
        DepartmentEmployee departmentEmployee= departmentEmployeeService.getOne(queryWrapper);
        if(null == departmentEmployee) {
            return null;
        }
        Department department= departmentService.getById(departmentEmployee.getDepartmentId());
        return departmentConvert.convertEntityToVO(department);

    }

    @Override
    public Boolean updateDep(DepartmentUpdateIdPO updateIdPO) {
        return departmentEmployeeService.updateDep(updateIdPO);
    }

    @Override
    public List<DepartmentVO> getDepParentUp(Long departmentId) {
        //获取所有的部门信息
        List<Department> departments = departmentService.listAll();
        //递归查询当前部门向上所属层级
        List<DepartmentVO> departmentList=departmentConvert.convertEntityToVOList(departments);
        List<DepartmentVO> departmentVOList = getDepartmentHierarchy(departmentList, departmentId);
        return departmentVOList;
    }


    @Override
    public List<DepartmentVO> getDepParentLow(Long departmentId) {
        List<Department> departments = departmentService.listAll();
        List<Department> allIds = Lists.newArrayList();
        //递归查询当前部门向下所属层级
        displayDepartment(departments,departmentId,allIds);
        return BeanUtil.copyToList(allIds, DepartmentVO.class);
    }

    private static List<DepartmentVO> getDepartmentHierarchy(List<DepartmentVO> departmentList, Long departmentId) {
        List<DepartmentVO> hierarchy = Lists.newArrayList();
        // 递归查询父部门，直到找到根部门
        DepartmentVO currentDepartment = findDepartmentById(departmentList, departmentId);
        while (currentDepartment != null) {
            // 将部门名称添加到列表头部
            hierarchy.add(0, currentDepartment);
            currentDepartment = findDepartmentById(departmentList, currentDepartment.getParentId());
        }
        return hierarchy;
    }

    // 根据部门ID向上查找部门信息
    private static DepartmentVO findDepartmentById(List<DepartmentVO> departmentList, Long departmentId) {
        for (DepartmentVO department : departmentList) {
            if (department.getId().equals(departmentId)) {
                return department;
            }
        }
        return null;
    }

    // 根据部门ID向下查找部门信息
    private static void displayDepartment(List<Department> departmentList, Long parentId, List<Department> allIds) {
        for (Department department : departmentList) {
            if (department.getParentId().equals(parentId)) {
                allIds.add(department);
                displayDepartment(departmentList, department.getId(),allIds);
            }
        }
    }



    /**
     * 生成部门编码，生成规则为：父部门编码 + 组成id，最大支持9999个子部门
     * @return 部门ID
     */
    private void generateCode(Department entity) {
        Long parentId = entity.getParentId();
        Integer maxFormId = departmentService.getMaxFormIdByParentId(parentId);
        if (maxFormId == null) {
            //组成id默认从1000开始
            maxFormId = DepartmentConstants.DEFAULT_FORM_ID;
        } else {
            maxFormId += 1;
        }
        if (maxFormId > DepartmentConstants.MAX_FORM_ID) {
            throw new BusinessException("部门数量已达上限：" + DepartmentConstants.MAX_FORM_ID);
        }
        entity.setFormId(maxFormId);
        entity.setSort(maxFormId);
        Department parent = departmentService.getById(parentId);
        if (parent == null) {
            //是根部门，直接使用formId做为部门编码
            entity.setDepartmentCode(maxFormId.toString());
        }else {
            entity.setDepartmentCode(parent.getDepartmentCode() + maxFormId);
        }
    }
    private List<DepartmentVO> buildTree(List<DepartmentVO> departmentVOS) {
        Map<Long, DepartmentVO> idDepartmentMap = departmentVOS.stream().collect(Collectors.toMap(DepartmentVO::getId, Function.identity()));
        // 构建树形结构
        List<DepartmentVO> tree = new ArrayList<>();
        for (DepartmentVO departmentVO : departmentVOS) {
            DepartmentVO parent = idDepartmentMap.get(departmentVO.getParentId());
            if (DepartmentConstants.DEFAULT_PARENT_ID.equals(departmentVO.getParentId())) {
                // 如果父部门ID为默认的根部门ID，说明这是一个根部门
                tree.add(departmentVO);
            } else {
                // 如果有父部门，将当前部门添加到父部门的子部门列表中
                if (parent.getSunDepartmentList() == null) {
                    parent.setSunDepartmentList(new ArrayList<>());
                }
                parent.getSunDepartmentList().add(departmentVO);
            }
        }
        // 对树形结构进行排序
        return this.sortDepartments(tree);
    }

    private List<DepartmentVO> sortDepartments(List<DepartmentVO> departmentVOS) {
        // 对部门列表进行排序
        List<DepartmentVO> sortedDepartmentVOS = departmentVOS.stream().sorted(Comparator.comparing(DepartmentVO::getSort)).collect(Collectors.toList());
        // 对每个部门的子部门列表进行排序
        for (DepartmentVO departmentVO : sortedDepartmentVOS) {
            if (departmentVO.getSunDepartmentList() != null) {
                List<DepartmentVO> sortedSumDepartments = sortDepartments(departmentVO.getSunDepartmentList());
                departmentVO.setSunDepartmentList(sortedSumDepartments);
            }
        }
        return sortedDepartmentVOS;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMove(DepartmentUpdateMovePO updateMovePO) {

        if(null==updateMovePO.getNewTargetParentId()){
            updateMovePO.setNewTargetParentId(0L);
        }
        log.info("将部门：{},移动到：{}下",updateMovePO.getCurrDepartmentId(),updateMovePO.getNewTargetParentId());

        Department targetDepartment= departmentService.getById(updateMovePO.getNewTargetParentId());
        if(null!=targetDepartment && targetDepartment.getParentId().equals(updateMovePO.getCurrDepartmentId())){
            throw new BusinessException("当前部门不能移到自己所属子级部门下");
        }


        Department currDepartment=departmentService.getById(updateMovePO.getCurrDepartmentId());
        List<String> names=departmentService.getDepartmentNameByParentId(updateMovePO.getNewTargetParentId());
        if(null!=currDepartment && names.contains(currDepartment.getDepartmentName())){
            throw new BusinessException("当前层级部门名称已经存在:"+currDepartment.getDepartmentName());
        }

        Department moveDep=new Department();
        moveDep.setId(updateMovePO.getCurrDepartmentId());
        moveDep.setParentId(updateMovePO.getNewTargetParentId());
        Integer fromId=DepartmentConstants.DEFAULT_FORM_ID;

        Department maxDep=departmentService.getMaxByParentId(updateMovePO.getNewTargetParentId());
        if(null!=maxDep){
            fromId=maxDep.getFormId()+1;
        }
        if(null==targetDepartment){
            moveDep.setDepartmentCode(String.valueOf(fromId));
        }else {
            moveDep.setDepartmentCode(targetDepartment.getDepartmentCode().concat(String.valueOf(fromId)));
        }
        moveDep.setFormId(fromId);
        departmentService.updateById(moveDep);

        List<Department> departmentList=updateMovePO.getDepSortList().stream().map(obj->{
            Department department1=new Department();
            department1.setId(obj.getDepartmentId());
            department1.setSort(obj.getSort());
            return department1;
        }).collect(Collectors.toList());
        departmentService.updateBatchById(departmentList);
        return Boolean.TRUE;
    }


}