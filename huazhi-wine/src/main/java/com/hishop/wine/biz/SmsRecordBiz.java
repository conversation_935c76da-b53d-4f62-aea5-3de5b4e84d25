package com.hishop.wine.biz;

import com.hishop.wine.model.po.sms.SmsRecordQueryPO;
import com.hishop.common.response.PageResult;
import com.hishop.wine.model.vo.sms.SmsRecordVO;

/**
 * 短信发送记录表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-12
 */

public interface SmsRecordBiz {

    /**
     * 查询短信发送集合
     *
     * @param pagePO 筛选参数
     * @return 短信发送集合
     */
    PageResult<SmsRecordVO> pageList(SmsRecordQueryPO pagePO);

}