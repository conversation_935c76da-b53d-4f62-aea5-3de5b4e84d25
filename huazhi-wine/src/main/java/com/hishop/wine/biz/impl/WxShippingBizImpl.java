package com.hishop.wine.biz.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.assist.service.WxMaShippingService;
import com.hishop.wine.biz.WxShippingBiz;
import com.hishop.wine.model.po.wxShipping.Payer;
import com.hishop.wine.model.po.wxShipping.request.GetOrderRequest;
import com.hishop.wine.model.po.wxShipping.request.IsTradeManagedRequest;
import com.hishop.wine.model.po.wxShipping.request.UploadShippingRequest;
import com.hishop.wine.model.po.wxShipping.response.DeliveryListResponse;
import com.hishop.wine.model.po.wxShipping.response.GetOrderResponse;
import com.hishop.wine.model.po.wxShipping.response.IsTradeManagedResponse;
import com.hishop.wine.model.po.wxShipping.response.UploadShippingResponse;
import com.hishop.wine.repository.entity.MiniApp;
import com.hishop.wine.repository.entity.MiniUser;
import com.hishop.wine.repository.entity.Transaction;
import com.hishop.wine.repository.service.MiniAppService;
import com.hishop.wine.repository.service.MiniUserService;
import com.hishop.wine.repository.service.TransactionService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Date;
import java.util.TimeZone;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
@Service
@Slf4j
public class WxShippingBizImpl implements WxShippingBiz {

    @Resource
    private WxMaShippingService wxMaShippingService;

    @Resource
    private MiniAppService miniAppService;

    @Resource
    private TransactionService transactionService;

    @Resource
    private MiniUserService miniUserService;

    private static final String WX_SHIPPING_IS_TRADED_MANAGED = "wx_shipping_is_traded_managed:";

    @Override
    public Boolean isTradeManaged(String appId) {
        String token = this.queryToken(appId);
        IsTradeManagedRequest requet = new IsTradeManagedRequest();
        requet.setAccessToken(token);
        requet.setAppid(appId);
        try {
            IsTradeManagedResponse response = wxMaShippingService.isTradeManaged(requet);
            RedisUtil.set(WX_SHIPPING_IS_TRADED_MANAGED + appId, response.isTradeManaged(), 60 * 60 * 24);
            return response.isTradeManaged();
        } catch (Exception e) {
            log.error("查询小程序是否开通发货信息管理服务失败", e);
            throw new BusinessException("查询小程序是否开通发货信息管理服务失败");
        }
    }

    @Override
    public Boolean uploadShipping(UploadShippingRequest request) {
        log.info("发货信息录入接口request：{}", JSON.toJSONString(request));
        if(request.getOrder_key() == null) {
            throw new BusinessException("订单信息不能为空");
        }
        if(StringUtils.isEmpty(request.getOrder_key().getOut_trade_no())) {
            throw new BusinessException("订单号不能为空");
        }
        if(CollectionUtils.isEmpty(request.getShipping_list())) {
            throw new BusinessException("物流信息不能为空");
        }
        // OutTradeNo就是transaction表的id,所以可以用来反查交易记录
        Transaction transaction = transactionService.getById(request.getOrder_key().getOut_trade_no());
        if(transaction == null) {
            throw new BusinessException("交易记录不存在");
        }
        String token = this.queryToken(transaction.getAppId());
        request.setAccessToken(token);
        // 走微信支付单号
        request.getOrder_key().setTransaction_id(transaction.getThirdTransactionNo());
        request.getOrder_key().setOrder_number_type(2);
        Payer payer = new Payer();
        payer.setOpenid(transaction.getOpenId());
        request.setPayer(payer);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
        sdf.setTimeZone(TimeZone.getTimeZone("Asia/Shanghai")); // 显式设置时区
        String rfc3339String = sdf.format(new Date());
        request.setUpload_time(rfc3339String);
        try {
            UploadShippingResponse response = wxMaShippingService.uploadShipping(request);
            log.info("发货信息录入接口调用成功:{}", JSON.toJSONString(response));
            if(response.getErrCode() == 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("发货信息录入接口调用失败:{}", e.getMessage());
            throw new BusinessException("发货信息录入接口调用失败");
        }
    }

    @Override
    public Boolean getOrder(GetOrderRequest request) {
        if(StringUtils.isEmpty(request.getTransactionId())) {
            throw new BusinessException("交易记录id不能为空");
        }
        // 这里需要根据交易记录id就是transaction表的third_transaction_no
        Transaction transaction = transactionService.getOne(new LambdaQueryWrapper<Transaction>().eq(Transaction::getThirdTransactionNo, request.getTransactionId()).last("limit 1"));
        if(transaction == null) {
            throw new BusinessException("交易记录不存在");
        }
        String token = this.queryToken(transaction.getAppId());
        request.setAccessToken(token);
        try {
            GetOrderResponse response = wxMaShippingService.getOrder(request);
            if(response.getErrCode() == 0 && response.getOrder() != null) {
                if(response.getOrder().getOrderState() == 3) {
                    return true;
                } else {
                    return false;
                }
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("查询订单接口调用失败:{}", e.getMessage());
            throw new BusinessException("查询订单接口调用失败");
        }
    }

    @Override
    public DeliveryListResponse getDeliveryList(String appId) {
        String token = this.queryToken(appId);
        try {
            DeliveryListResponse deliveryListResponse = wxMaShippingService.getDeliveryList(token);
            return deliveryListResponse;
        } catch (Exception e) {
            log.error("获取运力id列表接口调用失败:{}", e.getMessage());
            throw new BusinessException("获取运力id列表接口调用失败");
        }
    }

    @Override
    public Boolean uploadShippingByAppId(UploadShippingRequest request) {
        if(StringUtils.isEmpty(request.getAppId())) {
            throw new BusinessException("appId不能为空");
        }
        if(request.getUserId() == null) {
            throw new BusinessException("userId不能为空");
        }
        if(request.getOrder_key() == null) {
            throw new BusinessException("订单信息不能为空");
        }
        if(StringUtils.isEmpty(request.getOrder_key().getTransaction_id())) {
            throw new BusinessException("微信支付单号");
        }
        if(CollectionUtils.isEmpty(request.getShipping_list())) {
            throw new BusinessException("物流信息不能为空");
        }
        // 查询用户
        MiniUser miniUser = miniUserService.getOne(new LambdaQueryWrapper<MiniUser>().eq(MiniUser::getUserId, request.getUserId()).eq(MiniUser::getAppId, request.getAppId()).last("limit 1"));
        if(miniUser == null) {
            throw new BusinessException("客户不存在");
        }
        String token = this.queryToken(request.getAppId());
        request.setAccessToken(token);
        // 走微信支付单号
        request.getPayer().setOpenid(miniUser.getOpenId());
        try {
            UploadShippingResponse response = wxMaShippingService.uploadShipping(request);
            if(response.getErrCode() == 0) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            log.error("发货信息录入接口调用失败:{}", e.getMessage());
            throw new BusinessException("发货信息录入接口调用失败");
        }
    }

    private String queryToken(String appId) {
        MiniApp miniApp = miniAppService.getOne(new LambdaQueryWrapper<MiniApp>().eq(MiniApp::getAppId, appId).last("limit 1"));
        if(miniApp == null) {
            throw new BusinessException("小程序未找到");
        }
        String token = wxMaShippingService.getToken(miniApp.getAppId(), miniApp.getAppSecret());
        if(StringUtils.isBlank(token)) {
            throw new BusinessException("小程序token获取失败");
        }
        return token;
    }
}
