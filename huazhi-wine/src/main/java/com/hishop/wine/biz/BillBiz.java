package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.dto.bill.BillCountDto;
import com.hishop.wine.model.dto.bill.BillGroupDto;
import com.hishop.wine.model.po.bill.BillExportPo;
import com.hishop.wine.model.po.bill.BillQueryPo;
import com.hishop.wine.model.vo.bill.BillCountVo;
import com.hishop.wine.model.vo.bill.BillGroupVo;
import com.hishop.wine.model.vo.bill.BillVo;
import com.hishop.wine.repository.entity.Bill;

import java.util.List;

/**
 * 账单管理业务接口
 */
public interface BillBiz {

    /**
     * 账单保存
     * @param bill 入参
     * @return 结果
     */
    void billSave(Bill bill);

    /**
     * 分页查询
     * @param billQueryPo 入参
     * @return 结果
     */
    PageResult<BillVo> pageList(BillQueryPo billQueryPo);

    /**
     * 明细总收入
     * @param billQueryPo 入参
     * @return 结果
     */
    BillCountVo pageListCount(BillQueryPo billQueryPo);

    /**
     * 月统计分页查询
     * @param billQueryPo 入参
     * @return 结果
     */
    PageResult<BillGroupVo> monthPageList(BillQueryPo billQueryPo);

    /**
     * 月统计总收入
     * @param billQueryPo 入参
     * @return 结果
     */
    BillCountVo monthPageListCount(BillQueryPo billQueryPo);

    /**
     * 日统计分页查询
     * @param billQueryPo 入参
     * @return 结果
     */
    PageResult<BillGroupVo> dayPageList(BillQueryPo billQueryPo);

    /**
     * 日统计总收入
     * @param billQueryPo 入参
     * @return 结果
     */
    BillCountVo dayPageListCount(BillQueryPo billQueryPo);

    /**
     * 导出查询
     * @param param 入参
     * @return 结果
     */
    List<BillVo> exportList(BillExportPo param);
}
