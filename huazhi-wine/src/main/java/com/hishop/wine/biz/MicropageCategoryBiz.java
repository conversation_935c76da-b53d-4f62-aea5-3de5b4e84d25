package com.hishop.wine.biz;

import com.hishop.wine.model.po.micropage.MicropageCategoryDeletePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryMovePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryPO;
import com.hishop.wine.model.po.micropage.MicropageCategoryUpdatePO;
import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;

import java.util.List;

public interface MicropageCategoryBiz {
    boolean create(MicropageCategoryPO micropageCategoryPO);

    boolean update(MicropageCategoryUpdatePO micropageCategoryUpdatePO);

    boolean delete(MicropageCategoryDeletePO micropageCategoryDeletePO);

    MicropageCategoryVO detail(int id);

    List<MicropageCategoryVO> list(String name);

    List<MicropageCategoryVO> listParent();

    boolean move(MicropageCategoryMovePO micropageCategoryMovePO);

    List<MicropageCategoryVO> getListByIds(List<Long> cateIds);

    List<MicropageCategoryVO> getListLessThanLevel(int maxLevel, List<Long> cateIds);
}
