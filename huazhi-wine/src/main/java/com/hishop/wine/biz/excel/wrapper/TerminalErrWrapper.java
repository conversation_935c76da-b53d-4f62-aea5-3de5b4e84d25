package com.hishop.wine.biz.excel.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.model.po.terminate.TerminateImportPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class TerminalErrWrapper implements DataWrapper<TerminateImportPo> {

    private final List<TerminateImportPo> dataList;

    public TerminalErrWrapper(List<TerminateImportPo> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<TerminateImportPo> getDataList() {
        return dataList;
    }
}
