package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.assist.UserAssist;
import com.hishop.wine.assist.transaction.TransactionHandlerMapping;
import com.hishop.wine.biz.TransactionBiz;
import com.hishop.wine.common.config.WxAutoMapping;
import com.hishop.wine.common.config.WxAutoMappingConfig;
import com.hishop.wine.enums.TransactionEnum;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionQueryPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.vo.transaction.TransactionEntPayVO;
import com.hishop.wine.model.vo.transaction.TransactionInfoVO;
import com.hishop.wine.model.vo.transaction.TransactionPayVO;
import com.hishop.wine.model.vo.transaction.TransactionRefundVO;
import com.hishop.wine.repository.entity.Transaction;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.param.transaction.TransactionParam;
import com.hishop.wine.repository.service.TransactionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


/**
 * 交易流水表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Slf4j
@Service
public class TransactionBizImpl implements TransactionBiz {

    @Resource
    private TransactionService transactionService;
    @Resource
    private UserAssist userAssist;
    @Resource
    private WxAutoMappingConfig wxAutoMappingConfig;

    private static final Long PAGE_SIZE = 10L;

    /**
     * 发起支付
     *
     * @param payPO 支付参数
     * @return 支付返回值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransactionPayVO pay(TransactionPayPO payPO) {
        log.info("【发起支付】 发起参数: {}", payPO);
        payPO.setUserId(LoginUserUtil.getLoginUser().getUserId());
        // 检测是否指定了非支付的业务类型
        Assert.isTrue(TransactionEnum.Type.PAY.equals(payPO.getBizType().getTransactionType()), "业务类型异常, 请指定PAY类型");
        // 判断该订单是否已经支付
        Boolean izPayed = transactionService.izTransactionSuccess(payPO.getBizType().getType(), payPO.getBizCode());
        Assert.isTrue(!izPayed, "该订单已经支付成功，请勿重复支付");
        // 判断支付模式：WX_PAY 微信支付 WX_SEC_PAY 微信服务商支付
        WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(payPO.getBizType().getModuleCode());
        Assert.isTrue(!ObjectUtil.isNull(wxConfig), "未找到对应的支付配置");
        TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(wxConfig.getPaymentType());
        payPO.setTransactionMethod(method);
        // 调用实际支付方法
        return TransactionHandlerMapping.getHandler(payPO.getTransactionMethod()).pay(payPO);
    }

    /**
     * 判断是否支付中
     * @param payPO
     * @return
     */
    @Override
    public Boolean queryPaying(TransactionQueryPO payPO) {
        Boolean izPayed = transactionService.izTransactionWaiting(payPO.getBizType().getType(), payPO.getBizCode());
        return izPayed;
    }

    /**
     * 支付回调
     * todo 后续如果接入多个平台, 需要考虑如何判断回调的来源， 暂时写死微信回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String notifyPay(String moduleCode, String notifyData) {
        log.info("【支付回调】接收到回调, 模块编码: {}, 回调信息: {}", moduleCode, notifyData);
        WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(moduleCode);
        Assert.isTrue(!ObjectUtil.isNull(wxConfig), "未找到对应的支付配置");
        TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(wxConfig.getPaymentType());
        return TransactionHandlerMapping.getHandler(method).notifyPay(moduleCode, notifyData);
    }

    /**
     * 发起退款
     *
     * @param refundPO 发起退款参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransactionRefundVO refund(TransactionRefundPO refundPO) {
        log.info("【发起退款】发起参数{}", refundPO);
        Assert.isTrue(refundPO.getBizType().getTransactionType().equals(TransactionEnum.Type.REFUND), "业务类型异常, 请指定REFUND类型");
        Transaction orgTransaction = transactionService.getById(refundPO.getOrgTransactionId());
        checkRefundParam(orgTransaction, refundPO);

        TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(orgTransaction.getTransactionMethod());
        return TransactionHandlerMapping.getHandler(method).refund(refundPO, orgTransaction);
    }

    /**
     * 退款回调
     * todo 后续如果接入多个平台, 需要考虑如何判断回调的来源， 暂时写死微信回调
     *
     * @param moduleCode 模块编码
     * @param notifyData 回调参数
     * @return 回调结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String notifyRefund(String moduleCode, String notifyData) {
        log.info("【退款回调】接收到回调, 模块编码: {}, 回调信息: {}", moduleCode, notifyData);
        // 判断支付模式：WX_PAY 微信支付 WX_SEC_PAY 微信服务商支付
        WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(moduleCode);
        Assert.isTrue(!ObjectUtil.isNull(wxConfig), "未找到对应的支付配置");
        TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(wxConfig.getPaymentType());
        return TransactionHandlerMapping.getHandler(method).notifyRefund(moduleCode, notifyData);
    }

    /**
     * 企业付款到零钱
     *
     * @param entPayPO 付款参数
     * @return 付款返回值
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public TransactionEntPayVO entPay(TransactionEntPayPO entPayPO) {
        log.info("【企业打款】发起参数: {}", entPayPO);

        entPayPO.setUserId(LoginUserUtil.getLoginUser().getUserId());
        Assert.isTrue(TransactionEnum.Type.ENTRY_PAY.equals(entPayPO.getBizType().getTransactionType()), "业务类型异常, 请指定ENTRY_PAY类型");
        // 判断是否已经发起打款或者打款成功
        Boolean izSuccess = transactionService.izTransactionSuccess(entPayPO.getBizType().getType(), entPayPO.getBizCode());
        Assert.isTrue(!izSuccess, "打款已成功, 无需重复打款");
        Boolean izConfirm = transactionService.izTransactionWaiting(entPayPO.getBizType().getType(), entPayPO.getBizCode());
        Assert.isTrue(!izConfirm, "打款确认中, 请勿重复打款");

        // 判断支付模式：WX_PAY 微信支付 WX_SEC_PAY 微信服务商支付
        WxAutoMapping wxConfig = wxAutoMappingConfig.getWxAutoMapping(entPayPO.getBizType().getModuleCode());
        Assert.isTrue(!ObjectUtil.isNull(wxConfig), "未找到对应的支付配置");
        TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(wxConfig.getPaymentType());
        entPayPO.setTransactionMethod(method);

        // 发起打款
        return TransactionHandlerMapping.getHandler(entPayPO.getTransactionMethod()).entPay(entPayPO);
    }

    /**
     * 检查企业打款交易状态
     */
    @Override
    public void checkEntPayResult() {
        log.info("【企业打款】检查交易状态");
        Long page = 1L;
        Boolean izEnd = Boolean.FALSE;
        LambdaQueryWrapper<Transaction> wrapper = new LambdaQueryWrapper<Transaction>()
                .eq(Transaction::getStatus, TransactionEnum.Status.WAITING.getStatus()).eq(Transaction::getTransactionType, TransactionEnum.Type.ENTRY_PAY.name());
        while (!izEnd) {
            log.info("【企业打款】检查交易状态, 当前页: {}", page);
            Page<Transaction> result = transactionService.page(new Page<>(page, PAGE_SIZE), wrapper);
            List<Transaction> records = result.getRecords();

            records.forEach(record -> {
                TransactionEnum.MethodEnum method = TransactionEnum.MethodEnum.getEnum(record.getTransactionMethod());
                TransactionHandlerMapping.getHandler(method).checkEntPayResult(record);
            });

            if (records.size() < PAGE_SIZE) {
                izEnd = Boolean.TRUE;
            }
            page ++;
        }
        log.info("【企业打款】检查交易状态, 当前页: {}, 已经是最后一页, 检查结束", page - 1);
    }

    /**
     * 交易查询
     *
     * @param queryPO 查询参数
     * @return 交易流水
     */
    @Override
    public PageResult<TransactionInfoVO> queryTransaction(TransactionQueryPO queryPO) {
        log.info("【交易查询】查询参数: {}", queryPO);
        TransactionParam param = BeanUtil.copyProperties(queryPO, TransactionParam.class);
        param.setStatus(ObjectUtil.isNotNull(queryPO.getStatus()) ? queryPO.getStatus().getStatus() : null);
        Page<Transaction> dbResult = transactionService.queryTransaction(queryPO.buildPage(), param);

        List<Long> userIds = dbResult.getRecords().stream().map(Transaction::getCreateBy).collect(Collectors.toList());
        Map<Long, User> userMap = userAssist.getUserMap(userIds);
        return PageResultHelper.transfer(dbResult, TransactionInfoVO.class, (db, vo) -> {
            vo.setTransactionId(db.getId());
            vo.setTransactionMethodName(TransactionEnum.MethodEnum.getDesc(vo.getTransactionMethod()));
            User user = userMap.get(db.getCreateBy());
            if (ObjectUtil.isNotNull(user)) {
                vo.setCreateName(StrUtil.isNotEmpty(user.getRealName()) ? user.getRealName() : user.getNickName());
            }
        });
    }

    @Override
    public String getMchId(Long id) {
        Transaction transaction = transactionService.getById(id);
        if(transaction != null) {
            return transaction.getMchId();
        } else {
            return "";
        }
    }

    /**
     * 检测退款参数
     *
     * @param transaction 原支付记录
     * @param refundPO    退款参数
     */
    private void checkRefundParam(Transaction transaction, TransactionRefundPO refundPO) {
        Assert.isTrue(ObjectUtil.isNotNull(transaction), "交易记录不存在");
        Assert.isTrue(transaction.getTransactionType().equals(TransactionEnum.Type.PAY.name()), "非支付交易记录, 无法退款");
        Assert.isTrue(transaction.getStatus().equals(TransactionEnum.Status.SUCCESS.getStatus()), "交易未成功, 无法退款");
        Assert.isTrue(!TransactionEnum.RefundStatus.ALL_REFUND.getStatus().equals(transaction.getRefundStatus()), "该交易已全部退款, 无法再次退款");
        Assert.isTrue(refundPO.getAmount().compareTo(transaction.getAmount()) <= 0, "退款金额不能大于支付金额");
        Assert.isTrue(refundPO.getAmount().compareTo(BigDecimal.ZERO) > 0, "退款金额必须大于0");
        transaction.setRefundAmount(ObjectUtil.defaultIfNull(transaction.getRefundAmount(), BigDecimal.ZERO));
        Assert.isTrue(refundPO.getAmount().compareTo(transaction.getAmount().subtract(transaction.getRefundAmount())) <= 0, "退款金额不能大于可退金额");
    }
}