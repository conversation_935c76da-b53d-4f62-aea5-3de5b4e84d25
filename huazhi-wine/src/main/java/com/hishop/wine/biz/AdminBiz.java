package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.admin.BusinessUserQueryPo;
import com.hishop.wine.model.po.basic.AdminCreatePO;
import com.hishop.wine.model.po.basic.AdminQueryPO;
import com.hishop.wine.model.po.basic.AdminUpdatePO;
import com.hishop.wine.model.vo.basic.AdminDetailVO;
import com.hishop.wine.model.vo.basic.AdminVO;
import com.hishop.wine.model.vo.user.BusinessUserVo;
import com.hishop.wine.model.vo.user.UserDetailVo;

/**
 * 管理员管理
 *
 * <AUTHOR>
 * @date : 2023/7/25
 */
public interface AdminBiz {

    /**
     * 创建管理员
     *
     * @param adminCreatePO 创建管理员参数
     */
    void createAdmin(AdminCreatePO adminCreatePO);

    /**
     * 更新管理员
     *
     * @param adminUpdatePO 更新管理员参数
     */
    void updateAdmin(AdminUpdatePO adminUpdatePO);

    /**
     * 查询管理员列表
     *
     * @param adminQueryPO 分页参数
     * @return 管理员列表
     */
    PageResult<AdminVO> pageListAdmin(AdminQueryPO adminQueryPO);


    /**
     * 分页查询管理员部门列表
     *
     * @param adminQueryPO 分页参数
     * @return 管理员列表
     */
    PageResult<AdminVO> pageListAdminDep(AdminQueryPO adminQueryPO);


    /**
     * 查询管理员详情
     *
     * @param id 身份id
     * @return 管理员详情
     */
    AdminDetailVO getAdminDetail(Long id);

    /**
     * 用户详情
     *
     * @param id 身份id
     */
    UserDetailVo userDetail(Long id);

    /**
     * 业务员列表
     *
     * @param queryPo 查询参数
     * @return 业务员列表
     */
    PageResult<BusinessUserVo> businessPageList(BusinessUserQueryPo queryPo);

    /**
     * 用户详情
     * @param userId 用户id
     * @return 用户信息
     */
    UserDetailVo userDetailByUserId(Long userId);
}
