package com.hishop.wine.biz.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.HeaderUtil;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.common.util.PasswordUtil;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.WechatWebUserBiz;
import com.hishop.wine.constants.WechatConstants;
import com.hishop.wine.enums.WechatEnum;
import com.hishop.wine.model.vo.wechat.WebQrCodeInitParamVO;
import com.hishop.wine.repository.entity.User;
import com.hishop.wine.repository.entity.WechatWebUser;
import com.hishop.wine.repository.service.UserService;
import com.hishop.wine.repository.service.WechatWebUserService;
import lombok.extern.slf4j.Slf4j;
import javax.annotation.Resource;

import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.common.bean.oauth2.WxOAuth2AccessToken;
import me.chanjar.weixin.common.service.WxOAuth2Service;
import me.chanjar.weixin.mp.api.WxMpService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;


/**   
 * 微信网页应用用户表 业务逻辑实现类
 * @author: HuBiao
 * @date: 2023-07-10
 */

@Slf4j
@Service
public class WechatWebUserBizImpl implements WechatWebUserBiz {

    @Resource
    private WechatWebUserService wechatWebUserService;
    @Resource(name = "wxWebService")
    private WxMpService wxMpService;
    @Resource
    private UserService userService;

    /**
     * 查询生成小程序码参数
     *
     * @param type 码类型 1-绑定码 2-登陆码
     * @return 生成小程序码参数
     */
    @Override
    public WebQrCodeInitParamVO getQrCodeParam(Integer type) {
        WechatEnum.WxWebQuCodeTypeEnum typeEnum = WechatEnum.WxWebQuCodeTypeEnum.getByType(type);
        Assert.isTrue(ObjectUtil.isNotNull(typeEnum), "码类型错误");

        String state = IdUtil.fastSimpleUUID();
        String appId = wxMpService.getWxMpConfigStorage().getAppId();
        Assert.isTrue(!StrUtil.isEmpty(appId), "暂未绑定微信开放平台参数, 无法使用微信扫码功能");
        WebQrCodeInitParamVO result = WebQrCodeInitParamVO.of(appId, typeEnum.getScope(), state);

        String redisKey = String.format(WechatConstants.WECHAT_WEB_QR_CODE_STATE, state);
        RedisUtil.set(redisKey, 1, 60 * 60);
        return result;
    }

    /**
     * 绑定微信用户
     *
     * @param code  微信扫码获取的code
     * @param state 校验回调参数
     * @return 微信昵称
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String bind(String code, String state) {
        checkState(state);
        WxOAuth2UserInfo userInfo = getWxUserInfo(code);
        Long userId = LoginUserUtil.getLoginUser().getUserId();
        String appId = wxMpService.getWxMpConfigStorage().getAppId();

        // 检查扫码的微信
        long bindUserFlag = wechatWebUserService.count(new LambdaQueryWrapper<WechatWebUser>()
                .eq(WechatWebUser::getOpenId, userInfo.getOpenid()).eq(WechatWebUser::getAppId, appId));
        Assert.isTrue(bindUserFlag == 0, "该微信已绑定其他用户, 请更换微信");

        // 先解绑
        unBind();

        // 保存用户网页用户信息
        WechatWebUser wechatWebUser = new WechatWebUser();
        wechatWebUser.setUserId(userId);
        wechatWebUser.setNickName(userInfo.getNickname());
        wechatWebUser.setAvatarUrl(userInfo.getHeadImgUrl());
        wechatWebUser.setAppId(appId);
        wechatWebUser.setOpenId(userInfo.getOpenid());
        wechatWebUser.setUnionId(userInfo.getUnionId());
        wechatWebUser.setRegisterModuleCode(HeaderUtil.getModuleCode());
        wechatWebUserService.save(wechatWebUser);

        // 更新用户表信息
        User updUser = new User();
        updUser.setId(userId);
        updUser.setNickName(userInfo.getNickname());
        updUser.setIcon(userInfo.getHeadImgUrl());
        userService.updateById(updUser);

        // 删除凭据
        RedisUtil.del(String.format(WechatConstants.WECHAT_WEB_QR_CODE_STATE, state));
        return userInfo.getNickname();
    }

    /**
     * 解绑微信
     */
    @Override
    public void unBind() {
        String appId = wxMpService.getWxMpConfigStorage().getAppId();
        Long userId = LoginUserUtil.getLoginUser().getUserId();

        wechatWebUserService.remove(new LambdaQueryWrapper<WechatWebUser>()
                .eq(WechatWebUser::getUserId, userId).eq(WechatWebUser::getAppId, appId));
    }

    /**
     * 获取微信网页用户信息
     *
     * @param code 微信扫码参数
     * @return 微信用户信息
     */
    @Override
    public WechatWebUser getWechatWebUser(String code) {
        WxOAuth2UserInfo userInfo = getWxUserInfo(code);
        String appId = wxMpService.getWxMpConfigStorage().getAppId();

        return wechatWebUserService.getOne(new LambdaQueryWrapper<WechatWebUser>()
                .eq(WechatWebUser::getOpenId, userInfo.getOpenid()).eq(WechatWebUser::getAppId, appId));
    }

    /**
     * 校验凭证
     *
     * @param state 扫码校验
     */
    @Override
    public void checkState(String state) {
        String redisKey = String.format(WechatConstants.WECHAT_WEB_QR_CODE_STATE, state);
        Assert.isTrue(ObjectUtil.isNotNull(RedisUtil.get(redisKey)), "二维码已失效, 请刷新页面后重试");
    }


    /**
     * 获取微信网页用户信息
     *
     * @param userId 用户id
     * @return 微信用户信息
     */
    @Override
    public WechatWebUser getWechatWebUser(Long userId) {
        String appId = wxMpService.getWxMpConfigStorage().getAppId();
        return wechatWebUserService.getOne(new LambdaQueryWrapper<WechatWebUser>()
                .eq(WechatWebUser::getUserId, userId).eq(WechatWebUser::getAppId, appId));
    }

    /**
     * 获取微信用户信息
     *
     * @param code 微信扫码参数
     * @return 微信用户信息
     */
    private WxOAuth2UserInfo getWxUserInfo(String code) {
        try {
            WxOAuth2Service oAuth2Service = wxMpService.getOAuth2Service();
            WxOAuth2AccessToken accessToken = oAuth2Service.getAccessToken(code);
            WxOAuth2UserInfo userInfo = oAuth2Service.getUserInfo(accessToken, null);
            return userInfo;
        } catch (Exception e) {
            log.error("获取微信用户信息失败: 【{}】", e.getMessage());
            throw new BusinessException("获取微信用户信息失败");
        }
    }
}