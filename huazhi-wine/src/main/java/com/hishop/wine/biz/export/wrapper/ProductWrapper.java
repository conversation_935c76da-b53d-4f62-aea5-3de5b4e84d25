package com.hishop.wine.biz.export.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.biz.export.model.ProductEO;

import java.util.List;

/**

 * @author: HuBiao
 * @date: 2023-07-05
 */
public class ProductWrapper implements DataWrapper<ProductEO> {

    private final List<ProductEO> dataList;

    public ProductWrapper(List<ProductEO> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<ProductEO> getDataList() {
        return this.dataList;
    }

    @Override
    public String sheetName() {
        return "产品列表";
    }
}
