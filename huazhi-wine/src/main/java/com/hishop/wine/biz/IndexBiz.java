package com.hishop.wine.biz;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.wine.common.enums.IndexTrendEnum;
import com.hishop.wine.model.vo.index.*;

import java.util.List;

/**
 * @description: 首页业务逻辑接口
 * @author: chenzw
 * @date: 2024/6/25 11:21
 */
public interface IndexBiz {

    /**
     * 数据概览
     *
     * @return 数据概览
     */
    IndexDataOverviewVo getDataOverview();

    /**
     * 常用功能
     *
     * @return 常用功能列表
     */
    List<IndexCommonMenuVo> queryCommonMenuList();

    /**
     * 添加常用功能
     * @param idBatchPo id集合
     */
    void addCommonMenu(IdBatchPO<Long> idBatchPo);

    /**
     * 经营趋势
     * @return 经营趋势列表
     */
    List<IndexBusinessTrendVo> queryIndexBusinessTrendList(IndexTrendEnum indexTrendEnum);

    /**
     * 应用推荐和小程序列表
     */
    IndexAppWechatVo getIndexAppWechatList();

    /**
     * 客户数据列表
     */
    IndexCustomDataVo getIndexCustomData();
}
