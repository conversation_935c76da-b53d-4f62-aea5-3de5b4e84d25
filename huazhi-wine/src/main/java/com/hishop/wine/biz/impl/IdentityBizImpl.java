package com.hishop.wine.biz.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.pojo.IdStatusPo;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.wine.biz.IdentityBiz;
import com.hishop.wine.repository.entity.Identity;
import com.hishop.wine.repository.service.DepartmentEmployeeService;
import com.hishop.wine.repository.service.IdentityService;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;


/**
 * 用户身份表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Slf4j
@Service
public class IdentityBizImpl implements IdentityBiz {

    @Resource
    private IdentityService identityService;
    @Resource
    private DepartmentEmployeeService departmentEmployeeService;

    /**
     * 移除身份
     *
     * @param ids 身份id的集合
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public void deleteByIds(List<Long> ids) {
        List<Identity> identityList = identityService.listByIds(ids);
        if(CollectionUtils.isEmpty(identityList)) {
            return;
        }
        List<Long> userIds = identityList.stream().map(Identity::getUserId).collect(Collectors.toList());
        Long currentUserId = LoginUserUtil.getLoginUser().getUserId();
        identityService.logicDeleteByIds(ids, currentUserId);
        //还需要删除部门员工信息
        departmentEmployeeService.deleteUserIds(userIds);
    }

    /**
     * 更新身份状态
     *
     * @param idStatusPo 身份ids和状态
     */
    @Override
    public void updateStatusByIds(IdStatusPo<Long> idStatusPo) {
        Identity updIdentity = new Identity();
        updIdentity.setStatus(idStatusPo.getStatus());
        identityService.update(updIdentity, new LambdaQueryWrapper<Identity>().in(Identity::getId, idStatusPo.getIds()));
    }
}