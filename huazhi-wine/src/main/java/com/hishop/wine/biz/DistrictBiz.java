package com.hishop.wine.biz;

import com.hishop.wine.model.po.basic.DistrictCreatePO;
import com.hishop.wine.model.po.basic.DistrictUpdatePO;
import com.hishop.wine.model.vo.basic.*;

import java.util.List;

/**
 * 地区表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-06-26
 */
public interface DistrictBiz {

    /**
     * 查询行政区域树
     *
     * @param maxLevel 最大层级
     * @return 行政区域树
     */
    List<DistrictTreeVO> tree(Integer maxLevel);

    /**
     * 获取区域列表
     *
     * @param parentId 上级id
     * @return 区域列表
     */
    List<DistrictVO> list(Integer parentId);

    /**
     * 获取区域详情
     *
     * @param id 区域id
     * @return 区域详情
     */
    DistrictVO detail(Integer id);

    /**
     * 获取区域信息详情
     *
     * @param id 区域id
     * @return 区域详情
     */
    DistrictDetailVO getDistrictDetail(Integer id);

    /**
     * 查询大区集合
     *
     * @return 大区集合
     */
    List<RegionVO> listRegions();

    /**
     * 根据等级查询地区信息
     *
     * @param maxLevel 最大等级
     * @return 地区信息
     */
    List<DistrictVO> listByLevel(Integer maxLevel);

    /**
     * 获取基于大区的行政区域
     *
     * @param maxLevel 查询到的最小层级，默认为 省市区
     * <AUTHOR>
     * @date 2023/7/13
     */
    List<RegionTreeVO> regionTree(Integer maxLevel);

    /**
     * 同步地图数据
     */
    void syncDistrict();

    /**
     * 新增地区
     *
     * @param districtCreatePO 新增地区参数
     */
    void createDistrict(DistrictCreatePO districtCreatePO);

    /**
     * 编辑地区
     *
     * @param districtUpdatePO 编辑地区参数
     */
    void updateDistrict(DistrictUpdatePO districtUpdatePO);

    /**
     * 删除地区
     *
     * @param id 地区id
     */
    void deleteById(Integer id);

    /**
     * 移除缓存
     */
    void removeCache();

    /**
     * 根据最后一级id 获取区域列表
     *
     * @param lastId 最后一级id
     * @return 层级区域列表
     */
    List<DistrictVO> listByLastId(Integer lastId);
}