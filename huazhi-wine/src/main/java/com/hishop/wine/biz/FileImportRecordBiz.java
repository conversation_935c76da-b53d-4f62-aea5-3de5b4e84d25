package com.hishop.wine.biz;

import com.hishop.common.response.PageResult;
import com.hishop.wine.model.po.fileImport.FileImportRecordQueryPo;
import com.hishop.wine.model.vo.fileImport.FileImportRecordVo;
import org.springframework.web.multipart.MultipartFile;

/**
 * @description: 文件导入记录业务接口
 * @author: chenzw
 * @date: 2024/7/6 10:30
 */
public interface FileImportRecordBiz {

    /**
     * 根据主键删除
     * @param id 主键
     */
    void deleteById(Long id);

    /**
     * 根据业务类型分页获取导入记录
     * @param pagePo 分页查询条件
     * @return 分页结果
     */
    PageResult<FileImportRecordVo> pageList(FileImportRecordQueryPo pagePo);

    /**
     * 根据业务类型分页获取物流码导入记录
     * @param pagePo 分页查询条件
     * @return 分页结果
     */
    PageResult<FileImportRecordVo> pageLogisticsList(FileImportRecordQueryPo pagePo);

    /**
     * 导入物流码
     * @param file excel文件
     * @param name 批次名称
     */
    void fileImportLogisticsCode(MultipartFile file, String name);

    /**
     * 导入扫码营销物流码
     * @param file excel文件
     * @param name 批次名称
     */
    void fileImportLogisticsCodeScan(MultipartFile file, String name);

    /**
     * 根据产品编码分页获取物流码导入记录
     * @param pagePo 分页查询条件
     * @return 分页结果
     */
    PageResult<FileImportRecordVo> queryFileImportCodePageList(FileImportRecordQueryPo pagePo);

    /**
     * 分页获取物流码导入记录
     * @param pagePo 分页查询条件
     * @return 分页结果
     */
    PageResult<FileImportRecordVo> queryLogisticsCodePageList(FileImportRecordQueryPo pagePo);

    /**
     * 查询批次名称是否重复
     * @param name 批次名称
     * @return 是否重复
     */
    Boolean queryNameIsRepeat(String name);
}
