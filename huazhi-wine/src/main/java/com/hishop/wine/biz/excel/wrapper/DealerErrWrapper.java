package com.hishop.wine.biz.excel.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.model.po.dealer.DealerImportPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class DealerErrWrapper implements DataWrapper<DealerImportPo> {

    private final List<DealerImportPo> dataList;

    public DealerErrWrapper(List<DealerImportPo> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<DealerImportPo> getDataList() {
        return dataList;
    }

}
