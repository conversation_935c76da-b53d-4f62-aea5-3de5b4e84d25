package com.hishop.wine.biz.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.constants.CommonConstants;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.biz.ModuleBiz;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.common.enums.ResourceTypeEnums;
import com.hishop.wine.enums.ModuleEnums;
import com.hishop.wine.model.po.basic.ResourceSyncPO;
import com.hishop.wine.model.vo.basic.ResourceTreeVo;
import com.hishop.wine.repository.entity.Resource;
import com.hishop.wine.model.po.basic.ResourceCreatePO;
import com.hishop.wine.model.po.basic.ResourceUpdatePO;
import com.hishop.wine.biz.ResourceBiz;
import com.hishop.wine.repository.service.ResourceService;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseEnum;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import cn.hutool.core.bean.BeanUtil;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * 资源表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class ResourceBizImpl implements ResourceBiz {

    private final ResourceService resourceService;
    private final ModuleBiz moduleBiz;

    /**
     * 创建资源
     *
     * @param createPO 创建资源参数
     */
    @Override
    public void create(ResourceCreatePO createPO) {
        Resource entity = BeanUtil.copyProperties(createPO, Resource.class);

        // 检测资源信息
        checkResource(entity);

        resourceService.save(entity);

        // 清除缓存
        removeCache();
    }

    /**
     * 编辑资源
     *
     * @param updatePO 编辑资源参数
     */
    @Override
    public void update(ResourceUpdatePO updatePO) {
        Resource entity = resourceService.getById(updatePO.getId());
        if (entity == null || DeleteFlagEnums.checkDelete(entity.getIzDelete())) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        Resource updateEntity = BeanUtil.copyProperties(updatePO, Resource.class);

        // 检测资源信息
        checkResource(updateEntity);

        resourceService.updateById(updateEntity);

        // 清除缓存
        removeCache();
    }

    /**
     * 检测新增/编辑资源参数
     *
     * @param resource 资源参数
     */
    private void checkResource(Resource resource) {
        // 如果是一级资源 只允许为目录
        if (ObjectUtil.isNull(resource.getParentId()) || CommonConstants.DEFAULT_LONG_PARENT_ID.equals(resource.getParentId())) {
            if (!ResourceTypeEnums.DIRECTORY.getType().equals(resource.getType())) {
                throw new BusinessException("一级资源只允许为目录");
            }
        } else {
            // 如果不是一级资源 只允许为菜单或按钮
            if (!ResourceTypeEnums.MENU.getType().equals(resource.getType()) && !ResourceTypeEnums.BUTTON.getType().equals(resource.getType())) {
                throw new BusinessException("非一级资源只允许为菜单或按钮");
            }

            // 如果上级资源是按钮, 则不允许关联下级资源
            Resource parentResource = resourceService.getById(resource.getParentId());
            if (ResourceTypeEnums.BUTTON.getType().equals(parentResource.getType())) {
                throw new BusinessException("按钮资源不允许关联下级资源");
            }
        }

        // 上级资源不能为自己
        if (ObjectUtil.isNotNull(resource.getId()) && resource.getId().equals(resource.getParentId())) {
            throw new BusinessException("上级资源不能为自己");
        }

        // 判断资源编码是否重复
        LambdaQueryWrapper<Resource> wrapper = new LambdaQueryWrapper<Resource>()
                .eq(Resource::getPrivilege, resource.getPrivilege()).eq(Resource::getIzDelete, DeleteFlagEnums.NO.getCode());
        if (ObjectUtil.isNotNull(resource.getId())) {
            wrapper.ne(Resource::getId, resource.getId());
        }
        long count = resourceService.count(wrapper);
        if (count > 0) {
            throw new BusinessException("资源编码已存在");
        }
    }

    /**
     * 删除资源
     *
     * @param id 资源id
     */
    @Override
    public void deleteById(Long id) {
        Resource resource = new Resource();
        resource.setId(id);
        resource.setIzDelete(id);

        resourceService.updateById(resource);

        // 清除缓存
        removeCache();
    }

    /**
     * 查询资源树
     *
     * @param ignoreButton 是否忽略按钮资源
     * @return 资源树
     */
    @Override
    public List<ResourceTreeVo> tree(Boolean ignoreButton) {
        // todo 要根据当前用户所具有的权限来获取资源树
        String redisKey = ignoreButton ? BasicCacheConstants.PAGES_TREE_CACHE : BasicCacheConstants.RESOURCE_TREE_CACHE;
        List<ResourceTreeVo> cacheObj = RedisUtil.get(BasicCacheConstants.RESOURCE_TREE_CACHE);
        if (ObjectUtil.isNotNull(cacheObj)) {
            return cacheObj;
        }

        // 查询授权模块
        List<String> authModuleCodes = moduleBiz.listAuthModuleCodes();
        authModuleCodes.add(ModuleEnums.basic_system.name());

        LambdaQueryWrapper<Resource> wrapper = new LambdaQueryWrapper<Resource>()
                .in(Resource::getModuleCode, authModuleCodes)
                .eq(Resource::getIzDelete, DeleteFlagEnums.NO.getCode())
                .eq(Resource::getStatus, Boolean.TRUE);
        // 如果是忽略按钮资源 则不查询按钮资源
        if (ignoreButton) {
            wrapper.ne(Resource::getType, ResourceTypeEnums.BUTTON.getType());
        }
        List<Resource> dbResourceList = resourceService.list(wrapper);
        List<ResourceTreeVo> resourceList = BeanUtil.copyToList(dbResourceList, ResourceTreeVo.class);
        Map<Long, List<ResourceTreeVo>> resourceMap = resourceList.stream().collect(Collectors.groupingBy(ResourceTreeVo::getParentId));

        // 构建树
        List<ResourceTreeVo> rootList = resourceList.stream().filter(resource -> {
            resource.setChildren(resourceMap.getOrDefault(resource.getId(), Collections.emptyList()));
            resource.getChildren().stream().sorted(Comparator.comparing(ResourceTreeVo::getOrderNum).thenComparing(ResourceTreeVo::getId));
            return resource.getParentId().equals(CommonConstants.DEFAULT_LONG_PARENT_ID);
        }).collect(Collectors.toList());

        RedisUtil.setDefaultTime(redisKey, rootList);
        return rootList;
    }

    /**
     * 清除缓存
     */
    @Override
    public void removeCache() {
        RedisUtil.del(BasicCacheConstants.RESOURCE_TREE_CACHE);
        RedisUtil.del(BasicCacheConstants.PAGES_TREE_CACHE);
    }

    /**
     * 同步资源
     *
     * @param resourceSyncPO 同步参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncResource(ResourceSyncPO resourceSyncPO) {
        // 先把所以资源清空
        resourceService.remove(null);

        Map<String, ResourceSyncPO.ProductListPO> moduleMap = resourceSyncPO.getProductList().stream()
                .collect(Collectors.toMap(ResourceSyncPO.ProductListPO::getType, Function.identity()));

        List<ResourceSyncPO.MenuListPO> menuList = resourceSyncPO.getMenuList();
        menuList.forEach(menu -> {
            // 初始化一级菜单
            String moduleCode = StrUtil.toUnderlineCase(menu.getAppName());

            ResourceSyncPO.ProductListPO module = moduleMap.get(menu.getAppName());
            Resource moduleResource = new Resource();
            moduleResource.setId(module.getId());
            moduleResource.setParentId(CommonConstants.DEFAULT_LONG_PARENT_ID);
            moduleResource.setParentIds(String.valueOf(moduleResource.getId()));
            moduleResource.setName(module.getName());
            moduleResource.setModuleCode(moduleCode);
            moduleResource.setPrivilege(StrUtil.EMPTY);
            moduleResource.setMenuLayout(menu.getMenuLayout());
            moduleResource.setDefaultResourceId(menu.getDefaultIndex().getId());
            moduleResource.setDefaultResourcePath(menu.getDefaultIndex().getPath());
            moduleResource.setType(ResourceTypeEnums.MODULE.getType());
            resourceService.save(moduleResource);

            List<ResourceSyncPO.MenuListPO.MenuDataPO> dataList = menu.getMenuData();
            if (CollectionUtils.isEmpty(dataList)) {
                return;
            }
            recursionSync(moduleResource, dataList, 1);
        });

        removeCache();
    }

    /**
     * 排除下级id(如果上级id在集合中存在)
     *
     * @param resourceIds 资源id
     * @return 排除下级后资源id的集合
     */
    @Override
    public List<Long> excludeSubResourceIds(List<Long> resourceIds) {
        if (CollectionUtils.isEmpty(resourceIds)) {
            return resourceIds;
        }
        List<Resource> resourceList = resourceService.list(new LambdaQueryWrapper<Resource>().in(Resource::getId, resourceIds));
        if (CollectionUtils.isEmpty(resourceList)) {
            return resourceIds;
        }

        // 存放上级id的map
        Map<Long, List<Long>> parentIdsMap = new HashMap<>();
        resourceList.forEach(resource -> {
            List<Long> parentIds = Arrays.asList(resource.getParentIds().split(StrUtil.COMMA)).stream().map(Long::valueOf).collect(Collectors.toList());
            parentIds.remove(parentIds.size() - 1);
            parentIdsMap.put(resource.getId(), parentIds);
        });

        return resourceIds.stream().filter(resourceId -> !parentIdsMap.get(resourceId).stream().anyMatch(parentId -> resourceIds.contains(parentId))).collect(Collectors.toList());
    }

    /**
     * 递归同步
     *
     * @param parentResource 当前父级资源
     * @param dataList       资源数据
     * @param level          递归层级
     */
    private void recursionSync(Resource parentResource, List<ResourceSyncPO.MenuListPO.MenuDataPO> dataList, Integer level) {
        if (CollectionUtils.isEmpty(dataList)) {
            return;
        }

        dataList.forEach(data -> {
            Resource resource = new Resource();
            resource.setId(data.getId());
            resource.setParentId(parentResource.getId());
            resource.setParentIds(parentResource.getParentIds() + StrUtil.COMMA + resource.getId());
            resource.setName(data.getName());
            resource.setModuleCode(parentResource.getModuleCode());
            resource.setPrivilege(StrUtil.EMPTY);
            resource.setIzGroup(data.getIzGroup());
            resource.setIzTab(data.getIzTab());
            resource.setIzShow(data.getIzShow());
            resource.setIcon(data.getIcon());
            resource.setPath(data.getPath());
            resource.setType(level == 1 ? ResourceTypeEnums.DIRECTORY.getType() : ResourceTypeEnums.MENU.getType());
            resourceService.save(resource);

            recursionSync(resource, data.getMenus(), level + 1);
        });

    }
}