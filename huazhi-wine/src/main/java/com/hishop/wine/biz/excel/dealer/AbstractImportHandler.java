package com.hishop.wine.biz.excel.dealer;

import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.excel.read.RowReadResult;
import com.hishop.common.export.context.DataWrapper;
import com.hishop.common.export.model.BizType;
import com.hishop.wine.biz.excel.context.BizContent;
import com.hishop.wine.repository.entity.FileImportRecord;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public abstract class AbstractImportHandler<D extends RowReadResult> {

    /**
     * 业务类型
     * @return
     */
    public abstract BizType bizType();

    /**
     * 检查数据是否存在并设置值
     * @param importResult 导入结果
     * @param bizContent 业务内容
     */
    public abstract void checkExistsAndSetValue(ReadResult<D> importResult, BizContent bizContent, FileImportRecord record);

    /**
     * 保存导入数据
     * @param successList 成功数据
     */
    public abstract void saveImportData(List<D> successList, FileImportRecord record);

    /**
     * 封装导入数据
     * @param errList 错误数据
     * @return 封装结果
     */
    protected abstract DataWrapper<D> wrapData(List<D> errList);

}
