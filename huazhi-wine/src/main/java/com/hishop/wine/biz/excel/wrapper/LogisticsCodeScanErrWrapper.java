package com.hishop.wine.biz.excel.wrapper;

import com.hishop.common.export.context.DataWrapper;
import com.hishop.wine.model.po.logisticsCodeScan.LogisticsCodeScanImportPo;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/7/12
 */
public class LogisticsCodeScanErrWrapper implements DataWrapper<LogisticsCodeScanImportPo> {

    private final List<LogisticsCodeScanImportPo> dataList;

    public LogisticsCodeScanErrWrapper(List<LogisticsCodeScanImportPo> dataList) {
        this.dataList = dataList;
    }

    @Override
    public List<LogisticsCodeScanImportPo> getDataList() {
        return dataList;
    }

}
