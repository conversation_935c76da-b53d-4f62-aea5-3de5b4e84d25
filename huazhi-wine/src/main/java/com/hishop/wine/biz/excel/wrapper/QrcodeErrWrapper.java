//package com.hishop.wine.biz.excel.wrapper;
//
//import com.hishop.common.export.context.DataWrapper;
//import com.hishop.scan.model.po.qrcode.QrcodeImportPO;
//
//import java.util.List;
//
///**
// * <AUTHOR>
// * @date 2023/7/12
// */
//public class QrcodeErrWrapper implements DataWrapper<QrcodeImportPO> {
//
//    private final List<QrcodeImportPO> dataList;
//
//    public QrcodeErrWrapper(List<QrcodeImportPO> dataList) {
//        this.dataList = dataList;
//    }
//
//    @Override
//    public List<QrcodeImportPO> getDataList() {
//        return dataList;
//    }
//}
