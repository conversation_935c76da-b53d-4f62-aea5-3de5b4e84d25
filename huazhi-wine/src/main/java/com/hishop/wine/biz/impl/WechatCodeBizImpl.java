package com.hishop.wine.biz.impl;

import cn.binarywang.wx.miniapp.api.WxMaQrcodeService;
import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.nfs.api.NFS;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.biz.WechatCodeBiz;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.common.config.WxAutoMapping;
import com.hishop.wine.common.config.WxAutoMappingConfig;
import com.hishop.wine.model.po.wechat.WechatCodeCreatePO;
import com.hishop.wine.model.po.wechat.WechatSavePO;
import com.hishop.wine.model.vo.wechat.WechatCodeVO;
import com.hishop.wine.repository.entity.WechatCode;
import com.hishop.wine.repository.service.WechatCodeService;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;


/**
 * 微信二维码表 业务逻辑实现类
 *
 * @author: LiGuoQiang
 * @date: 2023-06-20
 */

@Slf4j
@Service
public class WechatCodeBizImpl implements WechatCodeBiz {

    @Resource
    private WechatCodeService wechatCodeService;
    @Resource
    private NFS nfs;
    @Resource
    private WxMaService wxMaService;
    @Resource
    private WxAutoMappingConfig wxAutoMappingConfig;
    @Resource
    private BasicSettingBiz basicSettingBiz;

    // 文件服务器统一路径前缀，后续改成配置化
    private final static String PATH_PREFIX = "hishop/images/wechat/mini/";
    private final static int DEFAULT_WIDTH = 600;
    private final static String DEFAULT_ENV_VERSION = "trial";
    private final static String REDIRECT_PAGE = "pages/redirect/code";
    private final static String HOME_PAGE = "pages/index/index";

    @Override
    public void save(WechatSavePO savePo) {
        WechatCode wechatCode = new WechatCode();
        wechatCode.setCodeType(savePo.getCodeType());
        wechatCode.setCodeFrom(savePo.getCodeFrom());
        wechatCode.setCodeKey(savePo.getCodeKey());
        wechatCode.setCodeDesc(savePo.getCodeDesc());
        wechatCode.setCodeUrl(savePo.getCodeUrl());
        wechatCode.setPage(savePo.getPage());
        wechatCode.setIzDelete(false);
        wechatCodeService.save(wechatCode);
    }

    @Override
    public WechatCodeVO qryByKey(String codeKey) {
        WechatCode wechatCode = wechatCodeService.qryByKey(codeKey);
        return BeanUtil.copyProperties(wechatCode, WechatCodeVO.class);
    }

    @Override
    public String genMaCode(WechatCodeCreatePO createPo) {
        // 将来源转换为小写, 和模块编码对应上
        createPo.setCodeFrom(createPo.getCodeFrom().toLowerCase());
        // 设置默认页面
        createPo.setPage(ObjectUtil.defaultIfBlank(createPo.getPage(), HOME_PAGE));
        // 判断 page 是否是 / 开头 如果不是 加上 /
        if (!createPo.getPage().startsWith(StrUtil.SLASH)) {
            createPo.setPage(StrUtil.SLASH + createPo.getPage());
        }

        // 根据appId 或者来源 动态切换小程序配置
        WxAutoMapping wxAutoMapping = StrUtil.isNotEmpty(createPo.getAppId())
                ? wxAutoMappingConfig.getWxAutoMappingByAppId(createPo.getAppId())
                : wxAutoMappingConfig.getWxAutoMapping(createPo.getCodeFrom());
        Assert.isTrue(ObjectUtil.isNotNull(wxAutoMapping), "未找到对应的小程序配置");
        Assert.isTrue(wxMaService.switchover(wxAutoMapping.getAppId()), "切换小程序配置失败");
        if(StringUtils.isNotEmpty(createPo.getEnvVersion())) {
            wxAutoMapping.setEnvVersion(createPo.getEnvVersion());
        }
        // 生成唯一key
        String key = SecureUtil.md5(createPo.getCodeFrom() + createPo.getPage() + wxAutoMapping.getAppId());

        // 查询是否已经生成过小程序码
        String codePath = getIfExists(key, createPo);
        if (StrUtil.isNotBlank(codePath)) {
            return codePath;
        }

        // 生成小程序码
        try {
            WxMaQrcodeService qrcodeService = wxMaService.getQrcodeService();
            codePath = getNfsPath(key, createPo.getPage(), createPo.getBizPath());

            byte[] img = qrcodeService.createWxaCodeUnlimitBytes(key, REDIRECT_PAGE, Boolean.FALSE,
                    ObjectUtil.defaultIfBlank(wxAutoMapping.getEnvVersion(), DEFAULT_ENV_VERSION), getWidth(createPo.getWidth()),
                    Boolean.TRUE, null, ObjectUtil.defaultIfNull(createPo.getIzHyaline(), Boolean.FALSE));

            nfs.upload(codePath, img);
            WechatSavePO savePo = BeanUtil.copyProperties(createPo, WechatSavePO.class);
            savePo.setCodeKey(key);
            savePo.setCodeUrl(codePath);
            this.save(savePo);
            return codePath;
        } catch (WxErrorException e) {
            log.error("创建二维码并且上传失败。", e);
            throw new BusinessException("创建二维码并且上传失败");
        }
    }

    @Override
    @SwitchWxMini(value = "#createPo.codeFrom")
    public String genFeastMaCode(WechatCodeCreatePO createPo) {
        Long userId = LoginUserUtil.getLoginUser().getUserId();
        //createPo.setPage(URLUtil.encode(createPo.getPage() + "?inviterUserId=" + userId));
        // 将来源转换为小写, 和模块编码对应上
        createPo.setCodeFrom(createPo.getCodeFrom().toLowerCase());

        // 生成唯一key
        String key = SecureUtil.md5(createPo.getCodeFrom() + createPo.getPage() + userId + createPo.getAppId() + DateUtil.formatDate(new Date()));
        // 查询是否已经生成过小程序码
        String codePath = getIfExists(key, createPo);
        if (StrUtil.isNotBlank(codePath)) {
            return codePath;
        }

        // 生成小程序码
        try {
            WxMaQrcodeService qrcodeService = wxMaService.getQrcodeService();
            codePath = getNfsPath(key, "invite", createPo.getBizPath());

            byte[] img = qrcodeService.createWxaCodeUnlimitBytes("inviterUserId=" + userId, createPo.getPage(), Boolean.FALSE,
                    DEFAULT_ENV_VERSION, getWidth(createPo.getWidth()),
                    Boolean.TRUE, null, ObjectUtil.defaultIfNull(createPo.getIzHyaline(), Boolean.FALSE));

            nfs.upload(codePath, img);
            WechatSavePO savePo = BeanUtil.copyProperties(createPo, WechatSavePO.class);
            savePo.setCodeKey(key);
            savePo.setCodeUrl(codePath);
            this.save(savePo);
            return codePath;
        } catch (WxErrorException e) {
            log.error("创建二维码并且上传失败。", e);
            throw new BusinessException("创建二维码并且上传失败");
        }
    }

    /**
     * 获取小程序地址(带参数)
     *
     * @param scene 二维码唯一Key
     * @return 小程序地址
     */
    @Override
    public String getRealPage(String scene) {
        WechatCode code = wechatCodeService.getOne(new LambdaQueryWrapper<WechatCode>().eq(WechatCode::getCodeKey, scene).select(WechatCode::getPage));
        return ObjectUtil.isNull(code) ? HOME_PAGE : code.getPage();
    }


    /**
     * 先尝试从已存在的数据获取，因为key全局唯一，且只跟活动相关，所以可以从缓存获取，后续可以加上redis缓存
     *
     * <AUTHOR>
     * @date 2023/6/20
     */
    private String getIfExists(String key, WechatCodeCreatePO createPo) {
        WechatCodeVO wechatCode = this.qryByKey(key);
        if (wechatCode != null) {
            return wechatCode.getCodeUrl();
        }
        // 数据库不存在，尝试从nfs获取
        String filePath = getNfsPath(key, createPo.getPage(), createPo.getBizPath());
        if (nfs.exist(filePath)) {
            WechatSavePO savePo = BeanUtil.copyProperties(createPo, WechatSavePO.class);
            savePo.setCodeKey(key);
            savePo.setCodeUrl(filePath);
            return filePath;
        }
        return null;
    }

    private String getNfsPath(String key, String url, String bizPath) {
        String basePath = PATH_PREFIX + resolvePath(url) + "/";
        if (StrUtil.isNotBlank(bizPath)) {
            basePath += resolvePath(bizPath) + "/";
        }
        return basePath + key + ".png";
    }

    private String resolvePath(String url) {
        if (StrUtil.isBlank(url)) {
            return "";
        }
        if (url.startsWith("/")) {
            url = url.substring(1);
        }
        if (url.contains("?")) {
            url = url.substring(0, url.indexOf("?"));
        }
        if (url.endsWith("/")) {
            url = url.substring(0, url.length() - 1);
        }
        return url;
    }

    private int getWidth(Integer width) {
        if (width == null || width <= 0) {
            return DEFAULT_WIDTH;
        }
        return width;
    }

}
