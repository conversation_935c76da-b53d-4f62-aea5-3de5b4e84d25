package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.excel.read.ExcelReadHelper;
import com.hishop.common.excel.read.ReadResult;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.wine.biz.DealerBiz;
import com.hishop.wine.biz.excel.dealer.ImportAssist;
import com.hishop.wine.biz.excel.listener.DealerReadListener;
import com.hishop.wine.common.enums.FileImportStatus;
import com.hishop.wine.common.enums.FileImportType;
import com.hishop.wine.model.po.dealer.DealerImportPo;
import com.hishop.wine.model.po.dealer.DealerQueryPo;
import com.hishop.wine.model.po.dealer.DealerSavePo;
import com.hishop.wine.model.po.dealer.DealerUpdateStatusPo;
import com.hishop.wine.model.vo.dealer.DealerDetailVo;
import com.hishop.wine.model.vo.dealer.DealerVo;
import com.hishop.wine.model.vo.sale.SaleAreaSimpleVo;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.service.*;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import static com.hishop.wine.constants.Constant.DEFAULT_SALE_AREA_SPLIT;

/**
 * @description: 经销商业务实现类
 * @author: chenzw
 * @date: 2024/7/4 17:29
 */
@Service
@RequiredArgsConstructor
public class DealerBizImpl implements DealerBiz {

    private final DealerService dealerService;

    private final DealerAreaService dealerAreaService;

    private final SaleAreaService saleAreaService;

    private final UserService userService;

    private final FileImportRecordService fileImportRecordService;

    private final DistrictService districtService;

    private final ImportAssist importAssist;

    private final TerminateService terminateService;

    @Override
    public PageResult<DealerVo> pageList(DealerQueryPo pagePo) {
        if(pagePo.getSaleAreaId() != null) {
            SaleArea saleArea = saleAreaService.getById(pagePo.getSaleAreaId());
            if(saleArea != null) {
                pagePo.setSaleAreaCode(saleArea.getFullCode());
            }
        }
        Page<DealerVo> dealerVoPage = dealerService.queryDealerPageList(pagePo);

        if (CollectionUtils.isNotEmpty(dealerVoPage.getRecords())) {
            //组装销售区域信息
            dealerVoPage.getRecords().forEach(dealerVo -> dealerVo.setSaleAreaList(this.getSaleAreaList(dealerVo.getId())));
        }
        return PageResultHelper.transfer(dealerVoPage, DealerVo.class);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveDealer(DealerSavePo savePo) {
        checkDealerRequestData(savePo);
        //判断功能新增还是更新
        if (savePo.getId() != null) {
            Dealer dealer = dealerService.getById(savePo.getId());
            Assert.notNull(dealer, "经销商不存在");
            //更新需要先删除原有销售区域数据
            dealerAreaService.remove(new LambdaQueryWrapper<DealerArea>().eq(DealerArea::getDealerId, savePo.getId()));
        }
        Dealer dealer = BeanUtil.copyProperties(savePo, Dealer.class);

        dealer.setProvinceName(districtService.getById(savePo.getProvinceId()).getName());
        dealer.setCityName(districtService.getById(savePo.getCityId()).getName());
        if (savePo.getDistrictId() != null) {
            dealer.setDistrictName(districtService.getById(savePo.getDistrictId()).getName());
        }
        dealer.setIzEnable(Boolean.TRUE);
        //保存经销商信息
        dealerService.saveOrUpdate(dealer);
        if (CollectionUtils.isNotEmpty(savePo.getSaleAreaList())) {
            List<DealerArea> dealerAreaList = savePo.getSaleAreaList().stream().map(saleAreaId -> {
                DealerArea dealerArea = new DealerArea();
                dealerArea.setDealerId(dealer.getId());
                dealerArea.setSaleAreaId(saleAreaId);
                return dealerArea;
            }).collect(Collectors.toList());
            dealerAreaService.saveBatch(dealerAreaList);
        }
    }

    /**
     * 校验经销商请求数据
     *
     * @param savePo 请求参数
     */
    private void checkDealerRequestData(DealerSavePo savePo) {
        //校验参数
        savePo.validate();
        //校验经销商编码和名称是否重复
        LambdaQueryWrapper<Dealer> queryWrapper = new LambdaQueryWrapper<>(Dealer.class)
                .eq(Dealer::getIzDelete, Boolean.FALSE)
                .eq(Dealer::getDealerCode, savePo.getDealerCode())
                .ne(savePo.getId() != null, Dealer::getId, savePo.getId());

        long count = dealerService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("经销商编码已存在");
        }

        queryWrapper = new LambdaQueryWrapper<>(Dealer.class)
                .eq(Dealer::getIzDelete, Boolean.FALSE)
                .eq(Dealer::getDealerName, savePo.getDealerName())
                .ne(savePo.getId() != null, Dealer::getId, savePo.getId());

        count = dealerService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("经销商名称已存在");
        }

        queryWrapper = new LambdaQueryWrapper<>(Dealer.class)
                .eq(Dealer::getIzDelete, Boolean.FALSE)
                .eq(Dealer::getPhone, savePo.getPhone())
                .ne(savePo.getId() != null, Dealer::getId, savePo.getId());

        count = dealerService.count(queryWrapper);
        if (count > 0) {
            throw new BusinessException("负责人手机号已存在");
        }

        //校验销售区域
        if (CollectionUtils.isNotEmpty(savePo.getSaleAreaList())) {
            this.checkSaleArea(savePo.getSaleAreaList());
        }
    }

    private void checkSaleArea(List<Long> saleAreaIds) {
        List<SaleArea> saleAreaList = saleAreaService.list(new LambdaQueryWrapper<SaleArea>().eq(SaleArea::getIzDelete, Boolean.FALSE)
                .in(SaleArea::getId, saleAreaIds));
        for (SaleArea saleArea : saleAreaList) {
            for (SaleArea area : saleAreaList) {
                if (area.getId().equals(saleArea.getId())) {
                    continue;
                }
                String[] codeList = saleArea.getFullCode().split("[" + DEFAULT_SALE_AREA_SPLIT + "]");
                String[] codes = area.getFullCode().split("[" + DEFAULT_SALE_AREA_SPLIT + "]");
                Set<String> distinctList = CollectionUtil.unionDistinct(Arrays.asList(codeList), Arrays.asList(codes));
                //如果销售区域的全路径编码长度小于等于2个销售区域的全路径编码长度，说明存在重叠
                if (distinctList.size() <= Math.max(codeList.length, codes.length)) {
                    throw new BusinessException("当前销售区域" + saleArea.getFullName().replace("|", ",") + "与" + area.getFullName().replace("|", ",") + "存在重叠，请重新选择");
                }
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchDelete(List<Long> ids) {
        long count = terminateService.count(new LambdaQueryWrapper<Terminate>()
                .in(Terminate::getDealerId, ids)
                .eq(Terminate::getIzDelete, Boolean.FALSE));

        if (count > 0) {
            throw new BusinessException("当前选择的经销商下存在门店，不能删除");
        }
        //删除经销商信息
        dealerService.removeBatchByIds(ids);
        dealerAreaService.remove(new LambdaQueryWrapper<DealerArea>().in(DealerArea::getDealerId, ids));
    }

    @Override
    public DealerDetailVo detail(Long id) {
        Dealer dealer = dealerService.getById(id);
        Assert.notNull(dealer, "经销商不存在");

        DealerDetailVo dealerDetailVo = BeanUtil.copyProperties(dealer, DealerDetailVo.class);

        if (dealer.getBusinessUserId() != null) {
            User user = userService.getById(dealer.getBusinessUserId());
            if (user != null && !user.getIzDelete()) {
                dealerDetailVo.setBusinessUserName(user.getRealName());
            }
        }
        dealerDetailVo.setSaleAreaList(this.getSaleAreaList(id));
        return dealerDetailVo;
    }

    /**
     * 获取销售区域信息列表
     *
     * @param id 主键
     * @return list
     */
    private List<List<SaleAreaSimpleVo>> getSaleAreaList(Long id) {
        //查询销售区域信息
        List<DealerArea> dealerAreaList = dealerAreaService.list(new LambdaQueryWrapper<DealerArea>()
                .eq(DealerArea::getIzDelete, Boolean.FALSE)
                .eq(DealerArea::getDealerId, id));

        List<List<SaleAreaSimpleVo>> allAreaList = Lists.newArrayList();
        if (CollectionUtils.isNotEmpty(dealerAreaList)) {
            List<SaleArea> saleAreaList = saleAreaService.list(new LambdaQueryWrapper<SaleArea>().eq(SaleArea::getIzDelete, Boolean.FALSE)
                    .in(SaleArea::getId, dealerAreaList.stream().map(DealerArea::getSaleAreaId).distinct().collect(Collectors.toList())));
            for (SaleArea saleArea : saleAreaList) {
                List<SaleAreaSimpleVo> areaList = Lists.newArrayList();

                String[] codes = saleArea.getFullCode().split("[" + DEFAULT_SALE_AREA_SPLIT + "]");
                saleAreaService.list(new LambdaQueryWrapper<SaleArea>().eq(SaleArea::getIzDelete, Boolean.FALSE)
                        .in(SaleArea::getCode, Arrays.asList(codes))).forEach(area -> {
                    SaleAreaSimpleVo saleAreaSimpleVo = new SaleAreaSimpleVo();
                    saleAreaSimpleVo.setId(area.getId());
                    saleAreaSimpleVo.setName(area.getName());
                    areaList.add(saleAreaSimpleVo);
                });
                allAreaList.add(areaList);
            }
        }
        return allAreaList;
    }

    @Override
    public void batchUpdateStatus(DealerUpdateStatusPo dealerUpdateStatusPo) {
        List<Dealer> dealerList = dealerUpdateStatusPo.getIds().stream().map(id -> {
            Dealer dealer = new Dealer();
            dealer.setId(id);
            dealer.setIzEnable(dealerUpdateStatusPo.getIzEnable());
            return dealer;
        }).collect(Collectors.toList());
        dealerService.updateBatchById(dealerList);
    }

    @Override
    public void importDealer(MultipartFile file) {
        ReadResult<DealerImportPo> importResult = ExcelReadHelper.read(file, DealerImportPo.class, new DealerReadListener(),2);
        if (CollectionUtil.isEmpty(importResult.getDataList())) {
            throw new BusinessException("模板导入数据为空，请检查导入文件");
        }

        FileImportRecord fileImportRecord = new FileImportRecord();
        fileImportRecord.setFileName(file.getOriginalFilename());
        fileImportRecord.setImportType(FileImportType.DEALER_IMPORT.getType());
        fileImportRecord.setImportStatus(FileImportStatus.PROCESSING.getType());
        fileImportRecordService.save(fileImportRecord);

        importAssist.importData(fileImportRecord, FileImportType.DEALER_IMPORT, null, importResult);
    }

    @Override
    public List<DealerDetailVo> queryListByCode(List<String> codeList) {
        return dealerService.list(new LambdaQueryWrapper<Dealer>()
                        .eq(Dealer::getIzEnable, Boolean.TRUE)
                        .eq(Dealer::getIzDelete, Boolean.FALSE)
                        .in(Dealer::getDealerCode, codeList))
                .stream().map(dealer -> BeanUtil.copyProperties(dealer, DealerDetailVo.class))
                .collect(Collectors.toList());
    }

}
