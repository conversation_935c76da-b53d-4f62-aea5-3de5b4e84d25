package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.hishop.common.constants.CommonConstants;
import com.hishop.common.enums.IdentityTypeEnums;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.login.LoginIdentity;
import com.hishop.common.pojo.login.LoginResult;
import com.hishop.common.pojo.login.LoginUser;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.*;
import com.hishop.email.EmailMessage;
import com.hishop.email.EmailSender;
import com.hishop.mq.api.MQ;
import com.hishop.wine.assist.UserAssist;
import com.hishop.wine.biz.*;
import com.hishop.wine.common.enums.ResourceTypeEnums;
import com.hishop.wine.common.helper.AuthModuleHelper;
import com.hishop.wine.common.utils.VerificationCodeUtil;
import com.hishop.wine.constants.BasicCacheConstants;
import com.hishop.wine.constants.BasicConstants;
import com.hishop.wine.constants.WechatConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.enums.UserEnum;
import com.hishop.wine.enums.points.MemberPointsEnum;
import com.hishop.wine.enums.points.PointsMallBizType;
import com.hishop.wine.model.dto.BizUserCodeByPhoneDto;
import com.hishop.wine.model.po.basic.*;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.user.CheckRolePO;
import com.hishop.wine.model.po.user.PullNewSummaryPO;
import com.hishop.wine.model.po.user.UserCreatePO;
import com.hishop.wine.model.vo.basic.PcAccountInfoVO;
import com.hishop.wine.model.vo.basic.UserResourceVO;
import com.hishop.wine.model.vo.basic.UserVO;
import com.hishop.wine.model.vo.login.AuthModuleVO;
import com.hishop.wine.model.vo.login.PcLoginUserVO;
import com.hishop.wine.model.vo.login.PcLoginVO;
import com.hishop.wine.model.vo.module.ModuleVO;
import com.hishop.wine.model.vo.setting.PointsSettingVO;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import com.hishop.wine.model.vo.user.PullNewSummaryVO;
import com.hishop.wine.repository.dto.PullNewSummaryDTO;
import com.hishop.wine.repository.entity.*;
import com.hishop.wine.repository.param.PullNewSummaryParam;
import com.hishop.wine.repository.param.UserCombineQryParam;
import com.hishop.wine.repository.service.*;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


/**
 * 用户表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Slf4j
@Service
@AllArgsConstructor
public class UserBizImpl implements UserBiz {

    private final UserService userService;
    private final IdentityService identityService;
    private final RoleBiz roleBiz;
    private final EmailSender emailSender;
    private final WechatWebUserBiz wechatWebUserBiz;
    private final SmsBiz smsBiz;
    private final RoleService roleService;
    private final BasicSettingBiz basicSettingBiz;
    private final AuthModuleHelper authModuleHelper;
    private final ResourceService resourceService;
    private final ModuleBiz moduleBiz;
    private final UserAssist userAssist;
    private final MemberPointsBiz memberPointsBiz;
    private final RankService rankService;
    private final TagsService tagsService;
    private final UserTagsService userTagsService;
    private final MQ mq;

    /**
     * 获取用户身份信息
     *
     * @param username     用户名
     * @param identityType 身份类型
     * @return 用户信息(包含是否存在当前身份)
     */
    @Override
    public User getUserIdentityByUsername(String username, IdentityTypeEnums identityType) {
        // 根据账号取匹配用户
        User user = userService.getUserByUsername(username);

        if (ObjectUtil.isNotNull(user)) {
            // 去身份表匹配身份
            Identity identity = identityService.getIdentity(user.getId(), identityType.getType(), HeaderUtil.getModuleCode());
            user.setIdentity(identity);
        }
        return user;
    }

    /**
     * 获取用户身份信息
     *
     * @param mobile       手机号
     * @param identityType 身份类型
     * @return 用户信息(包含是否存在当前身份)
     */
    @Override
    public User getUserIdentityByMobile(String mobile, IdentityTypeEnums identityType) {
        // 根据账号取匹配用户
        User user = userService.getUserByMobile(mobile);

        if (ObjectUtil.isNotNull(user)) {
            // 去身份表匹配身份
            Identity identity = identityService.getIdentity(user.getId(), identityType.getType(), HeaderUtil.getModuleCode());
            user.setIdentity(identity);
        }
        return user;
    }

    /**
     * PC端 用户登录
     *
     * @param account      账号(手机号/邮箱/用户名)
     * @param password     密码
     * @param identityType 登录身份类型
     * @param checkPwd     是否检查密码
     * @param rememberMe   是否记住我
     * @return 登录结果
     */
    @Override
    public PcLoginVO pcLogin(String account, String password, Integer identityType, Boolean checkPwd, Boolean rememberMe) {
        User user = userAssist.checkAndGetUserByAccount(account, identityType);

        // 如果需要检查密码
        if (ObjectUtil.isNull(checkPwd) || checkPwd) {
            // 校验密码
            userAssist.checkPassword(password, user.getPassword());
        }

        // 查询当前登录身份
        Identity identity = user.getIdentity();

        // 计算登录过期时间
        Long expireTime = ObjectUtil.isNotNull(rememberMe) && rememberMe ? BasicConstants.REMEMBER_ME_EXPIRE_TIME : BasicConstants.EXPIRE_TIME;
        // 登录
        LoginResult loginResult = SessionUtil.login(buildLoginUser(user, identity, null), expireTime);
        PcLoginVO pcLoginVO = BeanUtil.copyProperties(loginResult, PcLoginVO.class);
        PcLoginUserVO userInfo = BeanUtil.copyProperties(user, PcLoginUserVO.class);
        userInfo.setIdentityType(identityType);
        userInfo.setIzSupAdmin(userAssist.checkSuperAdmin(identity.getRoleId()));
        pcLoginVO.setUserInfo(userInfo);

        // 查询默认登录页面
        if (ObjectUtil.isNotNull(identity.getRoleId())) {
            Role role = roleService.getById(identity.getRoleId());
            pcLoginVO.setDefaultUrl(ObjectUtil.isNotNull(role) ? role.getDefaultUrl() : StrUtil.EMPTY);
        }
        // 如果是超级管理员 设置默认的登录页面
        if (userInfo.getIzSupAdmin()) {
            pcLoginVO.setDefaultUrl(BasicConstants.DEFAULT_LOGIN_URL);
        }

        // 调授权平台获取授权列表
        AuthModuleVO authModuleVO = authModuleHelper.getAuthModule();
        pcLoginVO.setAuthModule(authModuleVO);
        return pcLoginVO;
    }

    /**
     * PC端 微信扫码登录
     *
     * @param code         微信扫描二维码
     * @param state        校验参数
     * @param identityType 登录身份
     * @return 登录结果
     */
    @Override
    public PcLoginVO pcWxScanLogin(String code, String state, Integer identityType) {
        String redisKey = String.format(WechatConstants.WECHAT_WEB_QR_CODE_STATE, state);
        Assert.isTrue(ObjectUtil.isNotNull(RedisUtil.get(redisKey)), "二维码已失效, 请刷新页面后重试");

        // 获取微信用户信息
        wechatWebUserBiz.checkState(state);
        WechatWebUser wxUserInfo = wechatWebUserBiz.getWechatWebUser(code);
        Assert.isTrue(ObjectUtil.isNotNull(wxUserInfo), "暂未绑定微信账号");

        User user = userService.getById(wxUserInfo.getUserId());

        PcLoginVO loginResult = pcLogin(user.getUsername(), null, identityType, Boolean.FALSE, Boolean.FALSE);

        // 删除凭据
        RedisUtil.del(String.format(WechatConstants.WECHAT_WEB_QR_CODE_STATE, state));
        return loginResult;
    }

    /**
     * 用户登出
     * (小程序和pc端可共用)
     */
    @Override
    public void logout() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        SessionUtil.logout(loginUser.getUserId(), loginUser.getIdentity().getIdentityId());
    }

    /**
     * PC端 刷新token
     *
     * @param refreshTokenParam 刷新token参数
     * @return 登录信息
     */
    @Override
    public PcLoginVO pcRefreshToken(UserRefreshTokenPO refreshTokenParam) {
        LoginUser loginUser = LoginUserUtil.parseLoginUser(refreshTokenParam.getRefreshToken(), Boolean.TRUE);
        Integer identityType = ObjectUtil.isNotNull(refreshTokenParam.getIdentityType())
                ? refreshTokenParam.getIdentityType() : loginUser.getIdentity().getIdentityType();

        User user = userAssist.checkAndGetUser(loginUser.getUserId());

        return pcLogin(user.getMobile(), null, identityType, Boolean.FALSE, Boolean.FALSE);
    }

    /**
     * 构建登录用户信息
     *
     * @param user     用户信息
     * @param identity 身份信息
     * @param miniUser 小程序用户信息
     * @return 登录用户信息
     */
    @Override
    public LoginUser buildLoginUser(User user, Identity identity, MiniUser miniUser) {
        // 创建登录用户信息
        LoginUser loginUser = BeanUtil.copyProperties(user, LoginUser.class);
        loginUser.setUserId(user.getId());

        // 创建身份信息
        LoginIdentity loginIdentity = BeanUtil.copyProperties(identity, LoginIdentity.class);
        loginIdentity.setIdentityId(identity.getId());
        loginUser.setIdentity(loginIdentity);

        // 小程序用户相关信息
        if (ObjectUtil.isNotNull(miniUser)) {
            loginUser.setMiniUserId(miniUser.getId());
            loginUser.setSessionKey(miniUser.getSessionKey());
        }
        return loginUser;
    }

    /**
     * 根据用户id列表查询用户信息 如果identityType为空, 返回该手机号的手机注册时间
     *
     * @param userIds      用户id列表
     * @param identityType 身份类型
     * @return 用户信息列表
     */
    @Override
    public List<UserVO> listUserByIds(List<Long> userIds, Integer identityType) {
        if (CollectionUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<User> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(User::getId, userIds);
        List<User> userList = userService.list(queryWrapper);
        List<UserVO> userVoList = BeanUtil.copyToList(userList, UserVO.class);

        // 查询头衔状态 仅返回启用状态的头衔
        List<Long> rankIds = userList.stream().filter(item -> ObjectUtil.isNotNull(item.getRankId())).map(item -> item.getRankId()).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(rankIds)) {
            Map<Long, Rank> rankMap = rankService.list(new LambdaQueryWrapper<Rank>()
                    .in(Rank::getId, rankIds)).stream().collect(Collectors.toMap(Rank::getId, item -> item));
            userVoList.forEach(user -> {
                if (ObjectUtil.isNotNull(user.getRankId())) {
                    Rank rank = rankMap.get(user.getRankId());
                    if (ObjectUtil.isNotNull(rank)) {
                        user.setRankId(rank.getId());
                        user.setRankStatus(rank.getStatus() && !rank.getIzDelete());
                    }
                }
            });
        }
        // TODO 根据身份类型获取注册时间
        return userVoList;
    }

    @Override
    public UserVO userById(Long userId) {
        return BeanUtil.copyProperties(userService.getById(userId), UserVO.class);
    }

    @Override
    public List<Long> qryUserId(UserCombineQryPO qryPo) {
        UserCombineQryParam param = BeanUtil.copyProperties(qryPo, UserCombineQryParam.class);
        return userService.qryUserId(param);
    }

    /**
     * 发送验证邮箱
     *
     * @param email 邮箱
     */
    @Override
    public void sendBindEmail(String email) {
        checkEmailBindStatus(email);

        String code = RandomUtil.randomNumbers(4);
        String redisKey = String.format(BasicCacheConstants.PC_BIND_EMAIL_KEY, email);

        log.info("======> 发送绑定邮箱验证码, 邮箱: {}, 验证码: {}", email, code);
        VerificationCodeUtil.setCode(redisKey, code);
        SystemSettingVO systemSetting = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
        String systemName = systemSetting.getSystemName();
        EmailMessage emailMessage = new EmailMessage(email, String.format("%s验证邮件", systemName),
                String.format("您的验证码为：%s，五分钟内有效，请马上进行验证。若非本人操作，请忽略此邮件", code));
        emailMessage.setNickName(systemName);
        emailSender.send(emailMessage);
    }

    /**
     * 绑定邮箱
     *
     * @param emailBindPO 绑定邮箱参数
     */
    @Override
    public void bindEmail(EmailBindPO emailBindPO) {
        String redisKey = String.format(BasicCacheConstants.PC_BIND_EMAIL_KEY, emailBindPO.getEmail());
        VerificationCodeUtil.checkCode(redisKey, emailBindPO.getCode());

        LoginUser loginUser = LoginUserUtil.getLoginUser();
        User user = userAssist.checkAndGetUser(loginUser.getUserId());

        // 如果已经绑定了邮箱, 则需要传入登录密码
        if (StrUtil.isNotEmpty(user.getEmail())) {
            userAssist.checkPassword(emailBindPO.getPassword(), user.getPassword());
        }

        // 检测邮箱是否已经被绑定
        checkEmailBindStatus(emailBindPO.getEmail());

        // 更新用户邮箱
        User updUser = new User();
        updUser.setId(loginUser.getUserId());
        updUser.setEmail(emailBindPO.getEmail());
        userService.updateById(updUser);

        VerificationCodeUtil.removeCode(redisKey);
    }

    /**
     * 解绑邮箱
     */
    @Override
    public void unBindEmail() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        User updUser = new User();
        updUser.setId(loginUser.getUserId());
        updUser.setEmail(StrUtil.EMPTY);
        userService.updateById(updUser);
    }

    /**
     * 发送绑定手机号验证码
     *
     * @param mobile 手机号
     */
    @Override
    public void sendBindMobile(String mobile) {
        checkMobileBindStatus(mobile);
        smsBiz.sendSmsCode(mobile, BasicCacheConstants.PC_BIND_MOBILE_KEY, "绑定手机号");
    }

    /**
     * 绑定手机号
     *
     * @param mobileBindPO 绑定手机号参数
     */
    @Override
    public void bindMobile(MobileBindPO mobileBindPO) {
        smsBiz.checkSmsCode(mobileBindPO.getMobile(), BasicCacheConstants.PC_BIND_MOBILE_KEY, mobileBindPO.getCode());

        LoginUser loginUser = LoginUserUtil.getLoginUser();
        User user = userAssist.checkAndGetUser(loginUser.getUserId());

        // 如果已经绑定了手机号, 则需要传入登录密码
        if (StrUtil.isNotEmpty(user.getMobile())) {
            userAssist.checkPassword(mobileBindPO.getPassword(), user.getPassword());
        }

        // 判断手机号是否已经被绑定
        checkMobileBindStatus(mobileBindPO.getMobile());

        // 更新用户手机号
        User updUser = new User();
        updUser.setId(loginUser.getUserId());
        updUser.setMobile(mobileBindPO.getMobile());
        userService.updateById(updUser);

        smsBiz.removeSmsCode(mobileBindPO.getMobile(), BasicCacheConstants.PC_BIND_MOBILE_KEY);
    }

    /**
     * 解绑手机号
     */
    @Override
    public void unBindMobile() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        User updUser = new User();
        updUser.setId(loginUser.getUserId());
        updUser.setMobile(StrUtil.EMPTY);
        userService.updateById(updUser);
    }

    /**
     * 获取PC端账号信息
     *
     * @return 账号信息
     */
    @Override
    public PcAccountInfoVO getPcAccountInfo() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        LoginIdentity loginIdentity = loginUser.getIdentity();

        User user = userAssist.checkAndGetUser(loginUser.getUserId());

        PcAccountInfoVO accountInfo = BeanUtil.copyProperties(user, PcAccountInfoVO.class);
        accountInfo.setEditRoleAble(Boolean.FALSE);

        Identity identity = userAssist.checkAndGetIdentity(loginIdentity.getIdentityId());

        if (ObjectUtil.isNotNull(identity.getRoleId())) {
            accountInfo.setRoleId(identity.getRoleId());
            Role role = roleBiz.checkAndGetRole(identity.getRoleId(), Boolean.FALSE);
            accountInfo.setRoleName(role.getName());

            Boolean flag = roleBiz.checkResourceAuth(identity.getRoleId(), BasicConstants.ADMIN_MANAGER_RESOURCE_ID);
            accountInfo.setEditRoleAble(flag);
        }

        // 查询微信网页应用用户信息
        WechatWebUser wechatWebUser = wechatWebUserBiz.getWechatWebUser(user.getId());
        accountInfo.setNickName(ObjectUtil.isNotNull(wechatWebUser) ? wechatWebUser.getNickName() : StrUtil.EMPTY);
        return accountInfo;
    }

    /**
     * 更新账号信息
     *
     * @param updateAccountPO 更新账号信息参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAccountInfo(PcAccountUpdatePO updateAccountPO) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        LoginIdentity loginIdentity = loginUser.getIdentity();

        Identity identity = userAssist.checkAndGetIdentity(loginIdentity.getIdentityId());

        // 如果角色信息有变更, 更新角色
        if (!updateAccountPO.getRoleId().equals(identity.getRoleId())) {
            Boolean flag = userAssist.checkResourceAuth(identity.getRoleId(), BasicConstants.ADMIN_MANAGER_RESOURCE_ID);
            Assert.isTrue(flag, "没有权限修改角色信息");

            Identity updIdentity = new Identity();
            updIdentity.setId(loginIdentity.getIdentityId());
            updIdentity.setRoleId(updateAccountPO.getRoleId());
            identityService.updateById(updIdentity);
        }

        // 更新用户信息
        User updUser = new User();
        updUser.setId(loginUser.getUserId());
        updUser.setRealName(updateAccountPO.getRealName());
        updUser.setIcon(updateAccountPO.getIcon());
        userService.updateById(updUser);

    }

    /**
     * 修改密码发送短信验证码
     */
    @Override
    public void sendUpdPwdSms() {
        LoginUser loginUser = LoginUserUtil.getLoginUser();

        User user = userAssist.checkAndGetUser(loginUser.getUserId());
        String mobile = user.getMobile();
        Assert.isTrue(StrUtil.isNotEmpty(mobile), "请先绑定手机号");

        smsBiz.sendSmsCode(user.getMobile(), BasicCacheConstants.PC_UPDATE_PASSWORD_MOBILE_KEY, "修改密码");
    }

    /**
     * 修改密码
     * <p>
     * 1.如果不存在旧密码, 则为首次绑定直接修改
     * 2.存在旧密码
     * 2.1.如果绑定了手机号, 则使用验证码修改密码
     * 2.2.未绑定手机号, 则是通过旧密码修改密码
     *
     * @param updatePasswordPO 修改密码参数
     */
    @Override
    public void updatePassword(PasswordUpdatePO updatePasswordPO) {
        LoginUser loginUser = LoginUserUtil.getLoginUser();
        User user = userAssist.checkAndGetUser(loginUser.getUserId());
        Assert.isTrue(StrUtil.equals(updatePasswordPO.getPassword(), updatePasswordPO.getPasswordAgain()), "两次密码不一致");

        // 没有旧密码直接修改
        if (StrUtil.isEmpty(user.getPassword())) {
            checkAndUpdatePassword(user, updatePasswordPO.getPassword());
            return;
        }

        if (!StrUtil.isEmpty(user.getMobile())) {
            updatePasswordBySmsCode(updatePasswordPO, user);
        } else {
            updatePasswordByOldPassword(updatePasswordPO, user);
        }
    }

    /**
     * 发送忘记密码验证码
     *
     * @param mobile       手机号
     * @param identityType 身份类型
     */
    @Override
    public void sendForForgetPwd(String mobile, Integer identityType) {
        String key = BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_NUM_KEY + mobile;
        Integer num = RedisUtil.get(key);

        if (num == null) {
            // 第一次尝试，设置初始值和过期时间
            RedisUtil.set(key, 1, 30 * 60);
            num = 1;
        } else {
            // 后续尝试，先检查是否超过限制
            if (num >= 5) {
                log.error("30分钟内不可超过5次，当前次数: {}", num);
                throw new BusinessException("30分钟内不可超过5次");
            }
            // 更新计数，保持原有过期时间
            RedisUtil.set(key, num + 1, 30 * 60);
        }
        User user = userAssist.checkAndGetUserByMobile(mobile, ObjectUtil.defaultIfNull(identityType, IdentityTypeEnums.ADMIN.getType()));

        smsBiz.sendSmsCode(user.getMobile(), BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_KEY, "重置密码");
    }

    /**
     * 忘记密码
     *
     * @param passwordForgetPO 忘记密码参数
     */
    @Override
    public void forgetPassword(PasswordForgetPO passwordForgetPO) {
        User user = userAssist.checkAndGetUserByMobile(passwordForgetPO.getMobile(),
                ObjectUtil.defaultIfNull(passwordForgetPO.getIdentityType(), IdentityTypeEnums.ADMIN.getType()));

        // 校验验证码
        smsBiz.checkSmsCode(user.getMobile(), BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_KEY, passwordForgetPO.getCode());

        // 更新密码
        checkAndUpdatePassword(user, passwordForgetPO.getPassword());

        // 清除验证码
        smsBiz.removeSmsCode(user.getMobile(), BasicCacheConstants.PC_FORGET_PASSWORD_MOBILE_KEY);
    }

    /**
     * 查询用户菜单权限
     *
     * @return 菜单权限
     */
    @Override
    public UserResourceVO menuAuth() {
        // 查询出已经授权的模块
        List<ModuleVO> moduleList = moduleBiz.listAuthModuleIncludeBasic();

        // 查询当前身份有什么角色
        Identity identity = identityService.getById(LoginUserUtil.getLoginUser().getIdentity().getIdentityId());
        // 如果身份已经被删除, 提出登录
        if (ObjectUtil.isNull(identity) || !identity.getStatus() || identity.getIzDelete()) {
            throw new BusinessException(ResponseEnum.UNAUTHORIZED);
        }
        Long roleId = identity.getRoleId();
        Assert.isTrue(ObjectUtil.isNotNull(roleId), "该身份未授权任何角色权限");

        // 角色信息
        Role role = roleBiz.checkAndGetRole(roleId, Boolean.FALSE);

        UserResourceVO userResourceVO = new UserResourceVO();
        // 设置默认登录页面 从角色中获取
        userResourceVO.setDefaultPagePath(role.getDefaultUrl());

        // 构建菜单权限
        List<UserResourceVO.MenuListVO> menuList = buildMenuList(moduleList, roleId);
        userResourceVO.setMenuList(menuList);

        // 设置授权模块（筛选出下面有菜单权限的模块）
        userResourceVO.setProductList(buildProductList(moduleList, menuList));
        return userResourceVO;
    }

    /**
     * 执行注册成功处理
     *
     * @param identityId 身份id
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void registerSuccessHandel(Long identityId) {
        Identity identity = identityService.getById(identityId);
        Assert.isTrue(ObjectUtil.isNotNull(identity), "身份不存在");
        if (identity.getRegisterHandle()) {
            log.info("=======> 身份id: {} 已经处理过注册成功逻辑, 无需重复处理", identityId);
            return;
        }

        // 执行具体的注册成功逻辑
        registerGivePoints(identity);

        // 修改处理状态
        Identity updIdentity = new Identity();
        updIdentity.setId(identityId);
        updIdentity.setRegisterHandle(Boolean.TRUE);
        identityService.updateById(updIdentity);

        // 获取注册回调地址
        String callBackTopic = UserEnum.RegisterChannel.getCallBackTopic(identity.getRegisterChannel());
        if (StrUtil.isNotEmpty(callBackTopic)) {
            mq.publish(callBackTopic, identity.getRegisterBizCode());
        }
        log.info("=======> 身份id: {} 注册成功处理完成", identityId);
    }

    /**
     * 检测是否存在角色
     *
     * @param checkRolePO 查询参数
     * @return true-存在 false-没有
     */
    @Override
    public Boolean checkRole(CheckRolePO checkRolePO) {
        return userAssist.checkRole(checkRolePO.getUserId(), checkRolePO.getIdentityId(), checkRolePO.getRoleId());
    }

    /**
     * 获取拉新数据
     *
     * @param pullNewSummaryPO 查询拉新参数
     * @return 拉新数据
     */
    @Override
    public List<PullNewSummaryVO> getPullNewSummary(PullNewSummaryPO pullNewSummaryPO) {
        List<PullNewSummaryDTO> dbList = identityService.getPullNewSummary(BeanUtil.copyProperties(pullNewSummaryPO, PullNewSummaryParam.class));
        return BeanUtil.copyToList(dbList, PullNewSummaryVO.class);
    }

    /**
     * 用户打标
     *
     * @param userId 用户id
     * @param tagIds 标签id列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void markTags(Long userId, List<Long> tagIds) {
        // 获取用户已经存在的标签
        List<Long> existUserIds = userTagsService.getTagIdsByUserId(userId);
        tagIds.removeAll(existUserIds);
        tagIds = tagIds.stream().distinct().collect(Collectors.toList());

        // 获取标签名称
        Map<Long, String> tagMap = CollectionUtil.isEmpty(tagIds) ? new HashMap<>()
                : tagsService.list(Wrappers.lambdaQuery(Tags.class).in(Tags::getId, tagIds))
                .stream().collect(Collectors.toMap(Tags::getId, Tags::getTagName));

        // 批量保存标签
        List<UserTags> userTagList = tagIds.stream().filter(tagId -> StrUtil.isNotEmpty(tagMap.get(tagId))).map(tagId -> {
            UserTags userTags = new UserTags();
            userTags.setUserId(userId);
            userTags.setTagId(tagId);
            userTags.setTagName(tagMap.get(tagId));
            return userTags;
        }).collect(Collectors.toList());
        if (CollectionUtil.isNotEmpty(userTagList)) {
            userTagsService.saveBatch(userTagList);
        }
    }

    @Override
    public void updateBizUserCodeByPhone(BizUserCodeByPhoneDto dto) {
        User user = new User();
        user.setBizUserCode(dto.getBizUserCode());
        user.setBizUserName(dto.getBizUserName());
        user.setBizPhone(dto.getBizPhone());
        userService.update(user, Wrappers.lambdaUpdate(User.class).eq(User::getMobile, dto.getPhone()));
    }

    @Override
    public UserVO createUser(UserCreatePO userCreatePO) {
        User user = userService.getUserByMobile(userCreatePO.getUserPhone());
        UserVO userVO = null;
        if(user == null) {
            user = new User();
            user.setBizPhone(userCreatePO.getBizPhone());
            user.setBizUserName(userCreatePO.getBizUserName());
            user.setBizUserCode(userCreatePO.getBizUserCode());
            user.setMobile(userCreatePO.getUserPhone());
            user.setNickName(userCreatePO.getUserName());
            user.setRealName(userCreatePO.getUserName());
            user.setUsername("dr" + userCreatePO.getUserPhone());
            userService.save(user);
            userVO = BeanUtil.copyProperties(user, UserVO.class);
        } else {
            userVO = BeanUtil.copyProperties(user, UserVO.class);
        }
        return userVO;
    }

    /**
     * 注册送积分
     *
     * @param identity 身份id
     */
    private void registerGivePoints(Identity identity) {
        PointsSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.POINTS_SETTING);
        // 如果没有设置积分规则, 则不送积分, 只需初始化积分主表数据即可
        if (setting.getNewRegisterPoints() > 0) {
            log.info("=======> 身份id: {} 注册成功, 送积分: {}", identity.getId(), setting.getNewRegisterPoints());
            // 执行送积分逻辑
            ChangePointsPO changePointsPO = new ChangePointsPO();
            changePointsPO.setUserId(identity.getUserId());
            changePointsPO.setIdentityType(identity.getIdentityType());
            changePointsPO.setModifiedType(MemberPointsEnum.ModifiedType.INCREASE.getCode());
            changePointsPO.setModifiedPoints(setting.getNewRegisterPoints());
            changePointsPO.setModuleCode(identity.getModuleCode());
            changePointsPO.setBizType(PointsMallBizType.NEW_MEMBER.getCode());
            changePointsPO.setBizCode(String.valueOf(identity.getId()));
            changePointsPO.setTraceNo(IdUtil.fastSimpleUUID());
            changePointsPO.setCreateBy(0L);
            changePointsPO.setModifiedRemark("新用户注册送积分");
            memberPointsBiz.change(changePointsPO);
        } else {
            log.info("=======> 身份id: {} 注册成功, 无积分规则, 初始化积分主表数据", identity.getId());
            memberPointsBiz.initMemberPoints(identity.getUserId(), identity.getIdentityType());
        }
    }

    /**
     * 构建菜单权限树
     *
     * @param moduleList 授权模块列表
     * @param roleId     当前角色id
     * @return 菜单权限树
     */
    private List<UserResourceVO.MenuListVO> buildMenuList(List<ModuleVO> moduleList, Long roleId) {
        // 查询角色被赋予的资源权限
        List<Long> resourceIds = roleBiz.listResourceIdsByRoleId(roleId);
        if (CollectionUtil.isEmpty(resourceIds)) {
            return Collections.emptyList();
        }

        // 获取所具有的资源权限
        List<String> authModuleCodes = moduleList.stream().map(ModuleVO::getCode).collect(Collectors.toList());
        List<Resource> resourceList = resourceService.list(new LambdaQueryWrapper<Resource>().in(Resource::getModuleCode, authModuleCodes));
        // 如果不是超级管理员 需要过滤权限
        if (!roleId.equals(BasicConstants.SUPER_ADMIN_ROLE_ID)) {
            // 将parentIds 路径中包含resourceIds的资源或者模块资源过滤出来
            resourceList = resourceList.stream().filter(resource -> {
                String[] parentIds = resource.getParentIds().split(StrUtil.COMMA);
                return Arrays.stream(parentIds).anyMatch(parentId -> resourceIds.contains(Long.valueOf(parentId)))
                        || resource.getType().equals(ResourceTypeEnums.MODULE.getType());
            }).collect(Collectors.toList());
        }

        // 构建菜单权限树
        Map<Long, List<Resource>> resourceMap = resourceList.stream().collect(Collectors.groupingBy(Resource::getParentId));
        // 获取模块集合
        List<Resource> rootResourceList = resourceMap.get(CommonConstants.DEFAULT_LONG_PARENT_ID);
        List<UserResourceVO.MenuListVO> menuList = new ArrayList<>();
        rootResourceList.forEach(rootResource -> {
            UserResourceVO.MenuListVO menuListVO = new UserResourceVO.MenuListVO();
            menuListVO.setAppName(CharSequenceUtil.toCamelCase(rootResource.getModuleCode()));
            menuListVO.setMenuLayout(rootResource.getMenuLayout());
            UserResourceVO.MenuListVO.DefaultIndexVO defaultIndexVO = new UserResourceVO.MenuListVO.DefaultIndexVO();
            defaultIndexVO.setId(rootResource.getDefaultResourceId());
            defaultIndexVO.setPath(rootResource.getDefaultResourcePath());
            menuListVO.setDefaultIndex(defaultIndexVO);

            // 构建子集树
            List<Resource> childResourceList = resourceMap.getOrDefault(rootResource.getId(), new ArrayList<>());
            List<UserResourceVO.MenuListVO.MenuDataVO> rootMenuDataList = BeanUtil.copyToList(childResourceList, UserResourceVO.MenuListVO.MenuDataVO.class);
            List<UserResourceVO.MenuListVO.MenuDataVO> menuDataList = new ArrayList<>(rootMenuDataList);
            for (int i = 0; i < menuDataList.size(); i++) {
                UserResourceVO.MenuListVO.MenuDataVO parentData = menuDataList.get(i);
                List<Resource> childList = resourceMap.get(parentData.getId());
                if (!CollectionUtils.isEmpty(childList)) {
                    parentData.setMenus(BeanUtil.copyToList(childList, UserResourceVO.MenuListVO.MenuDataVO.class));
                    menuDataList.addAll(parentData.getMenus());
                }
            }
            menuListVO.setMenuData(rootMenuDataList);
            menuList.add(menuListVO);
        });
        return menuList;
    }

    /**
     * 筛选出有菜单权限的模块
     *
     * @param moduleList 模块的集合
     * @param menuList   菜单权限集合
     * @return 有菜单权限的模块
     */
    private List<UserResourceVO.ProductListVO> buildProductList(List<ModuleVO> moduleList, List<UserResourceVO.MenuListVO> menuList) {
        // 先将模块编码 由下划线转换成驼峰
        moduleList.stream().forEach(module -> module.setCode(CharSequenceUtil.toCamelCase(module.getCode())));
        // 过滤出有菜单的模块
        List<ModuleVO> authModuleList = moduleList.stream().filter(module -> menuList.stream()
                .anyMatch(menu -> menu.getAppName().equals(module.getCode()) && !CollectionUtils.isEmpty(menu.getMenuData())))
                .collect(Collectors.toList());
        return authModuleList.stream().map(module -> {
            UserResourceVO.ProductListVO productListVO = new UserResourceVO.ProductListVO();
            productListVO.setType(CharSequenceUtil.toCamelCase(module.getCode()));
            productListVO.setName(module.getName());
            return productListVO;
        }).collect(Collectors.toList());
    }


    /**
     * 通过手机号验证码修改密码
     *
     * @param updatePasswordPO 更新密码参数
     * @param user             用户信息
     */
    private void updatePasswordBySmsCode(PasswordUpdatePO updatePasswordPO, User user) {
        smsBiz.checkSmsCode(user.getMobile(), BasicCacheConstants.PC_UPDATE_PASSWORD_MOBILE_KEY, updatePasswordPO.getCode());

        // 更新密码
        checkAndUpdatePassword(user, updatePasswordPO.getPassword());

        // 移除验证码
        smsBiz.removeSmsCode(user.getMobile(), BasicCacheConstants.PC_UPDATE_PASSWORD_MOBILE_KEY);
    }

    /**
     * 根据旧密码修改密码
     *
     * @param updatePasswordPO 更新密码参数
     * @param user             用户信息
     */
    private void updatePasswordByOldPassword(PasswordUpdatePO updatePasswordPO, User user) {
        Assert.isTrue(StrUtil.isNotEmpty(updatePasswordPO.getOldPassword()), "请输入旧密码");
        Assert.isTrue(PasswordUtil.checkPassword(updatePasswordPO.getOldPassword(), user.getPassword()), "旧密码错误");

        // 更新密码
        checkAndUpdatePassword(user, updatePasswordPO.getPassword());
    }

    /**
     * 检测并更新密码
     *
     * @param user     用户
     * @param password 新密码
     */
    private void checkAndUpdatePassword(User user, String password) {
        String newPasswordEncrypt = PasswordUtil.encrypt(password);
        Assert.isTrue(!PasswordUtil.checkPassword(password, user.getPassword()), "新密码不能与旧密码相同");

        User updUser = new User();
        updUser.setId(user.getId());
        updUser.setPassword(newPasswordEncrypt);
        userService.updateById(updUser);
    }

    /**
     * 检测手机号绑定状态
     *
     * @param mobile 手机号
     */
    private void checkMobileBindStatus(String mobile) {
        Long existMobile = userService.count(new LambdaQueryWrapper<User>().eq(User::getMobile, mobile));
        Assert.isTrue(existMobile == 0, "该手机号已被绑定, 请换个手机号试试吧~");
    }

    /**
     * 检测邮箱绑定状态
     *
     * @param email 邮箱
     */
    private void checkEmailBindStatus(String email) {
        Long existEmail = userService.count(new LambdaQueryWrapper<User>().eq(User::getEmail, email));
        Assert.isTrue(existEmail == 0, "该邮箱已被绑定, 请换个邮箱试试吧~");
    }
}
