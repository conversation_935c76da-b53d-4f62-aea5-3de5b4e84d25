package com.hishop.wine.biz.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.*;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.hishop.common.constants.CommonConstants;
import com.hishop.common.enums.DeleteFlagEnums;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.pojo.media.VideoBean;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.PageResultHelper;
import com.hishop.common.response.ResponseEnum;
import com.hishop.common.util.CompressUtil;
import com.hishop.common.util.ExcelUtil;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.nfs.api.NFS;
import com.hishop.wine.biz.ProductBiz;
import com.hishop.wine.biz.ProductCategoryBiz;
import com.hishop.wine.common.enums.ProductMediaTypeEnums;
import com.hishop.wine.constants.ProductConstants;
import com.hishop.wine.model.po.product.*;
import com.hishop.wine.model.vo.product.ProductInnerVO;
import com.hishop.wine.model.vo.product.ProductPageVO;
import com.hishop.wine.model.vo.product.ProductVO;
import com.hishop.wine.repository.dto.ProductImportCheckDTO;
import com.hishop.wine.repository.dto.ProductImportDTO;
import com.hishop.wine.repository.dto.ProductPageDTO;
import com.hishop.wine.repository.dto.RelateProductMediaDTO;
import com.hishop.wine.repository.entity.Product;
import com.hishop.wine.repository.entity.ProductCategory;
import com.hishop.wine.repository.entity.ProductMedia;
import com.hishop.wine.repository.param.ProductParam;
import com.hishop.wine.repository.service.ProductCategoryService;
import com.hishop.wine.repository.service.ProductMediaService;
import com.hishop.wine.repository.service.ProductService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.InputStream;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;


/**
 * 产品表 业务逻辑实现类
 *
 * @author: HuBiao
 * @date: 2023-06-19
 */
@Slf4j
@Service
public class ProductBizImpl implements ProductBiz {

    @Resource
    private ProductService productService;
    @Resource
    private ProductMediaService productMediaService;
    @Resource
    private ProductCategoryService productCategoryService;
    @Resource
    private ProductCategoryBiz productCategoryBiz;
    @Resource
    private NFS nfs;

    /**
     * 新增产品
     *
     * @param createPO 新增产品参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void create(ProductCreatePO createPO) {
        // 检测分类是否有效
        productCategoryBiz.checkAndGetValidCategory(createPO.getProductCategoryId());

        // 检测编码是否重复
        checkProductCode(createPO.getProductCode(), null);

        // 存储基本信息, 获取产品图片的第一张作为产品封面
        Product entity = BeanUtil.copyProperties(createPO, Product.class);
        entity.setProductImg(createPO.getProductImgList().get(0));
        productService.save(entity);

        // 关联产品媒体资源
        RelateProductMediaDTO relateProductMediaDTO = BeanUtil.copyProperties(createPO, RelateProductMediaDTO.class);
        relateProductMediaDTO.setProductId(entity.getId());
        relateProductMediaDTO.setIzRemoveHistory(Boolean.FALSE);
        relateProductMedia(relateProductMediaDTO);
    }

    /**
     * 编辑产品
     *
     * @param updatePO 编辑产品参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(ProductUpdatePO updatePO) {
        Product entity = productService.getById(updatePO.getId());
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }

        // 检测分类是否有效
        productCategoryBiz.checkAndGetValidCategory(updatePO.getProductCategoryId());

        // 检测编码是否重复
        checkProductCode(updatePO.getProductCode(), updatePO.getId());

        // 存储基本信息, 获取产品图片的第一张作为产品封面
        Product updateEntity = BeanUtil.copyProperties(updatePO, Product.class);
        updateEntity.setProductImg(updatePO.getProductImgList().get(0));
        productService.updateById(updateEntity);

        // 关联产品媒体资源
        RelateProductMediaDTO relateProductMediaDTO = BeanUtil.copyProperties(updatePO, RelateProductMediaDTO.class);
        relateProductMediaDTO.setProductId(entity.getId());
        relateProductMediaDTO.setIzRemoveHistory(Boolean.TRUE);
        relateProductMedia(relateProductMediaDTO);
    }

    /**
     * 通过id批量删除
     *
     * @param ids id的集合
     */
    @Override
    public void deleteByIds(List<Long> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }

        Long userId = LoginUserUtil.getLoginUser().getUserId();
        productService.logicDeleteByIds(ids, userId);
    }

    /**
     * 查询产品详情
     *
     * @param id 产品id
     * @return 产品详情
     */
    @Override
    public ProductVO detail(Long id) {
        // 产品基本信息
        Product entity = productService.getById(id);
        // 暂时详情接口去掉了逻辑删除判断
        if (entity == null) {
            throw new BusinessException(ResponseEnum.NOT_FOUND);
        }
        ProductVO productVO = BeanUtil.copyProperties(entity, ProductVO.class);

        // 查询资源信息 根据资源类型分组转换
        LambdaQueryWrapper<ProductMedia> wrapper = new LambdaQueryWrapper<ProductMedia>().eq(ProductMedia::getProductId, id);
        Map<Integer, List<ProductMedia>> mediaMap = productMediaService.list(wrapper).stream().collect(Collectors.groupingBy(ProductMedia::getMediaType));
        productVO.setProductImgList(mediaMap.getOrDefault(ProductMediaTypeEnums.PRODUCT_IMG.getType(), new ArrayList<>()).stream().map(media -> media.getUrl()).collect(Collectors.toList()));
        productVO.setProductDetailImgList(mediaMap.getOrDefault(ProductMediaTypeEnums.PRODUCT_DETAIL_IMG.getType(), new ArrayList<>()).stream().map(media -> media.getUrl()).collect(Collectors.toList()));

        // 设置视频信息
        List<ProductMedia> videoList = mediaMap.get(ProductMediaTypeEnums.MAIN_VIDEO.getType());
        if (!CollectionUtils.isEmpty(videoList)) {
            productVO.setMainVideo(VideoBean.of(videoList.get(0).getCover(), videoList.get(0).getUrl()));
        }

        // 查询产品分类
        ProductCategory category = productCategoryService.getById(entity.getProductCategoryId());
        productVO.setProductCategoryName(ObjectUtil.isNull(category) ? StrUtil.EMPTY : category.getCategoryName());
        return productVO;
    }

    /**
     * 分页查询产品列表
     *
     * @param pagePO 分页参数
     * @return 分页结果
     */
    @Override
    public PageResult<ProductPageVO> pageList(ProductQueryPO pagePO) {
        ProductParam param = BeanUtil.copyProperties(pagePO, ProductParam.class);
        Page<ProductPageDTO> pageResult = productService.queryProductPage(pagePO.buildPage(), param);

        return PageResultHelper.transfer(pageResult, ProductPageVO.class);
    }

    /**
     * 更新产品价格
     *
     * @param updatePricePO 更新参数
     */
    @Override
    public void updatePrice(ProductUpdatePricePO updatePricePO) {
        LambdaUpdateWrapper<Product> lambdaUpdateWrapper = new LambdaUpdateWrapper<>();
        lambdaUpdateWrapper.in(Product::getId, updatePricePO.getIds());
        lambdaUpdateWrapper.set(Product::getMarketPrice, updatePricePO.getMarketPrice());
        productService.update(lambdaUpdateWrapper);
    }

    /**
     * 导入产品信息
     *
     * @param file 产品压缩包
     * @return 导入结果
     */
    @Override
    public String importProduct(MultipartFile file) {
        String tempDir = null;
        ByteArrayOutputStream os = null;
        try {
            // 判断文件是否是.zip文件
            String orgName = file.getOriginalFilename();
            Assert.isTrue(orgName.endsWith(CommonConstants.ZIP_FILE_SUFFIX), "请上传.zip文件");

            // 先写入本地文件
            tempDir = ProductConstants.IMPORT_TEMP_PATH + IdUtil.fastUUID();
            if (!FileUtil.exist(tempDir)) {
                FileUtil.mkdir(tempDir);
            }
            String tempName = tempDir + File.separator + orgName;
            File tempFile = new File(tempName);
            file.transferTo(tempFile);

            // 解压文件
            File unzipFile = CompressUtil.decompress(tempName);

            // 提取导入的excel文件
            List<File> rootFiles = FileUtil.loopFiles(unzipFile, filterFile ->
                    filterFile.getName().replace(filterFile.getParent(), StrUtil.EMPTY).equals(ProductConstants.PRODUCT_IMPORT_EXCEL_NAME));
            Assert.isTrue(!CollectionUtils.isEmpty(rootFiles), String.format("压缩包中不存在[%s]文件", ProductConstants.PRODUCT_IMPORT_EXCEL_NAME));
            Assert.isTrue(rootFiles.size() == 1, String.format("压缩包中存在多个[%s]文件", ProductConstants.PRODUCT_IMPORT_EXCEL_NAME));

            // 提取excel文件
            File rootFile = rootFiles.get(0);
            // 当前文件的根路径(用来获取图片)
            String rootPath = rootFile.getParent();

            // 提取导入信息
            List<ProductImportDTO> productList = ExcelUtil.read(FileUtil.getInputStream(rootFile), ProductImportDTO.class);
            Assert.isTrue(ObjectUtil.isNotNull(productList), "导入数据为空");

            // 检测导入数据
            ProductImportCheckDTO checkResult = checkProductImport(productList, rootPath);

            // 检测通过的数据入库
            ProductBiz productBiz = SpringUtil.getBean(ProductBiz.class);
            productBiz.insertCheckSuccessForImport(checkResult.getSuccessList());

            // 检测未通过的需要返回给前端
            if (!CollectionUtils.isEmpty(checkResult.getFailList())) {
                byte[] fileContent = nfs.getFileContent(ProductConstants.PRODUCT_IMPORT_RESULT_TEMPLATE);
                InputStream inputStream = new ByteArrayInputStream(fileContent);

                os = new ByteArrayOutputStream();
                EasyExcel.write(os).withTemplate(inputStream).sheet().doFill(checkResult.getFailList());
                String filePath = String.format(ProductConstants.PRODUCT_IMPORT_RESULT, DateUtil.format(new Date(), DatePattern.PURE_DATETIME_FORMAT));
                nfs.upload(filePath, os.toByteArray());
                return filePath;
            }
        } catch (Exception e) {
            log.error("导入产品信息失败", e);
            throw new BusinessException(String.format("导入产品信息失败: [%s]", e.getMessage()));
        } finally {
            // 删除临时文件
            if (FileUtil.exist(tempDir)) {
                FileUtil.del(tempDir);
            }
            IoUtil.close(os);
        }
        return StrUtil.EMPTY;
    }

    /**
     * 导入检测成功的数据
     *
     * @param exportProductList 导入产品集合
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void insertCheckSuccessForImport(List<ProductImportDTO> exportProductList) {
        if (CollectionUtils.isEmpty(exportProductList)) {
            return;
        }

        // 批量添加产品
        List<Product> products = BeanUtil.copyToList(exportProductList, Product.class);
        products.forEach(product -> {
            product.setStatus(Boolean.TRUE);
            product.setIzDelete(DeleteFlagEnums.NO.getCode());
            product.setProductUnit(StrUtil.EMPTY);
        });
        productService.insertProductBatch(products);

        // 根据产品编码 把刚刚添加进去的这批产品id查询出来 用来关联图片
        List<String> productCodes = exportProductList.stream().map(exportProduct -> exportProduct.getProductCode()).collect(Collectors.toList());
        Map<String, Long> productCodeIdMap = productService.list(new LambdaQueryWrapper<Product>()
                .in(Product::getProductCode, productCodes).eq(Product::getIzDelete, DeleteFlagEnums.NO.getCode())
                .select(Product::getId, Product::getProductCode))
                .stream().collect(Collectors.toMap(Product::getProductCode, Product::getId));

        // 关联图片
        List<ProductMedia> productMediaList = exportProductList.stream().map(exportProduct
                -> ProductMedia.of(productCodeIdMap.get(exportProduct.getProductCode()), ProductMediaTypeEnums.PRODUCT_IMG.getType(), exportProduct.getProductImg(), StrUtil.EMPTY))
                .collect(Collectors.toList());
        productMediaService.insertProductMediaBatch(productMediaList);
    }

    /**
     * 查询产品详情
     *
     * @param id 产品id
     * @return 产品详情
     */
    @Override
    public ProductInnerVO getDetailForInner(Long id) {
        ProductVO vo = detail(id);
        ProductInnerVO productInnerVO = BeanUtil.copyProperties(vo, ProductInnerVO.class);
        productInnerVO.setProductImg(CollectionUtils.isEmpty(vo.getProductImgList()) ? StrUtil.EMPTY : vo.getProductImgList().get(0));
        return productInnerVO;
    }

    @Override
    public List<ProductInnerVO> listById(ProductQueryPO qryPO) {
        ProductParam param = BeanUtil.copyProperties(qryPO, ProductParam.class);
        List<Product> dbList = productService.qryList(param);
        if (dbList == null) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(dbList, ProductInnerVO.class);
    }

    /**
     * 查询产品id的集合
     *
     * @param productIdQueryPO 筛选参数
     * @return 产品id的集合
     */
    @Override
    public List<Long> queryIds(ProductIdQueryPO productIdQueryPO) {
        return productService.listObjs(new LambdaQueryWrapper<Product>()
                .eq(ObjectUtil.isNotNull(productIdQueryPO.getProductType()), Product::getProductType, productIdQueryPO.getProductType())
                .eq(ObjectUtil.isNotNull(productIdQueryPO.getProductCategoryId()), Product::getProductCategoryId, productIdQueryPO.getProductCategoryId())
                .and(ObjectUtil.isNotNull(productIdQueryPO.getSearchValue()), wrapper -> wrapper.like(Product::getProductName, productIdQueryPO.getSearchValue())
                        .or().like(Product::getProductCode, productIdQueryPO.getSearchValue()))
                .select(Product::getId)).stream().map(id -> Long.valueOf(String.valueOf(id))).collect(Collectors.toList());
    }

    /**
     * 检测产品导入数据
     *
     * @param exportProductList 产品导入数据
     * @param rootPath          产品根路径
     * @return 检测结果
     */
    private ProductImportCheckDTO checkProductImport(List<ProductImportDTO> exportProductList, String rootPath) {
        ProductImportCheckDTO checkResult = ProductImportCheckDTO.ofEmpty();

        List<String> productCodeList = exportProductList.stream().map(product -> product.getProductCode()).collect(Collectors.toList());

        // 查询所有已经存在的产品编码
        Set<String> existCodes = productService.listObjs(new LambdaQueryWrapper<Product>()
                .in(Product::getProductCode, productCodeList).eq(Product::getIzDelete, DeleteFlagEnums.NO.getCode()).select(Product::getProductCode))
                .stream().map(code -> String.valueOf(code)).collect(Collectors.toSet());

        // 查询已经存在的产品分类
        Map<String, ProductCategory> categoryMap = productCategoryService.list(new LambdaQueryWrapper<ProductCategory>()
                .eq(ProductCategory::getIzDelete, DeleteFlagEnums.NO.getCode()))
                .stream().collect(Collectors.toMap(ProductCategory::getCategoryName, Function.identity()));

        // 检测导入信息
        exportProductList.forEach(exportProduct -> {
            StringBuilder resultBuilder = exportProduct.checkBaseParam();

            // 检测主图在压缩包中是否存在
            checkProductImgForImport(exportProduct, rootPath, resultBuilder);

            // 检测编码重复
            checkProductCodeForImport(exportProduct, existCodes, resultBuilder);

            // 分组成功和失败的集合
            if (resultBuilder.length() > 0) {
                exportProduct.setResult(resultBuilder.toString());
                checkResult.getFailList().add(exportProduct);
            } else {
                checkResult.getSuccessList().add(exportProduct);

                // 添加产品分类
                insertProductCategoryIfNotExist(exportProduct, categoryMap);

                // 上传图片
                updateProductImgForImport(exportProduct);
            }
        });
        return checkResult;
    }

    /**
     * 导入时检测产品编码是否重复
     *
     * @param exportProduct 导入产品信息
     * @param existCodes    已经存在的产品编码
     * @param resultBuilder 异常信息
     */
    private void checkProductCodeForImport(ProductImportDTO exportProduct, Set<String> existCodes, StringBuilder resultBuilder) {
        // 检测编码是否重复
        if (existCodes.contains(exportProduct.getProductCode())) {
            resultBuilder.append("产品编码已经存在;");
        }
        // 如果没有异常信息了 则标识该产品能正常导入 将需要导入的编码添加到set中
        if (resultBuilder.length() == 0) {
            existCodes.add(exportProduct.getProductCode());
        }
    }

    /**
     * 检测导入的产品主图 是否在压缩包中存在
     *
     * @param exportProduct 导入产品信息
     * @param rootPath      压缩包根目录
     * @param resultBuilder 异常信息
     */
    private void checkProductImgForImport(ProductImportDTO exportProduct, String rootPath, StringBuilder resultBuilder) {
        String productImg = exportProduct.getProductImg();
        String pathName = rootPath + File.separator + productImg;
        if (!FileUtil.exist(pathName)) {
            resultBuilder.append("主图在打包文件中不存在;");
        } else {
            exportProduct.setProductImgFile(new File(pathName));
        }
    }

    /**
     * 如果没有分类信息添加分类信息
     *
     * @param exportProduct 导入产品信息
     * @param categoryMap   分类map
     */
    private void insertProductCategoryIfNotExist(ProductImportDTO exportProduct, Map<String, ProductCategory> categoryMap) {
        // 检验产品分类
        String categoryName = exportProduct.getProductCategoryName();
        if (!StrUtil.isEmpty(categoryName)) {
            ProductCategory category = categoryMap.get(categoryName);

            // 如果不存在的分类 则添加一个
            if (ObjectUtil.isNull(category)) {
                category = new ProductCategory();
                category.setCategoryName(categoryName);
                productCategoryService.save(category);
                categoryMap.put(categoryName, category);
            } else {
                // 如果存在分类 则检测分类是否被禁用
                if (!category.getStatus()) {
                    exportProduct.setResult("产品分类已经被禁用;");
                    return;
                }
            }

            exportProduct.setProductCategoryId(category.getId());
        }
    }

    /**
     * 上传图片
     *
     * @param productImportDTO 导入信息
     */
    private void updateProductImgForImport(ProductImportDTO productImportDTO) {
        InputStream is = null;
        try {
            is = FileUtil.getInputStream(productImportDTO.getProductImgFile());
            String suffix = FileUtil.getSuffix(productImportDTO.getProductImg());
            String filePath = ProductConstants.NFS_PRODUCT_IMPORT_PATH + IdUtil.fastUUID() + StrUtil.DOT + suffix;
            nfs.upload(filePath, is);
            productImportDTO.setProductImg(filePath);
        } finally {
            IoUtil.close(is);
        }
    }

    /**
     * 检测产品编码是否存在
     *
     * @param productCode 产品编码
     * @param productId   产品Id
     */
    private void checkProductCode(String productCode, Long productId) {
        LambdaQueryWrapper<Product> wrapper = new LambdaQueryWrapper<Product>()
                .eq(Product::getProductCode, productCode).eq(Product::getIzDelete, DeleteFlagEnums.NO.getCode());
        if (ObjectUtil.isNotNull(productId)) {
            wrapper.ne(Product::getId, productId);
        }
        Long count = productService.count(wrapper);
        Assert.isTrue(count == 0, String.format("编码已存在[%s]", productCode));
    }

    /**
     * 关联产品媒体资源
     *
     * @param relateProductMediaParam 关联产品媒体资源参数
     */
    private void relateProductMedia(RelateProductMediaDTO relateProductMediaParam) {
        Boolean izRemoveHistory = relateProductMediaParam.getIzRemoveHistory();
        if (ObjectUtil.isNotNull(izRemoveHistory) && izRemoveHistory) {
            // 先移除旧的媒体资源
            productMediaService.removeByProductId(relateProductMediaParam.getProductId());
        }

        // 关联新的媒体资源
        List<ProductMedia> productMediaList = buildProductMediaList(relateProductMediaParam);
        productMediaService.insertProductMediaBatch(productMediaList);
    }

    /**
     * 构建产品媒体资源列表
     *
     * @param relateProductMediaParam 关联产品媒体资源参数
     * @return 产品媒体列表
     */
    private List<ProductMedia> buildProductMediaList(RelateProductMediaDTO relateProductMediaParam) {
        List<ProductMedia> productMediaList = new ArrayList<>();
        Long productId = relateProductMediaParam.getProductId();
        productMediaList.addAll(buildProductMediaList(productId, ProductMediaTypeEnums.PRODUCT_IMG, relateProductMediaParam.getProductImgList()));
        productMediaList.addAll(buildProductMediaList(productId, ProductMediaTypeEnums.PRODUCT_DETAIL_IMG, relateProductMediaParam.getProductDetailImgList()));
        VideoBean mainVideo = relateProductMediaParam.getMainVideo();
        if (ObjectUtil.isNotNull(mainVideo)) {
            productMediaList.add(ProductMedia.of(productId, ProductMediaTypeEnums.MAIN_VIDEO.getType(), mainVideo.getPath(), mainVideo.getCover()));
        }
        return productMediaList;
    }

    /**
     * 构建产品媒体资源列表
     *
     * @param productId 产品Id
     * @param mediaType 媒体类型
     * @param mediaList 资源列表
     * @return 产品媒体列表
     */
    private List<ProductMedia> buildProductMediaList(Long productId, ProductMediaTypeEnums mediaType, List<String> mediaList) {
        if (CollectionUtils.isEmpty(mediaList)) {
            return new ArrayList<>();
        }
        return mediaList.stream().map(media -> ProductMedia.of(productId, mediaType.getType(), media, StrUtil.EMPTY)).collect(Collectors.toList());
    }
}