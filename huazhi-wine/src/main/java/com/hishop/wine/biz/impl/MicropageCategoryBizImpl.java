package com.hishop.wine.biz.impl;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.hishop.common.exception.BusinessException;
import com.hishop.wine.biz.MicropageCategoryBiz;
import com.hishop.wine.common.utils.TreeConverter;
import com.hishop.wine.enums.MicropageEnum;
import com.hishop.wine.model.po.micropage.MicropageCategoryDeletePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryMovePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryPO;
import com.hishop.wine.model.po.micropage.MicropageCategoryUpdatePO;
import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;
import com.hishop.wine.repository.entity.Micropage;
import com.hishop.wine.repository.entity.MicropageCategory;
import com.hishop.wine.repository.service.MicropageCategoryService;
import com.hishop.wine.repository.service.MicropageService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class MicropageCategoryBizImpl implements MicropageCategoryBiz {

    private final MicropageCategoryService micropageCategoryService;

    private final MicropageService micropageService;

    @Override
    public boolean create(MicropageCategoryPO micropageCategoryPO) {

        String path = "0";
        //判断是否大于3级结构
        if (micropageCategoryPO.getParentId() != 0) {
            MicropageCategory prev = micropageCategoryService.getById(micropageCategoryPO.getParentId());
            if (ObjectUtil.isNull(prev)) {
                throw new BusinessException(String.format("父级分组[%s]不存在", micropageCategoryPO.getId()));
            }
            micropageCategoryPO.setLevel(prev.getLevel() + 1);
            path = prev.getPath() + "," + prev.getId();
        } else {
            micropageCategoryPO.setLevel(0);
        }

        /*if (micropageCategoryPO.getLevel() > 2) {
            throw new BusinessException(String.format("分组最多支持三级"));
        }*/

        //判断是否有重名
        UpdateWrapper<MicropageCategory> query = new UpdateWrapper<>();
        query.eq("name", micropageCategoryPO.getName());
        MicropageCategory exist = micropageCategoryService.getOne(query);
        if (!ObjectUtil.isNull(exist)) {
            throw new BusinessException(String.format("已存在分组名称[%s]", micropageCategoryPO.getName()));
        }
        MicropageCategory model = BeanUtil.copyProperties(micropageCategoryPO, MicropageCategory.class);
        model.setPath(path);
        micropageCategoryService.save(model);
        return true;
    }

    @Override
    public boolean update(MicropageCategoryUpdatePO micropageCategoryUpdatePO) {

        String path = "0";
        //判断是否大于3级结构
        if (micropageCategoryUpdatePO.getParentId() != 0) {
            MicropageCategory prev = micropageCategoryService.getById(micropageCategoryUpdatePO.getParentId());
            if (ObjectUtil.isNull(prev)) {
                throw new BusinessException(String.format("父级分组[%s]不存在", micropageCategoryUpdatePO.getId()));
            }
            micropageCategoryUpdatePO.setLevel(prev.getLevel() + 1);
            path = prev.getPath() + "," + prev.getId();
        } else {
            micropageCategoryUpdatePO.setLevel(0);
        }

        /*if (micropageCategoryUpdatePO.getLevel() > 2) {
            throw new BusinessException(String.format("分组最多支持三级"));
        }*/

        UpdateWrapper<MicropageCategory> query = new UpdateWrapper<>();
        query.eq("name", micropageCategoryUpdatePO.getName());
        query.ne("id", micropageCategoryUpdatePO.getId());
        MicropageCategory exist = micropageCategoryService.getOne(query);
        if (!ObjectUtil.isNull(exist)) {
            throw new BusinessException(String.format("已存在分组名称[%s]", micropageCategoryUpdatePO.getName()));
        }

        MicropageCategory model = BeanUtil.copyProperties(micropageCategoryUpdatePO, MicropageCategory.class);
        UpdateWrapper<MicropageCategory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.eq("id", model.getId())
                .set("name", model.getName())
                .set("parent_id", model.getParentId())
                .set("update_time", new Date())
                .set("level", model.getLevel())
                .set("path", path);
        micropageCategoryService.update(null, updateWrapper);

        return false;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(MicropageCategoryDeletePO micropageCategoryDeletePO) {
        MicropageCategory micropageCategory = micropageCategoryService.getById(micropageCategoryDeletePO.getId());
        if (ObjectUtil.isNull(micropageCategory)) {
            throw new BusinessException(String.format("分组Id[%s]不存在", micropageCategoryDeletePO.getId()));
        }

        // 获取下级分组的集合
        List<Long> ids = micropageCategoryService.recursionChildIds(micropageCategory.getId());
        ids.add(micropageCategory.getId());

        //将该分组下包括子分组下的所有微页面，移动到未分组
        UpdateWrapper<Micropage> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("category_id", ids)
                .set("category_id", 0);
        micropageService.update(null, updateWrapper);

        //物理删除分组
        micropageCategoryService.deleteBatchIds(ids);
        return true;
    }

    @Override
    public MicropageCategoryVO detail(int id) {
        if (id < 1) {
            throw new BusinessException(String.format("分组Id[%s]不合法", id));
        }
        MicropageCategory category = micropageCategoryService.getById(id);
        return BeanUtil.copyProperties(category, MicropageCategoryVO.class);
    }

    @Override
    public List<MicropageCategoryVO> list(String name) {
        // 构建树形结构
        List<MicropageCategory> dbCategoryList = micropageCategoryService.listAllCategorys(name);
        List<MicropageCategoryVO> categoryList = BeanUtil.copyToList(dbCategoryList, MicropageCategoryVO.class);
        List<MicropageCategoryVO> result = TreeConverter.convertToTreeList(categoryList, 0);

        // 添加草稿分组
        MicropageCategoryVO defaultVo = new MicropageCategoryVO();
        defaultVo.setName(MicropageEnum.Category.DRAFT.getName());
        defaultVo.setId(MicropageEnum.Category.DRAFT.getValue().intValue());
        defaultVo.setFileCount(micropageService.countFile(MicropageEnum.MicropageStatus.draft.getCode(), null));

        result.add(0, defaultVo);

        // 添加未分组
        defaultVo = new MicropageCategoryVO();
        defaultVo.setName(MicropageEnum.Category.UNGROUPED.getName());
        defaultVo.setId(MicropageEnum.Category.UNGROUPED.getValue().intValue());
        defaultVo.setFileCount(micropageService.countFile(null, 0L));
        result.add(0, defaultVo);

        // 添加全部分组
        defaultVo = new MicropageCategoryVO();
        defaultVo.setName(MicropageEnum.Category.ALL.getName());
        defaultVo.setId(MicropageEnum.Category.ALL.getValue().intValue());
        defaultVo.setFileCount(micropageService.countFile(null, null));
        result.add(0, defaultVo);

        //增加过滤
        if (StringUtils.isNotBlank(name)) {
            result = result.stream().filter(item -> item.getName().contains(name)).collect(Collectors.toList());
        }
        return result;
    }

    @Override
    public List<MicropageCategoryVO> listParent() {
        QueryWrapper<MicropageCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.lt("level", 2);
        List<MicropageCategory> list = micropageCategoryService.selectList(queryWrapper);
        return TreeConverter.convertToTreeList(BeanUtil.copyToList(list, MicropageCategoryVO.class), 0);
    }

    @Override
    public boolean move(MicropageCategoryMovePO micropageCategoryMovePO) {
        String path = "0";
        MicropageCategory micropageCategory = micropageCategoryService.getById(micropageCategoryMovePO.getParentId());
        if (ObjectUtil.isNotNull(micropageCategory)) {
            path = micropageCategory.getPath() + "," + micropageCategory.getId();
        }
        UpdateWrapper<MicropageCategory> updateWrapper = new UpdateWrapper<>();
        updateWrapper.in("id", micropageCategoryMovePO.getIds())
                .set("parent_id", micropageCategoryMovePO.getParentId())
                .set("path", path)
                .set("update_time", new Date());
        micropageCategoryService.update(null, updateWrapper);
        return true;
    }

    @Override
    public List<MicropageCategoryVO> getListByIds(List<Long> cateIds) {
        QueryWrapper<MicropageCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("id", cateIds);
        List<MicropageCategory> list = micropageCategoryService.selectList(queryWrapper);
        return BeanUtil.copyToList(list, MicropageCategoryVO.class);
    }

    @Override
    public List<MicropageCategoryVO> getListLessThanLevel(int maxLevel, List<Long> cateIds) {
        QueryWrapper<MicropageCategory> queryWrapper = new QueryWrapper<>();
        queryWrapper.le("level", maxLevel);
        queryWrapper.notIn("id", cateIds);
        List<MicropageCategory> list = micropageCategoryService.selectList(queryWrapper);
        List<MicropageCategoryVO> listVo = BeanUtil.copyToList(list, MicropageCategoryVO.class);
        return TreeConverter.convertToTreeList(listVo, 0);
    }
}
