package com.hishop.wine.biz;

import com.hishop.wine.model.po.address.BaseAddressCreatePO;
import com.hishop.wine.model.po.address.BaseAddressUpdatePO;
import com.hishop.wine.model.po.address.BaseAddressQueryPO;
import com.hishop.wine.model.vo.address.BaseAddressSimpleVO;
import com.hishop.wine.model.vo.address.BaseAddressVO;

import java.util.List;

import com.hishop.common.response.PageResult;

/**
 * 地址库表 业务逻辑接口
 *
 * @author: HuBiao
 * @date: 2023-07-17
 */

public interface BaseAddressBiz {

    /**
     * 创建地址
     *
     * @param createPO 创建地址参数
     * @return 地址id
     */
    Long create(BaseAddressCreatePO createPO);

    /**
     * 编辑地址
     *
     * @param updatePO 编辑地址参数
     */
    void update(BaseAddressUpdatePO updatePO);

    /**
     * 删除地址
     *
     * @param id 地址Id
     */
    void deleteById(Long id);

    /**
     * 获取地址详情
     *
     * @param id 地址id
     * @return 地址详情
     */
    BaseAddressVO detail(Long id);

    /**
     * 获取地址列表
     *
     * @param queryPO 筛选参数
     * @return 地址列表
     */
    List<BaseAddressSimpleVO> list(BaseAddressQueryPO queryPO);

    /**
     * 分页查询地址列表
     *
     * @param pagePO 筛选参数
     * @return 地址列表
     */
    PageResult<BaseAddressSimpleVO> pageList(BaseAddressQueryPO pagePO);

}