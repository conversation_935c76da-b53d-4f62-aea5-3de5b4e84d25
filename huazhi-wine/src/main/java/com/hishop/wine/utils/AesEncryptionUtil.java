package com.hishop.wine.utils;

import lombok.extern.slf4j.Slf4j;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.SecureRandom;
import java.util.Base64;

/**
 * AES加密工具类
 * 支持AES-256-CBC加密模式
 *
 * <AUTHOR>
 * @date 2024-08-04
 */
@Slf4j
public class AesEncryptionUtil {

    // 固定密钥（实际项目中应从安全配置中获取）
    private static final String SECRET_KEY = "MySuperSecretKeyForAES256Encrypt"; // 32字节密钥
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16; // 16 bytes for AES
    private static final int KEY_LENGTH = 32; // 32 bytes for AES-256
    /**
     * 生成32字节的密钥
     *
     * @param key 原始密钥字符串
     * @return 32字节的密钥
     */
    private static byte[] generateKey(String key) {
        try {
            MessageDigest sha = MessageDigest.getInstance("SHA-256");
            byte[] keyBytes = key.getBytes(StandardCharsets.UTF_8);
            byte[] hashedKey = sha.digest(keyBytes);

            // 确保密钥长度为32字节
            byte[] finalKey = new byte[KEY_LENGTH];
            System.arraycopy(hashedKey, 0, finalKey, 0, Math.min(hashedKey.length, KEY_LENGTH));
            return finalKey;
        } catch (Exception e) {
            log.error("生成密钥失败", e);
            throw new RuntimeException("生成密钥失败", e);
        }
    }

    /**
     * 加密方法
     *
     * @param plaintext 明文
     * @return Base64编码的加密结果（包含IV）
     * @throws IllegalArgumentException 当输入参数为空时抛出
     */
    public static String encrypt(String plaintext) {
        if (plaintext == null || plaintext.isEmpty()) {
            throw new IllegalArgumentException("待加密的明文不能为空");
        }

        try {
            log.debug("开始AES加密，明文长度: {}", plaintext.length());

            // 生成随机IV
            byte[] iv = new byte[IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 生成32字节密钥
            byte[] keyBytes = generateKey(SECRET_KEY);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

            // 初始化加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);

            // 加密数据
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));

            // 组合IV和加密数据
            byte[] combined = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);

            // Base64编码
            String result = Base64.getEncoder().encodeToString(combined);
            log.debug("AES加密完成，密文长度: {}", result.length());
            return result;

        } catch (Exception e) {
            log.error("AES加密失败，明文: {}", plaintext, e);
            throw new RuntimeException("加密失败: " + e.getMessage(), e);
        }
    }
    /**
     * 解密方法
     *
     * @param ciphertext Base64编码的加密结果
     * @return 解密后的明文
     * @throws IllegalArgumentException 当输入参数为空或格式错误时抛出
     */
    public static String decrypt(String ciphertext) {
        if (ciphertext == null || ciphertext.isEmpty()) {
            throw new IllegalArgumentException("待解密的密文不能为空");
        }

        try {
            log.debug("开始AES解密，密文长度: {}", ciphertext.length());

            // Base64解码
            byte[] combined = Base64.getDecoder().decode(ciphertext);

            // 验证数据长度
            if (combined.length < IV_LENGTH) {
                throw new IllegalArgumentException("密文格式错误，长度不足");
            }

            // 分离IV和加密数据
            byte[] iv = new byte[IV_LENGTH];
            byte[] encrypted = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, iv.length);
            System.arraycopy(combined, iv.length, encrypted, 0, encrypted.length);

            IvParameterSpec ivSpec = new IvParameterSpec(iv);

            // 生成32字节密钥
            byte[] keyBytes = generateKey(SECRET_KEY);
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, "AES");

            // 初始化解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);

            // 解密数据
            byte[] decrypted = cipher.doFinal(encrypted);
            String result = new String(decrypted, StandardCharsets.UTF_8);

            log.debug("AES解密完成，明文长度: {}", result.length());
            return result;

        } catch (IllegalArgumentException e) {
            log.error("AES解密参数错误: {}", e.getMessage());
            throw e;
        } catch (Exception e) {
            log.error("AES解密失败，密文: {}", ciphertext, e);
            throw new RuntimeException("解密失败: " + e.getMessage(), e);
        }
    }
    /**
     * 验证加密解密功能是否正常
     *
     * @param plaintext 待测试的明文
     * @return 是否验证成功
     */
    public static boolean verify(String plaintext) {
        try {
            String encrypted = encrypt(plaintext);
            String decrypted = decrypt(encrypted);
            return plaintext.equals(decrypted);
        } catch (Exception e) {
            log.error("AES加密解密验证失败", e);
            return false;
        }
    }

    /**
     * 测试方法
     *
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            // 测试加密解密
            String original = "{\"accessKeyId\":\"AKIDa3LQwHRO6brukLiqibHl8IDuOuDrSqzu\",\"accessKeySecret\":\"pTuBHBqHUVHoALI2ayBFAqJbfV7XjDUY\"}";

            System.out.println("=== AES加密工具测试 ===");
            System.out.println("原文: " + original);
            System.out.println("原文长度: " + original.length());

            // 加密
            long startTime = System.currentTimeMillis();
            String encrypted = encrypt(original);
            long encryptTime = System.currentTimeMillis() - startTime;
            System.out.println("加密结果: " + encrypted);
            System.out.println("加密耗时: " + encryptTime + "ms");

            // 解密
            startTime = System.currentTimeMillis();
            String decrypted = decrypt(encrypted);
            long decryptTime = System.currentTimeMillis() - startTime;
            System.out.println("解密结果: " + decrypted);
            System.out.println("解密耗时: " + decryptTime + "ms");

            // 验证
            boolean isValid = original.equals(decrypted);
            System.out.println("验证结果: " + (isValid ? "成功" : "失败"));

            // 测试异常情况
            System.out.println("\n=== 异常测试 ===");
            try {
                encrypt(null);
            } catch (IllegalArgumentException e) {
                System.out.println("空明文测试: " + e.getMessage());
            }

            try {
                decrypt("invalid_base64");
            } catch (Exception e) {
                System.out.println("无效密文测试: " + e.getMessage());
            }

            System.out.println("\n=== 测试完成 ===");

        } catch (Exception e) {
            System.err.println("测试过程中发生异常: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
