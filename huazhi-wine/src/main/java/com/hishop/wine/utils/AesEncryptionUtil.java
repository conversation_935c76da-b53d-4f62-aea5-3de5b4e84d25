package com.hishop.wine.utils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.util.Base64;

public class AesEncryptionUtil {
    // 固定密钥（实际项目中应从安全配置中获取）
    private static final String SECRET_KEY = "MySuperSecretKeyForAES256Encryption";
    private static final String ALGORITHM = "AES/CBC/PKCS5Padding";
    private static final int IV_LENGTH = 16; // 16 bytes for AES
    /**
     * 加密方法
     * @param plaintext 明文
     * @return Base64编码的加密结果（包含IV）
     */
    public static String encrypt(String plaintext) {
        try {
            // 生成随机IV
            byte[] iv = new byte[IV_LENGTH];
            new SecureRandom().nextBytes(iv);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            // 生成密钥
            SecretKeySpec keySpec = new SecretKeySpec(
                    SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            // 初始化加密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, ivSpec);
            // 加密数据
            byte[] encrypted = cipher.doFinal(plaintext.getBytes(StandardCharsets.UTF_8));
            // 组合IV和加密数据
            byte[] combined = new byte[iv.length + encrypted.length];
            System.arraycopy(iv, 0, combined, 0, iv.length);
            System.arraycopy(encrypted, 0, combined, iv.length, encrypted.length);
            // Base64编码
            return Base64.getEncoder().encodeToString(combined);
        } catch (Exception e) {
            throw new RuntimeException("加密失败", e);
        }
    }
    /**
     * 解密方法（用于测试）
     * @param ciphertext Base64编码的加密结果
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext) {
        try {
            // Base64解码
            byte[] combined = Base64.getDecoder().decode(ciphertext);
            // 分离IV和加密数据
            byte[] iv = new byte[IV_LENGTH];
            byte[] encrypted = new byte[combined.length - IV_LENGTH];
            System.arraycopy(combined, 0, iv, 0, iv.length);
            System.arraycopy(combined, iv.length, encrypted, 0, encrypted.length);
            IvParameterSpec ivSpec = new IvParameterSpec(iv);
            SecretKeySpec keySpec = new SecretKeySpec(
                    SECRET_KEY.getBytes(StandardCharsets.UTF_8), "AES");
            // 初始化解密器
            Cipher cipher = Cipher.getInstance(ALGORITHM);
            cipher.init(Cipher.DECRYPT_MODE, keySpec, ivSpec);
            // 解密数据
            byte[] decrypted = cipher.doFinal(encrypted);
            return new String(decrypted, StandardCharsets.UTF_8);
        } catch (Exception e) {
            throw new RuntimeException("解密失败", e);
        }
    }
    public static void main(String[] args) {
        // 测试加密解密
        String original = "{\"accessKeyId\":\"AKIDa3LQwHRO6brukLiqibHl8IDuOuDrSqzu\",\"accessKeySecret\":\"pTuBHBqHUVHoALI2ayBFAqJbfV7XjDUY\"}";
        // 加密
        String encrypted = encrypt(original);
        System.out.println("加密结果: " + encrypted);
        // 解密
        String decrypted = decrypt(encrypted);
        System.out.println("解密结果: " + decrypted);
    }
}
