package com.hishop.wine.controller;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.LogisticsCodeBiz;
import com.hishop.wine.model.po.logisticsCode.BatchLogisticsCodeStatusPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeFeignPo;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeStatusPo;
import com.hishop.wine.model.vo.logisticsCode.LogisticsCodeVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import com.hishop.wine.model.po.logisticsCode.LogisticsCodeQueryPo;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.Set;

/**
 * @description: 物流码管理
 * @author: guoyufeng
 * @date: 2024/1/30 9:16
 */
@Api(value = "LogisticsCodeController", tags = "物流码管理相关接口")
@RestController
@RequestMapping("/logisticsCode")
public class LogisticsCodeController {

    @Resource
    private LogisticsCodeBiz logisticsCodeBiz;

    @ApiOperation(value = "PC-分页查询", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<LogisticsCodeVo>> pageList(@RequestBody LogisticsCodeQueryPo queryPo) {
        return ResponseBean.success(logisticsCodeBiz.pageList(queryPo));
    }

    @ApiOperation(value = "PC-删除物流码", httpMethod = "POST")
    @PostMapping("/pc/deleteBatch")
    @OperationLog(primaryKey = "ids")
    public ResponseBean<Void> delete(@RequestBody @Valid IdBatchPO<Long> idBatchPO) {
        logisticsCodeBiz.deleteByIds(idBatchPO.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "更新使用状态", httpMethod = "POST")
    @PostMapping("/batchUpdateStatus")
    public ResponseBean<Void> batchUpdateStatus(@RequestBody @Valid LogisticsCodeStatusPo codeStatusPo) {
        logisticsCodeBiz.batchUpdateStatus(codeStatusPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取物流码信息", httpMethod = "GET")
    @GetMapping("/getLogisticsCodeInfo")
    public ResponseBean<LogisticsCodeVo> getLogisticsCodeInfo(@RequestParam String logisticsCode) {
        return ResponseBean.success(logisticsCodeBiz.getLogisticsCodeInfo(logisticsCode));
    }

    @ApiOperation(value = "批量更新物流码使用状态", httpMethod = "GET")
    @PostMapping("/batchUpdateLogisticsCodesStatus")
    public ResponseBean<Set<String>> batchUpdateLogisticsCodesStatus(@RequestBody @Valid BatchLogisticsCodeStatusPo po) {
        return ResponseBean.success(logisticsCodeBiz.batchUpdateLogisticsCodesStatus(po));
    }

    @ApiOperation(value = "根据批次id获取物流码信息", httpMethod = "POST")
    @PostMapping("/getLogisticsCodeByFileImportIds")
    public ResponseBean<List<LogisticsCodeVo>> getLogisticsCodeByFileImportIds(@RequestBody @Valid LogisticsCodeFeignPo po) {
        return ResponseBean.success(logisticsCodeBiz.getLogisticsCodeByFileImportIds(po));
    }

    @ApiOperation(value = "批量更新物流码未使用状态", httpMethod = "GET")
    @PostMapping("/batchUpdateLogisticsCodesStatusNoUsed")
    public ResponseBean<Void> batchUpdateLogisticsCodesStatusNoUsed(@RequestBody @Valid BatchLogisticsCodeStatusPo po) {
        logisticsCodeBiz.batchUpdateLogisticsCodesStatusNoUsed(po);
        return ResponseBean.success();
    }

}