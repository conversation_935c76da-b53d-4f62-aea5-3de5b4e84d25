package com.hishop.wine.controller;

import com.hishop.wine.model.po.basic.ResourceSyncPO;
import com.hishop.wine.model.vo.basic.ResourceTreeVo;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.model.po.basic.ResourceCreatePO;
import com.hishop.wine.model.po.basic.ResourceUpdatePO;
import com.hishop.wine.biz.ResourceBiz;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资源表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Api(value = "ResourceController", tags = "【资源管理】")
@RestController
@RequestMapping("/resource")
public class ResourceController {

    @Resource
    private ResourceBiz resourceBiz;

    @ApiOperation(value = "PC-新增资源", httpMethod = "POST", hidden = true)
    @PostMapping({"/pc/create"})
    public ResponseBean create(@Valid @RequestBody ResourceCreatePO resourceCreatePO) {
        resourceBiz.create(resourceCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-编辑资源", httpMethod = "POST", hidden = true)
    @PostMapping({"/pc/update"})
    public ResponseBean update(@Valid @RequestBody ResourceUpdatePO resourceUpdatePO) {
        resourceBiz.update(resourceUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-删除资源", httpMethod = "POST", hidden = true)
    @PostMapping({"/pc/delete"})
    public ResponseBean delete(@RequestBody IdPO<Long> idPO) {
        resourceBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC-查询资源树", httpMethod = "GET")
    @GetMapping({"/pc/tree"})
    public ResponseBean<List<ResourceTreeVo>> tree() {
        return ResponseBean.success(resourceBiz.tree(Boolean.FALSE));
    }

    @ApiOperation(value = "PC-查询页面树", httpMethod = "GET")
    @GetMapping({"/pc/pagesTree"})
    public ResponseBean<List<ResourceTreeVo>> pagesTree() {
        return ResponseBean.success(resourceBiz.tree(Boolean.TRUE));
    }

    @ApiOperation(value = "PC-同步资源", httpMethod = "POST")
    @PostMapping({"/pc/sync"})
    public ResponseBean syncResource(@RequestBody ResourceSyncPO resourceSyncPO) {
        resourceBiz.syncResource(resourceSyncPO);
        return ResponseBean.success();
    }

}