package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.DecorateBiz;
import com.hishop.wine.model.po.decorate.DecoratePO;
import com.hishop.wine.model.po.decorate.DecorateQueryPO;
import com.hishop.wine.model.vo.decorate.DecorateVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* 小程序装修表 相关接口
* @author: chenpeng
* @date: 2023-07-11
*/

@Api(value = "MallDecorateController",tags="小程序装修相关接口" )
@RestController
@RequestMapping("/decorate")
public class DecorateController {

    @Resource
    private DecorateBiz decorateBiz;

    @ApiOperation(value = "PC端-保存(新增/修改) 商城装修", httpMethod = "POST")
    @PostMapping("/pc/save")
    public ResponseBean<Void> save(@Validated @RequestBody DecoratePO createPo) {
        decorateBiz.save(createPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "PC端-根据类型获取商城装修", httpMethod = "POST")
    @PostMapping("/pc/detail")
    public ResponseBean<DecorateVO> detail(@Validated @RequestBody DecorateQueryPO queryPO) {
        DecorateVO detail = decorateBiz.detail(queryPO);
        return ResponseBean.success(detail);
    }


    //*********************************小程序端接口*********************************

    @ApiOperation(value = "小程序端-根据类型获取商城装修", httpMethod = "POST")
    @PostMapping("/mini/detail")
    @AuthIgnore
    public ResponseBean<DecorateVO> miniDetail(@Validated @RequestBody DecorateQueryPO queryPO) {
        DecorateVO detail = decorateBiz.detail(queryPO);
        return ResponseBean.success(detail);
    }

}