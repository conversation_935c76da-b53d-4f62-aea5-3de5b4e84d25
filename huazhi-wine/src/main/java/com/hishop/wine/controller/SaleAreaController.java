package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.SaleAreaBiz;
import com.hishop.wine.biz.SaleDimBiz;
import com.hishop.wine.model.po.SaleAreaAddPo;
import com.hishop.wine.model.po.SaleAreaQueryPo;
import com.hishop.wine.model.po.SaleAreaUpdatePo;
import com.hishop.wine.model.po.SaleDimPo;
import com.hishop.wine.model.vo.sale.SaleAreaVo;
import com.hishop.wine.model.vo.sale.SaleDimVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * @description: 销售区域控制器
 * @author: chenzw
 * @date: 2024/7/4 09:13
 */
@Api(value = "SaleAreaController", tags = "【销售区域管理】")
@RestController
@RequestMapping("/saleArea")
public class SaleAreaController {

    @Resource
    private SaleDimBiz saleDimBiz;

    @Resource
    private SaleAreaBiz saleAreaBiz;

    @ApiOperation(value = "查询销售维度列表", httpMethod = "GET")
    @GetMapping(path = "/querySaleDimList")
    @OperationLog
    public ResponseBean<List<SaleDimVo>> querySaleDimList() {
        return ResponseBean.success(saleDimBiz.querySaleDimList());
    }

    @ApiOperation(value = "更新销售维度", httpMethod = "POST")
    @PostMapping(path = "/updateSaleDim")
    @OperationLog
    public ResponseBean<Void> updateSaleDim(@RequestBody @Validated SaleDimPo saleDimPo) {
        saleDimBiz.updateSaleDim(saleDimPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询销售区域列表", httpMethod = "POST")
    @PostMapping(path = "/querySaleAreaList")
    @OperationLog
    public ResponseBean<List<SaleAreaVo>> querySaleAreaList(@RequestBody SaleAreaQueryPo saleAreaQueryPo) {
        return ResponseBean.success(saleAreaBiz.querySaleAreaList(saleAreaQueryPo));
    }

    @ApiOperation(value = "新增销售区域", httpMethod = "POST")
    @PostMapping(path = "/addSaleArea")
    @OperationLog
    public ResponseBean<Void> addSaleArea(@RequestBody @Validated SaleAreaAddPo saleAreaAddPo) {
        saleAreaBiz.addSaleArea(saleAreaAddPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "更新销售区域", httpMethod = "POST")
    @PostMapping(path = "/updateSaleArea")
    @OperationLog
    public ResponseBean<Void> updateSaleArea(@RequestBody @Validated SaleAreaUpdatePo saleAreaUpdatePo) {
        saleAreaBiz.updateSaleArea(saleAreaUpdatePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除销售区域", httpMethod = "POST")
    @PostMapping(path = "/deleteSaleArea")
    @OperationLog
    public ResponseBean<Void> deleteSaleArea(@RequestBody IdPO<Long> idPo) {
        saleAreaBiz.deleteSaleArea(idPo.getId());
        return ResponseBean.success();
    }
}
