package com.hishop.wine.controller;

import com.hishop.wine.model.vo.pages.PagesTabVO;
import com.hishop.wine.model.vo.pages.PagesVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.biz.PagesBiz;
import org.springframework.web.bind.annotation.*;

import java.util.List;

import com.hishop.common.response.ResponseBean;
import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 页面配置表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-07-07
 */

@Api(value = "PagesController", tags = "PC-页面配置", hidden = true)
@RestController
@RequestMapping("/pages")
public class PagesController {

    @Resource
    private PagesBiz pagesBiz;

    /*@ApiOperation(value = "新增页面配置", httpMethod = "POST")
    @PostMapping("/create")
    public ResponseBean<Void> create(@Valid @RequestBody PagesCreatePO pagesCreatePO) {
        pagesCreatePO.setModuleCode(ObjectUtil.isNull(pagesCreatePO.getModuleCode()) ? ModuleEnums.basic_system.name() : pagesCreatePO.getModuleCode());
        pagesBiz.create(pagesCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除页面配置", httpMethod = "POST")
    @PostMapping("/delete")
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        pagesBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "新增 页面配置表", httpMethod = "POST")
    @PostMapping("/create")
    public ResponseBean<Void> create(@Valid @RequestBody PagesCreatePO pagesCreatePO) {
        pagesBiz.create(pagesCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "编辑 页面配置表", httpMethod = "POST")
    @PostMapping("/update")
    public ResponseBean<Void> update(@Valid @RequestBody PagesUpdatePO pagesUpdatePO) {
        pagesBiz.update(pagesUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除 页面配置表", httpMethod = "POST")
    @PostMapping("/delete")
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        pagesBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "根据ID 页面配置表", httpMethod = "GET")
    @GetMapping("/detail")
    public ResponseBean<PagesVO> detail(@RequestParam(name = "id") Long id) {
        PagesVO detail = pagesBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "获取 页面配置表 列表", httpMethod = "POST")
    @PostMapping("/list")
    public ResponseBean<List<PagesVO>> list(@RequestBody PagesQueryPO qryPO) {
        return ResponseBean.success(pagesBiz.list(qryPO));
    }

    @ApiOperation(value = "分页获取 页面配置表 列表", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<PagesVO>> pageList(@RequestBody PagesQueryPO pagePO) {
        return ResponseBean.success(pagesBiz.pageList(pagePO));
    }*/

    @ApiOperation(value = "查询页面配置", httpMethod = "GET", hidden = true)
    @GetMapping("/listAll")
    public ResponseBean<List<PagesTabVO>> listTabs() {
        return ResponseBean.success(pagesBiz.listTabs());
    }

    @ApiOperation(value = "根据模块查询页面配置", httpMethod = "GET", hidden = true)
    @GetMapping("/listByModule")
    public ResponseBean<List<PagesVO>> listByModule() {
        return ResponseBean.success(pagesBiz.listByModule());
    }
}