package com.hishop.wine.controller;

import com.hishop.wine.model.po.log.SystemLogQueryPO;
import com.hishop.wine.model.vo.log.SystemLogDetailVO;
import com.hishop.wine.model.vo.log.SystemLogVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.biz.SystemLogBiz;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import java.util.List;
import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;
import javax.annotation.Resource;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
* 操作日志表 相关接口
* @author: HuBiao
* @date: 2023-08-01
*/

@Api(value = "SystemLogController",tags="【操作日志】" )
@RestController
@RequestMapping("/systemLog")
public class SystemLogController {

    @Resource
    private SystemLogBiz systemLogBiz;

    @ApiOperation(value = "分页查询日志记录", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<SystemLogVO>> pageList(@RequestBody SystemLogQueryPO pagePO) {
        return ResponseBean.success(systemLogBiz.pageList(pagePO));
    }

    @ApiOperation(value = "查询日志明细", httpMethod = "GET")
    @GetMapping("/detail")
    public ResponseBean<SystemLogDetailVO> detail(@RequestParam Long id) {
        return ResponseBean.success(systemLogBiz.detail(id));
    }

	
}