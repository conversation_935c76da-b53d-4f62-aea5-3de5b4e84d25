package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.TagsBiz;
import com.hishop.wine.model.po.TagsCreatePO;
import com.hishop.wine.model.po.TagsQueryPO;
import com.hishop.wine.model.po.TagsUpdatePO;
import com.hishop.wine.model.po.minUser.MinUserDelTagPo;
import com.hishop.wine.model.po.minUser.MiniUserTagsPo;
import com.hishop.wine.model.vo.tags.TagsVO;
import com.hishop.wine.repository.dto.MyTagsDto;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
* 标签表 相关接口
* @author: chenpeng
* @date: 2023-07-17
*/

@Api(value = "TagsController",tags="标签表相关接口" )
@RestController
@RequestMapping("/tags")
public class TagsController {

    @Resource
    private TagsBiz tagsBiz;

    @ApiOperation(value = "【PC-新增标签】", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean<Void> create(@Valid @RequestBody TagsCreatePO tagsCreatePO) {
        tagsBiz.create(tagsCreatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-编辑标签】", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean<Void> create(@Valid @RequestBody TagsUpdatePO tagsUpdatePO) {
        tagsBiz.update(tagsUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-删除标签】", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        tagsBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-标签详情】", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<TagsVO> detail(@RequestParam(name = "id") Long id) {
        TagsVO detail = tagsBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "【PC-获取标签列表】", httpMethod = "GET")
    @GetMapping("/pc/list")
    public ResponseBean<List<TagsVO>> list() {
        return ResponseBean.success(tagsBiz.list());
    }


    @ApiOperation(value = "【PC-分页获取标签列表】", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<TagsVO>> pageList(@RequestBody TagsQueryPO pagePO) {
        return ResponseBean.success(tagsBiz.pageList(pagePO));
    }

    @ApiOperation(value = "【定制酒PC-打标签查询】", httpMethod = "POST")
    @PostMapping("/pc/getMyList")
    public ResponseBean<MyTagsDto> getMyList(@RequestBody TagsQueryPO pagePO) {
        return ResponseBean.success(tagsBiz.getMyList(pagePO));
    }

    @ApiOperation(value = "【定制酒PC-批量打标签查询】", httpMethod = "POST")
    @PostMapping("/pc/getBatchList")
    public ResponseBean<MyTagsDto> getBatchList(@RequestBody TagsQueryPO pagePO) {
        return ResponseBean.success(tagsBiz.getBatchList(pagePO));
    }

    @ApiOperation(value = "【定制酒PC-详情添加标签查询】", httpMethod = "POST")
    @PostMapping("/pc/getDetailList")
    public ResponseBean<MyTagsDto> getDetailList(@RequestBody TagsQueryPO pagePO) {
        return ResponseBean.success(tagsBiz.getDetailList(pagePO));
    }

    @ApiOperation(value = "【定制酒PC-批量添加标签】", httpMethod = "POST")
    @PostMapping("/pc/tags")
    public ResponseBean<Void> tags(@RequestBody MiniUserTagsPo miniUserTagsPo) {
        tagsBiz.tags(miniUserTagsPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【定制酒PC-详情添加标签】", httpMethod = "POST")
    @PostMapping("/pc/detailTags")
    public ResponseBean<Void> detailTags(@RequestBody MiniUserTagsPo miniUserTagsPo) {
        tagsBiz.detailTags(miniUserTagsPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【定制酒PC-删除标签】", httpMethod = "POST")
    @PostMapping("/mini/delTag")
    public ResponseBean<Void> delTag(@RequestBody MinUserDelTagPo minUserDelTagPo) {
        tagsBiz.delTag(minUserDelTagPo);
        return ResponseBean.success();
    }
}