package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.pojo.page.PageParam;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.TerminateBiz;
import com.hishop.wine.biz.TerminateTypeBiz;
import com.hishop.wine.model.po.terminate.*;
import com.hishop.wine.model.vo.terminate.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 门店相关接口
 *
 * @author: LiGuoQiang
 * @date: 2023-07-10
 */

@Api(value = "TerminateController", tags = "门店相关接口")
@RestController
@RequestMapping("/terminate")
public class TerminateController {

    @Resource
    private TerminateBiz terminateBiz;

    @Resource
    private TerminateTypeBiz terminateTypeBiz;

    @ApiOperation(value = "保存门店类型", httpMethod = "POST")
    @PostMapping("/saveType")
    public ResponseBean<Void> saveType(@Valid @RequestBody TerminateTypeSavePo savePo) {
        terminateTypeBiz.save(savePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除门店类型", httpMethod = "POST")
    @PostMapping("/deleteType")
    public ResponseBean<Void> deleteType(@Valid @RequestBody IdPO<Long> idPo) {
        terminateTypeBiz.deleteById(idPo.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询门店类型列表-分页", httpMethod = "POST")
    @PostMapping("/queryTypePage")
    @AuthIgnore
    public ResponseBean<PageResult<TerminateTypeVo>> queryTypePage(@RequestBody PageParam pageParam) {
        return ResponseBean.success(terminateTypeBiz.pageList(pageParam));
    }

    @ApiOperation(value = "保存门店", httpMethod = "POST")
    @PostMapping("/save")
    @OperationLog
    public ResponseBean<Void> save(@Valid @RequestBody TerminateSavePo savePo) {
        terminateBiz.save(savePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "批量删除门店", httpMethod = "POST")
    @PostMapping("/batchDelete")
    @OperationLog
    public ResponseBean<Void> batchDelete(@RequestBody @Valid IdBatchPO<Long> idBatchPo) {
        terminateBiz.deleteById(idBatchPo.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "批量更新门店状态", httpMethod = "POST")
    @PostMapping("/batchUpdateStatus")
    @OperationLog
    public ResponseBean<Void> batchUpdateStatus(@RequestBody @Valid TerminateUpdateStatusPo updateStatusPo) {
        terminateBiz.batchUpdateStatus(updateStatusPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "根据ID获取门店详情", httpMethod = "GET")
    @GetMapping("/detail")
    public ResponseBean<TerminateDetailVo> detail(@RequestParam(name = "id") Long id) {
        return ResponseBean.success(terminateBiz.detail(id));
    }

    @ApiOperation(value = "分页获取门店列表", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<TerminateVo>> pageList(@RequestBody TerminateQueryPo pagePo) {
        return ResponseBean.success(terminateBiz.pageList(pagePo));
    }

    @ApiOperation(value = "导入门店", httpMethod = "POST")
    @PostMapping("/import")
    public ResponseBean<Void> importDealer(MultipartFile file) {
        terminateBiz.importTerminate(file);
        return ResponseBean.success();
    }

    @ApiOperation(value = "审核门店", httpMethod = "POST")
    @PostMapping("/audit")
    @OperationLog
    public ResponseBean<Void> audit(@Valid @RequestBody TerminateAuditPo auditPo) {
        terminateBiz.audit(auditPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "门店列表", httpMethod = "POST")
    @PostMapping("/queryTerminateList")
    public ResponseBean<List<TerminateFeignVo>> queryTerminateList(@Valid @RequestBody IdBatchPO<Long> idBatchPo) {
        return ResponseBean.success(terminateBiz.queryTerminateList(idBatchPo.getIds()));
    }

    @ApiOperation(value = "门店列表", httpMethod = "POST")
    @PostMapping("/queryTerminateByCodeList")
    public ResponseBean<List<TerminateFeignVo>> queryTerminateByCodeList(@Valid @RequestBody IdBatchPO<String> idBatchPo) {
        return ResponseBean.success(terminateBiz.queryTerminateByCodeList(idBatchPo.getIds()));
    }

    @ApiOperation(value = "门店列表", httpMethod = "GET")
    @GetMapping("/feign/detail")
    public ResponseBean<TerminateFeignDetailVo> feignDetail(@RequestParam(name = "id") Long id) {
        return ResponseBean.success(terminateBiz.feignDetail(id));
    }

    @ApiOperation(value = "小程序-查询手机号门店情况（返回null为没有注册记录，1审核通过 2审核不通过 3审核中）", httpMethod = "GET")
    @GetMapping("/min/queryAuditStatusByPhone")
    @OperationLog
    @AuthIgnore
    public ResponseBean<TerminateVo> queryAuditStatusByPhone(@RequestParam(name = "phone") String phone) {
        return ResponseBean.success(terminateBiz.queryAuditStatusByPhone(phone));
    }

    @ApiOperation(value = "min-注册门店", httpMethod = "POST")
    @PostMapping("/min/save")
    @OperationLog
    @AuthIgnore
    public ResponseBean<Void> minSave(@Valid @RequestBody TerminateMinSavePo savePo) {
        terminateBiz.minSave(savePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "min-查询门店类型列表-分页", httpMethod = "POST")
    @PostMapping("/min/queryTypePage")
    @AuthIgnore
    public ResponseBean<PageResult<TerminateTypeVo>> minQueryTypePage(@RequestBody PageParam pageParam) {
        return ResponseBean.success(terminateTypeBiz.pageList(pageParam));
    }

    @ApiOperation(value = "min-门店列表", httpMethod = "GET")
    @GetMapping("/min/queryTerminateByCodeList")
    public ResponseBean<List<TerminateVo>> queryTerminateByCodeListMin() {
        return ResponseBean.success(terminateBiz.queryTerminateByCodeListMin());
    }

    @ApiOperation(value = "min-查询当前登录用户是否是业务员", httpMethod = "GET")
    @GetMapping("/feign/queryUserIsSalesman")
    public ResponseBean<Boolean> queryUserIsSalesman(@RequestParam(name = "userId") Long userId, @RequestParam(name = "moduleCode") String moduleCode) {
        return ResponseBean.success(terminateBiz.queryUserIsSalesman(userId, moduleCode));
    }

    @ApiOperation(value = "根据电话号码查询门店", httpMethod = "GET")
    @GetMapping("/feign/queryTerminateByPhone")
    @OperationLog
    @AuthIgnore
    ResponseBean<TerminateFeignDetailVo> queryTerminateByPhone(@RequestParam(name = "phone") String phone) {
        return ResponseBean.success(terminateBiz.queryTerminateByPhone(phone));
    }

    @ApiOperation(value = "根据当前用户查询管辖的门店列表", httpMethod = "POST")
    @PostMapping("/mini/queryTerminateByUserPageList")
    public ResponseBean<PageResult<TerminateVo>> queryTerminateByUserPageList(@RequestBody PageParam pageParam) {
        return ResponseBean.success(terminateBiz.queryTerminateByUserPageList(pageParam));
    }

    @ApiOperation(value = "根据业务员id查询门店id集合", httpMethod = "GET")
    @GetMapping("/feign/listByBusinessUserId")
    ResponseBean<List<Long>> listByBusinessUserId(@RequestParam(name = "businessUserId") Long businessUserId) {
        return ResponseBean.success(terminateBiz.listByBusinessUserId(businessUserId));
    }
}
