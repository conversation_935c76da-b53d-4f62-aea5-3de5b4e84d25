package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.FrontCacheBiz;
import com.hishop.wine.model.po.FrontCacheSavePO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 前端临时缓存接口
 *
 * <AUTHOR>
 * @date : 2024/5/20
 */
@Api(value = "FrontCacheController", tags = "【前端临时缓存接口】")
@RestController
@RequestMapping("/frontCache")
public class FrontCacheController {

    @Resource
    private FrontCacheBiz frontCacheBiz;

    @ApiOperation(value = "前端缓存保存", httpMethod = "POST")
    @PostMapping({"/save"})
    @AuthIgnore
    public ResponseBean<Void> save(@RequestBody @Validated FrontCacheSavePO frontCacheSavePO) {
        frontCacheBiz.save(frontCacheSavePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取缓存内容", httpMethod = "GET")
    @GetMapping({"/get"})
    @AuthIgnore
    public ResponseBean<String> get(@RequestParam(name = "key") String key) {
        if (StringUtils.isBlank(key)) {
            throw new BusinessException("key不能为空");
        }
        return ResponseBean.success(frontCacheBiz.get(key));
    }

}
