package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.biz.PaymentSettingBiz;
import com.hishop.wine.common.enums.PaymentEnum;
import com.hishop.wine.model.po.payment.PaymentSettingSavePO;
import com.hishop.wine.model.vo.payment.PaymentSettingDetailVO;
import com.hishop.wine.model.vo.payment.PaymentSettingSelectVO;
import com.hishop.wine.model.vo.payment.PaymentSettingVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
* 支付设置 相关接口
* @author: HuBiao
* @date: 2023-07-18
*/

@Api(value = "PaymentSettingController",tags="【支付设置】" )
@RestController
@RequestMapping("/paymentSetting")
public class PaymentSettingController {

    @Resource
    private PaymentSettingBiz paymentSettingBiz;

    @ApiOperation(value = "获取支付类型", httpMethod = "GET")
    @GetMapping("/pc/listType")
    public ResponseBean<List<PaymentEnum.Type>> listType() {
        PaymentEnum.Type[] values = PaymentEnum.Type.values();
        return ResponseBean.success(Arrays.asList(values));
    }

    @ApiOperation(value = "上传支付证书", httpMethod = "POST")
    @PostMapping("/pc/uploadCertificate")
    public ResponseBean<Long> uploadCertificate(@RequestPart("file") MultipartFile file) {
        return ResponseBean.success(paymentSettingBiz.uploadCertificate(file));
    }

    @ApiOperation(value = "新增支付设置", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean<Void> create(@Valid @RequestBody PaymentSettingSavePO paymentSettingSavePO) {
        paymentSettingBiz.create(paymentSettingSavePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "编辑支付设置", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean<Void> update(@Valid @RequestBody PaymentSettingSavePO paymentSettingSavePO) {
        paymentSettingBiz.update(paymentSettingSavePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除支付设置", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        paymentSettingBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "支付设置详情", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<PaymentSettingDetailVO> detail(@RequestParam(name = "id") Long id) {
        return ResponseBean.success(paymentSettingBiz.detail(id));
    }

    @ApiOperation(value = "支付设置列表", httpMethod = "GET")
    @GetMapping("/pc/list")
    public ResponseBean<List<PaymentSettingVO>> list() {
        return ResponseBean.success(paymentSettingBiz.list());
    }

    @ApiOperation(value = "支付设置下拉", httpMethod = "GET")
    @ApiImplicitParam(name = "group", value = "支付类型分组", dataType = "String", paramType = "query",example = "ONLINE,OFFLINE")
    @GetMapping("/pc/listForSelect")
    public ResponseBean<List<PaymentSettingSelectVO>> listForSelect(@RequestParam(required = false) String group) {
        return ResponseBean.success(paymentSettingBiz.listForSelect(group));
    }
}