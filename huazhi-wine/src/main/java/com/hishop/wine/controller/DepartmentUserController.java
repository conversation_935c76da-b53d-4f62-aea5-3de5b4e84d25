package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.DepartmentBiz;
import com.hishop.wine.model.po.DepartmentUpdateIdPO;
import com.hishop.wine.model.vo.department.DepartmentVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
* 部门用户表 相关接口
* @Author: chenpeng
* @since: 2023-05-17 10:50:00
*/

@Api(value = "DepartmentUserController",tags="部门用户表相关接口" )
@RestController
@RequestMapping("/departmentUser")
public class DepartmentUserController {

    @Resource
    private DepartmentBiz departmentBiz;


    @ApiOperation(value = "用户查询部门信息", httpMethod = "GET")
    @GetMapping({"/getDepartmentUser"})
    public ResponseBean<DepartmentVO> getDepartmentUser(@RequestParam Long userId) {
        return ResponseBean.success(departmentBiz.getDepartmentUser(userId));
    }


    @ApiOperation(value = "修改用户部门、批量", httpMethod = "POST")
    @PostMapping({"/updateDep"})
    public ResponseBean<Boolean> updateDep(@RequestBody @Validated DepartmentUpdateIdPO updatePO) {
        updatePO.setIzHead(Boolean.FALSE);
        return ResponseBean.success(departmentBiz.updateDep(updatePO));
    }

}