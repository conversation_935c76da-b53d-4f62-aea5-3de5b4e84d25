package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.NfsBiz;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.Map;

/**
 * 文件上传接口
 *
 * <AUTHOR>
 * @date : 2023/7/5
 */
@Api(value = "NFSController", tags = "【文件上传】")
@RestController
@RequestMapping("/nfs")
public class NfsController {

    @Resource
    private NfsBiz nfsBiz;

    @ApiOperation(value = "获取上传凭据", httpMethod = "GET")
    @GetMapping({"/getTemporaryCredentials"})
    //@AuthIgnore
    public ResponseBean<Map<String, String>> getTemporaryCredentials() {
        return ResponseBean.success(nfsBiz.getTemporaryCredentials());
    }

    @ApiOperation(value = "文件上传（图片、视频、音频）", httpMethod = "POST")
    @PostMapping({"/upload"})
    @AuthIgnore
    public ResponseBean<String> upload(@RequestPart("file") MultipartFile file) {
        return ResponseBean.success(nfsBiz.upload(file));
    }

}
