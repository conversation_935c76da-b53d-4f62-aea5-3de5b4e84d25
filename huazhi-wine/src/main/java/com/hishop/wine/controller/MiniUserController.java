package com.hishop.wine.controller;

import cn.binarywang.wx.miniapp.bean.WxMaRunStepInfo;
import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.MiniUserBiz;
import com.hishop.wine.common.annotation.SwitchWxMini;
import com.hishop.wine.model.po.basic.MiniUserSavePO;
import com.hishop.wine.model.po.basic.MobileSendPO;
import com.hishop.wine.model.po.login.UserMiniAuthLoginPO;
import com.hishop.wine.model.po.login.UserMiniDefaultLoginPO;
import com.hishop.wine.model.po.login.UserMiniMobileLoginPO;
import com.hishop.wine.model.po.login.UserRefreshTokenPO;
import com.hishop.wine.model.po.minUser.*;
import com.hishop.wine.model.po.miniApp.MiniEncryptDataPO;
import com.hishop.wine.model.vo.basic.MiniUserDetailVO;
import com.hishop.wine.model.vo.basic.MiniUserVO;
import com.hishop.wine.model.vo.login.MiniLoginUserVO;
import com.hishop.wine.model.vo.login.MiniLoginVO;
import com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;

/**
 * 小程序用户表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-21
 */
@Api(value = "MiniUserController", tags = "【小程序用户】")
@RestController
@RequestMapping("/user")
public class MiniUserController {

    @Resource
    private MiniUserBiz miniUserBiz;

    @ApiOperation(value = "mini-微信授权登录(自动注册)", httpMethod = "POST")
    @PostMapping({"/mini/authLogin"})
    @RejectRepeat
    @AuthIgnore
    public ResponseBean<MiniLoginVO> wxAuthLogin(@RequestBody @Valid UserMiniAuthLoginPO authLoginPO) {
        return ResponseBean.success(miniUserBiz.wxAuthLogin(authLoginPO));
    }

    @ApiOperation(value = "mini-微信授权登录(自动注册)2", httpMethod = "POST")
    @PostMapping({"/mini/authLogin2"})
    @RejectRepeat
    @AuthIgnore
    public ResponseBean<MiniLoginVO> wxAuthLogin2(@RequestBody @Valid UserMiniAuthLoginPO authLoginPO) {
        return ResponseBean.success(miniUserBiz.wxAuthLogin2(authLoginPO));
    }

    @ApiOperation(value = "mini-手机号登录(自动注册)", httpMethod = "POST")
    @PostMapping({"/mini/mobileLogin"})
    @RejectRepeat
    @AuthIgnore
    public ResponseBean<MiniLoginVO> mobileLogin(@RequestBody @Valid UserMiniMobileLoginPO mobileLoginPO) {
        return ResponseBean.success(miniUserBiz.mobileLogin(mobileLoginPO, Boolean.TRUE));
    }

    @ApiOperation(value = "mini-发送登录验证码", httpMethod = "POST")
    @PostMapping({"/mini/mobile/sendForLogin"})
    @RejectRepeat
    @AuthIgnore
    public ResponseBean sendForLogin(@RequestBody @Valid MobileSendPO mobileSendPO) {
        miniUserBiz.sendForLogin(mobileSendPO.getMobile());
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-静默登录", httpMethod = "POST")
    @PostMapping({"/mini/wxDefaultLogin"})
    @RejectRepeat
    public ResponseBean<MiniLoginVO> wxDefaultLogin(@RequestBody @Valid UserMiniDefaultLoginPO defaultLoginPO) {
        return ResponseBean.success(miniUserBiz.wxDefaultLogin(defaultLoginPO));
    }

    @ApiOperation(value = "mini-刷新token", httpMethod = "POST")
    @PostMapping("/mini/refreshToken")
    @AuthIgnore
    public ResponseBean<MiniLoginVO> refreshToken(@RequestBody @Valid UserRefreshTokenPO refreshTokenParam) {
        return ResponseBean.success(miniUserBiz.refreshToken(refreshTokenParam));
    }

    /**
     * 根据userId获取小程序用户信息
     *
     * <AUTHOR>
     * @date 2023/6/29
     */
    @ApiOperation(value = "feign-根据userId列表获取小程序用户信息", httpMethod = "POST", hidden = true)
    @PostMapping({"/mini/getMiniUser"})
    public ResponseBean<List<MiniUserVO>> getMiniUser(@RequestBody List<Long> userIdList) {
        List<MiniUserVO> list = miniUserBiz.getMiniUser(userIdList);
        return ResponseBean.success(list);
    }

    @ApiOperation(value = "feign-根据userId获取单个小程序用户信息", httpMethod = "GET", hidden = true)
    @GetMapping({"/mini/getMiniUserById"})
    public ResponseBean<MiniUserVO> getMiniUserById(@RequestParam("miniUserId") Long miniUserId) {
        List<MiniUserVO> list = miniUserBiz.getMiniUser(Collections.singletonList(miniUserId));
        return ResponseBean.success(CollectionUtils.isEmpty(list) ? null : list.get(0));
    }

    @ApiOperation(value = "feign-根据userId与模块code获取单个小程序用户信息", httpMethod = "GET", hidden = true)
    @GetMapping({"/mini/getMiniUserByUserIdAndModuleCode"})
    public ResponseBean<MiniUserVO> getMiniUserByUserIdAndModuleCode(@RequestParam("userId") Long userId, @RequestParam("moduleCode") String moduleCode) {
        return ResponseBean.success(miniUserBiz.getMiniUserByUserIdAndModuleCode(userId, moduleCode));
    }

    @ApiOperation(value = "mini-保存用户昵称和头像", httpMethod = "POST")
    @PostMapping({"/mini/saveUserData"})
    public ResponseBean saveUserData(@RequestBody @Valid MiniUserSavePO miniUserSavePO) {
        miniUserBiz.saveUserData(miniUserSavePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-获取登录用户信息", httpMethod = "GET")
    @GetMapping({"/mini/userInfo"})
    public ResponseBean<MiniLoginUserVO> getMiniUserInfo() {
        MiniLoginUserVO miniUserVO = miniUserBiz.getMiniUserInfo();
        return ResponseBean.success(miniUserVO);
    }

    @ApiOperation(value = "mini-获取用户协议/隐私政策", httpMethod = "GET")
    @GetMapping({"/mini/getAgreement"})
    @AuthIgnore
    public ResponseBean<String> getAgreement(@RequestParam("type") @ApiParam("type 1-用户协议 2-隐私政策") Integer type) {
        return ResponseBean.success(miniUserBiz.getAgreement(type));
    }

    /**
     * 获取微信步数
     *
     * <AUTHOR>
     * @date 2023/7/25
     */
    @SwitchWxMini
    @ApiOperation(value = "mini-获取微信步数", httpMethod = "POST")
    @PostMapping({"/mini/getRunStep"})
    public ResponseBean<List<WxMaRunStepInfo>> getRunStep(@RequestBody MiniEncryptDataPO encryptDataPo) {
        return ResponseBean.success(miniUserBiz.getRunStep(encryptDataPo));
    }

    @ApiOperation(value = "【PC-客户管理分页】", httpMethod = "POST")
    @PostMapping("/mini/pageList")
    public ResponseBean<PageResult<MiniUserVO>> pageList(@RequestBody MiniUserQueryPo miniUserQueryPo) {
        return ResponseBean.success(miniUserBiz.pageList(miniUserQueryPo));
    }

    @ApiOperation(value = "【PC-客户详情】", httpMethod = "GET")
    @GetMapping("/mini/getUserDetail")
    @OperationLog(queryMethod = "getUserDetail", operationType = OperationType.QUERY)
    public ResponseBean<MiniUserDetailVO> getUserDetail(@RequestParam(name = "id") Long id) {
        return ResponseBean.success(miniUserBiz.getUserDetail(id));
    }

    @ApiOperation(value = "【PC-备注名】", httpMethod = "POST")
    @PostMapping("/mini/remarkName")
    public ResponseBean<Void> remarkName(@RequestBody MiniUserRemarkNamePo miniUserRemarkNamePo) {
        miniUserBiz.remarkName(miniUserRemarkNamePo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-批量处理黑名单】", httpMethod = "POST")
    @PostMapping("/mini/blackList")
    public ResponseBean<Void> blackList(@RequestBody MiniUserBlacklistPo miniUserBlacklistPo) {
        miniUserBiz.blackList(miniUserBlacklistPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-批量添加标签】", httpMethod = "POST")
    @PostMapping("/mini/tags")
    public ResponseBean<Void> tags(@RequestBody MiniUserTagsPo miniUserTagsPo) {
        miniUserBiz.tags(miniUserTagsPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "【PC-黑名单分页】", httpMethod = "POST")
    @PostMapping("/mini/blacklistPage")
    public ResponseBean<PageResult<MiniUserBlacklistVo>> blacklistPage(@RequestBody MiniUserBlacklistQueryPo miniUserBlacklistQueryPo) {
        return ResponseBean.success(miniUserBiz.blacklistPage(miniUserBlacklistQueryPo));
    }

    @ApiOperation(value = "【PC-删除标签】", httpMethod = "POST")
    @PostMapping("/mini/delTag")
    public ResponseBean<Void> delTag(@RequestBody MinUserDelTagPo minUserDelTagPo) {
        miniUserBiz.delTag(minUserDelTagPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "mini-微信获取手机号码", httpMethod = "POST")
    @PostMapping({"/mini/getPhoneByWx"})
    @RejectRepeat
    @AuthIgnore
    public ResponseBean<String> getPhoneByWx(@RequestBody @Valid UserMiniAuthLoginPO authLoginPO) {
        return ResponseBean.success(miniUserBiz.getPhoneByWx(authLoginPO));
    }
}