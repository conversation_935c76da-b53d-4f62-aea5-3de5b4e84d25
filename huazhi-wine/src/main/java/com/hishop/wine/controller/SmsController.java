package com.hishop.wine.controller;

import com.hishop.common.response.PageResult;
import com.hishop.wine.biz.SmsBiz;
import com.hishop.wine.model.po.sms.SendSmsPO;
import com.hishop.wine.model.po.sms.SmsRecordQueryPO;
import com.hishop.wine.model.vo.sms.SmsRecordVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.biz.SmsRecordBiz;
import org.springframework.web.bind.annotation.*;
import com.hishop.common.response.ResponseBean;
import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
* 短信发送记录表 相关接口
* @author: HuBiao
* @date: 2023-07-12
*/

@Api(value = "SmsController",tags="【短信接口】" )
@RestController
@RequestMapping("/sms")
public class SmsController {

    @Resource
    private SmsRecordBiz smsRecordBiz;
    @Resource
    private SmsBiz smsBiz;

    @ApiOperation(value = "查询短信余额", httpMethod = "GET")
    @GetMapping("/getBalance")
    public ResponseBean<Integer> getBalance() {
        return ResponseBean.success(smsBiz.getBalance());
    }


    @ApiOperation(value = "发送短信", httpMethod = "POST")
    @PostMapping("/send")
    public ResponseBean sendSms(@RequestBody @Valid SendSmsPO sendSmsPO) {
        smsBiz.sendSms(sendSmsPO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "查询发送记录", httpMethod = "POST")
    @PostMapping("/record/pageList")
    public ResponseBean<PageResult<SmsRecordVO>> pageList(@RequestBody SmsRecordQueryPO pagePO) {
        return ResponseBean.success(smsRecordBiz.pageList(pagePO));
    }
}