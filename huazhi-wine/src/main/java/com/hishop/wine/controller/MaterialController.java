package com.hishop.wine.controller;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Strings;
import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;
import com.hishop.common.util.LoginUserUtil;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.model.po.material.*;
import com.hishop.wine.repository.dto.MaterialDTO;
import com.hishop.wine.repository.dto.MaterialTransCodeResult;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.biz.MaterialBiz;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.Map;

import com.hishop.common.response.ResponseBean;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 资源库表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-17
 */
@Api(value = "MaterialController", tags = "【素材库管理】")
@RestController
@RequestMapping("/material")
public class MaterialController {

    @Resource
    private MaterialBiz materialBiz;

//    @ApiOperation("PC-上传资源文件")
//    @PostMapping(path = "/pc/create")
//    public ResponseBean<Map<String, String>> uploadMaterial(@RequestBody @Valid MaterialCreatePO materialCreatePO) {
//        Map<String, String> uploadMap = materialBiz.requestMaterialUpload(materialCreatePO.getMaterialCategoryId(), materialCreatePO.getTitle(), materialCreatePO.getBannerPath());
//        return ResponseBean.success(uploadMap);
//    }

    @ApiOperation("PC-上传图片文件")
    @PostMapping(path = "/pc/create")
    @OperationLog
    public ResponseBean<MaterialDTO> uploadMaterial(@RequestBody @Valid ImageCreatePO imageCreatePO) {
        MaterialDTO result = materialBiz.requestImageCreate(imageCreatePO.getMaterialCategoryId(), imageCreatePO.getTitle(), imageCreatePO.getPath(), imageCreatePO.getFileSize());
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-上传视频文件")
    @PostMapping(path = "/pc/media/create")
    @OperationLog
    public ResponseBean<MaterialDTO> uploadMaterial(@RequestBody @Valid MaterialCreatePO materialCreatePO) {
        MaterialDTO result = materialBiz.requestMaterialUpload(materialCreatePO.getMaterialCategoryId(), materialCreatePO.getTitle(),
                materialCreatePO.getPath(), materialCreatePO.getBannerPath(), materialCreatePO.getFileSize(), materialCreatePO.getDuration(), false);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-上传音频文件")
    @PostMapping(path = "/pc/audio/create")
    @OperationLog
    public ResponseBean<MaterialDTO> uploadAudio(@RequestBody @Valid MaterialCreatePO materialCreatePO) {
        MaterialDTO result = materialBiz.requestMaterialUpload(materialCreatePO.getMaterialCategoryId(), materialCreatePO.getTitle(),
                materialCreatePO.getPath(), materialCreatePO.getBannerPath(), materialCreatePO.getFileSize(), materialCreatePO.getDuration(), true);
        return ResponseBean.success(result);
    }

    @ApiOperation(value = "PC-修改资源文件")
    @PostMapping(path = "/pc/update")
    @OperationLog(operationType = OperationType.CREATE)
    public ResponseBean updateMaterial(@RequestBody @Valid MaterialUpdatePO materialUpdatePO) {
        materialBiz.updateMaterial(materialUpdatePO.getId(), materialUpdatePO.getMaterialCategoryId(), materialUpdatePO.getTitle());
        return ResponseBean.success();
    }

    @ApiOperation("PC-上传成功资源(图片、视频、音频)成功回调接口")
    @PostMapping(path = "/pc/success")
    public ResponseBean uploadSuccess(@RequestBody @Valid MaterialUploadSuccessPO materialUploadSuccessPO) {
        materialBiz.uploadSuccess(materialUploadSuccessPO.getPath(), materialUploadSuccessPO.getSize(), materialUploadSuccessPO.getDuration());
        return ResponseBean.success();
    }

    @ApiOperation("PC-批量变更资源所属分组")
    @PostMapping(path = "/pc/category")
    @OperationLog(operationType = OperationType.CREATE)
    public ResponseBean changeMaterialCategory(@RequestBody @Valid MaterialChangeCategoryPO materialChangeCategoryPO) {
        materialBiz.changeMaterialCategory(materialChangeCategoryPO.getNewMaterialCategoryId(), materialChangeCategoryPO.getIds());
        return ResponseBean.success();
    }

    @ApiOperation("PC-查询资源")
    @PostMapping(path = "/pc/query")
    public ResponseBean<PageResult<MaterialDTO>> getMaterials(@RequestBody @Valid MaterialQueryPO query) {
        PageResult<MaterialDTO> materials = materialBiz.getMaterials(query);
        return ResponseBean.success(materials);
    }

//    @ApiOperation("PC-删除指定资源")
//    @PostMapping(path = "/pc/delete")
//    public ResponseBean deleteById(@RequestBody @Valid IdPO<Long> idPO) {
//        materialBiz.deleteMaterialsByIds(Arrays.asList(idPO.getId()));
//        return ResponseBean.success();
//    }


    @ApiOperation("PC-批量删除资源")
    @PostMapping(path = "/pc/deleteBatch")
    @OperationLog(operationType = OperationType.CREATE)
    public ResponseBean deleteByIds(@RequestBody @Valid MaterialDeletePO idBatchPO) {
        materialBiz.deleteMaterialsByIds(idBatchPO.getIds(), idBatchPO.isRemoveFiles());
        return ResponseBean.success();
    }

    // 仅测试时使用，其他时候请注释
//    @ApiOperation("提交转码任务")
//    @PostResource(path = "/transcode", requiredLogin = false, requiredPermission = false)
//    public ResponseData transcode(@RequestParam @ApiParam("提交转码任务") String path){
//        nfs.submitTransCodeJob(path,path +".mp4");
//        return new ResponseData<>();
//    }

    @ApiOperation("PC-接收转码回调")
    @PostMapping(path = "/pc/transcode/state")
    @AuthIgnore
    public ResponseBean transcodeCallBack(@RequestBody  ObsConvertMassagePO  convertMediaPO) throws JsonProcessingException {
        try{
            System.out.println("通知消息体:"+convertMediaPO.getMessage());
            if (convertMediaPO.getSubject().equals("TranscodeComplete")){
                ConvertMediaPO convertMedia =new ObjectMapper().readValue(convertMediaPO.getMessage(),ConvertMediaPO.class);
                if(convertMedia.getEventType().equals("TranscodeComplete")){
                    if (convertMedia.getTranscodeInfo().getStatus().equalsIgnoreCase("SUCCEEDED")){
                        materialBiz.setTranscodeStatus(convertMedia.getTranscodeInfo().getTaskId(), true);
                    }else {
                        materialBiz.setTranscodeStatus(convertMedia.getTranscodeInfo().getTaskId(), false);
                    }
                }
            }else if (convertMediaPO.getSubject().equals("ThumbnailComplete")){
                ConvertMediaPO convertMedia =new ObjectMapper().readValue(convertMediaPO.getMessage(),ConvertMediaPO.class);
                if (convertMedia.getTranscodeInfo().getStatus().equalsIgnoreCase("SUCCEEDED")){
//                    materialBiz.setTranscodeStatus(convertMedia.get().getTaskId(), true);
                }else {
//                    materialBiz.setTranscodeStatus(convertMedia.getTranscodeInfo().getTaskId(), false);
                }
            }


            return ResponseBean.success();
        }catch (Exception ex){
            throw  ex;
        }

    }

}