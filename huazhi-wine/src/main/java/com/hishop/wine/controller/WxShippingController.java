package com.hishop.wine.controller;

import com.hishop.common.response.ResponseBean;
import com.hishop.wine.api.WxShippingFeign;
import com.hishop.wine.biz.WxShippingBiz;
import com.hishop.wine.model.po.wxShipping.request.GetOrderRequest;
import com.hishop.wine.model.po.wxShipping.request.UploadShippingRequest;
import com.hishop.wine.model.po.wxShipping.response.DeliveryListResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @Date 2025/04/01/ $
 * @description:
 */
@Api(value = "WxShippingController",tags="【微信发货相关】" )
@RestController
@RequestMapping("/wxshipping")
@Slf4j
public class WxShippingController implements WxShippingFeign {

    @Resource
    private WxShippingBiz wxShippingBiz;

    @ApiOperation(value = "查询小程序是否已开通发货信息管理服务", httpMethod = "GET")
    @GetMapping("/isTradeManaged")
    @Override
    public ResponseBean<Boolean> isTradeManaged(@RequestParam("appId") String appId) {
        return ResponseBean.success(wxShippingBiz.isTradeManaged(appId));
    }

    @ApiOperation(value = "发货信息录入接口", httpMethod = "POST")
    @PostMapping("/uploadShipping")
    @Override
    public ResponseBean<Boolean> uploadShipping(@RequestBody @Validated UploadShippingRequest request) {
        return ResponseBean.success(wxShippingBiz.uploadShipping(request));
    }

    @ApiOperation(value = "查询订单发货状态", httpMethod = "POST")
    @PostMapping("/getOrder")
    @Override
    public ResponseBean<Boolean> getOrder(@RequestBody @Validated GetOrderRequest request) {
        return ResponseBean.success(wxShippingBiz.getOrder(request));
    }

    @ApiOperation(value = "获取运力id列表", httpMethod = "GET")
    @GetMapping("/getDeliveryList")
    @Override
    public ResponseBean<DeliveryListResponse> getDeliveryList(@RequestParam("appId") String appId) {
        return ResponseBean.success(wxShippingBiz.getDeliveryList(appId));
    }

    @ApiOperation(value = "根据appId进行发货信息录入接口", httpMethod = "POST")
    @PostMapping("/uploadShippingByAppId")
    @Override
    public ResponseBean<Boolean> uploadShippingByAppId(@RequestBody @Validated UploadShippingRequest request) {
        return ResponseBean.success(wxShippingBiz.uploadShippingByAppId(request));
    }

}
