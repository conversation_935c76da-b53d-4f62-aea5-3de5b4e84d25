package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.annotation.RejectRepeat;
import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.api.TransactionFeign;
import com.hishop.wine.model.po.transaction.TransactionPayPO;
import com.hishop.wine.model.po.transaction.TransactionQueryPO;
import com.hishop.wine.model.po.transaction.TransactionRefundPO;
import com.hishop.wine.model.po.transaction.TransactionEntPayPO;
import com.hishop.wine.model.vo.transaction.TransactionEntPayVO;
import com.hishop.wine.model.vo.transaction.TransactionInfoVO;
import com.hishop.wine.model.vo.transaction.TransactionPayVO;
import com.hishop.wine.model.vo.transaction.TransactionRefundVO;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;
import com.hishop.wine.biz.TransactionBiz;

import javax.annotation.Resource;
import javax.validation.Valid;

import io.swagger.annotations.Api;

/**
 * 交易流水表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-06-28
 */
@Api(value = "TransactionController", tags = "【交易模块】")
@RestController
@RequestMapping("/transaction")
public class TransactionController implements TransactionFeign {

    @Resource
    private TransactionBiz transactionBiz;

    @ApiOperation("发起支付")
    @PostMapping("/pay")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionPayVO> pay(@RequestBody @Valid TransactionPayPO payPO) {
        return ResponseBean.success(transactionBiz.pay(payPO));
    }

    @ApiOperation("判断是否支付中")
    @PostMapping("/queryPaying")
    @Override
    @RejectRepeat
    public ResponseBean<Boolean> queryPaying(@RequestBody @Valid TransactionQueryPO payPO) {
        return ResponseBean.success(transactionBiz.queryPaying(payPO));
    }

    @ApiOperation(value = "支付回调")
    @PostMapping("/notify/pay/{moduleCode}")
    @AuthIgnore
    public String notifyPay(@PathVariable("moduleCode") String moduleCode, @RequestBody String notifyData) {
        return transactionBiz.notifyPay(moduleCode, notifyData);
    }

    @ApiOperation("发起退款")
    @PostMapping("/refund")
    @Override
    @RejectRepeat
    public ResponseBean<TransactionRefundVO> refund(@RequestBody @Valid TransactionRefundPO refundPO) {
        return ResponseBean.success(transactionBiz.refund(refundPO));
    }

    @ApiOperation(value = "退款回调")
    @PostMapping("/notify/refund/{moduleCode}")
    @RejectRepeat
    @AuthIgnore
    public String notifyRefund(@PathVariable("moduleCode") String moduleCode, @RequestBody String notifyData) {
        return transactionBiz.notifyRefund(moduleCode, notifyData);
    }

    @ApiOperation("企业付款到零钱")
    @PostMapping("/entPay")
    @Override
    public ResponseBean<TransactionEntPayVO> entPay(@RequestBody @Valid TransactionEntPayPO entPayPO) {
        return ResponseBean.success(transactionBiz.entPay(entPayPO));
    }

    @ApiOperation("交易查询")
    @PostMapping("/query")
    @Override
    public ResponseBean<PageResult<TransactionInfoVO>> queryTransaction(@RequestBody @Valid TransactionQueryPO queryPO) {
        return ResponseBean.success(transactionBiz.queryTransaction(queryPO));
    }

    @ApiOperation("交易查询")
    @GetMapping("/getMchId")
    @Override
    public ResponseBean<String> getMchId(@RequestParam(name = "id") Long id) {
        return ResponseBean.success(transactionBiz.getMchId(id));
    }
}