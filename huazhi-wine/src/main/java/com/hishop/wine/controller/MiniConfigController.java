package com.hishop.wine.controller;

import com.hishop.common.annotation.AuthIgnore;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.MiniConfigBiz;
import com.hishop.wine.model.vo.MiniInitConfigVO;
import com.hishop.wine.model.vo.PcInitConfigVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 小程序配置相关接口
 *
 * @author: chenpeng
 * @date: 2023-07-11
 */
@Api(value = "MiniConfigController", tags = "【小程序配置】")
@RestController
@RequestMapping("/config")
public class MiniConfigController {

    @Resource
    private MiniConfigBiz miniConfigBiz;

    @ApiOperation(value = "mini-获取小程序初始化信息", httpMethod = "GET")
    @GetMapping("/mini/getInitConfig")
    @AuthIgnore
    public ResponseBean<MiniInitConfigVO> getInitConfig(@RequestParam("appId") String appId) {
        return ResponseBean.success(miniConfigBiz.getInitConfig(appId));
    }

    @ApiOperation(value = "PC-获取PC网站初始化信息", httpMethod = "GET")
    @GetMapping("/pc/getInit")
    @AuthIgnore
    public ResponseBean<PcInitConfigVO> getInit() {
        return ResponseBean.success(miniConfigBiz.getInitForPC());
    }
}