package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.wine.model.po.DepartmentEmployeeHeaderPO;
import com.hishop.wine.model.po.DepartmentUpdateMovePO;
import com.hishop.wine.model.vo.department.DepartmentVO;
import io.swagger.annotations.ApiImplicitParam;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.model.po.DepartmentCreatePO;
import com.hishop.wine.model.po.DepartmentUpdatePO;
import com.hishop.wine.biz.DepartmentBiz;
import com.hishop.common.response.ResponseBean;
import org.springframework.web.bind.annotation.*;
import javax.annotation.Resource;
import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
* 部门表 相关接口
* @Author: chenpeng
* @since: 2023-05-17 10:50:00
*/

@Api(value = "DepartmentController",tags="部门表相关接口" )
@RestController
@RequestMapping("/department")
public class DepartmentController {

    @Resource
    private DepartmentBiz departmentBiz;

    @ApiOperation(value = "新增部门", httpMethod = "POST")
    @PostMapping({"/create"})
    public ResponseBean create(@Validated @RequestBody DepartmentCreatePO departmentCreatePO) {
        departmentBiz.create(departmentCreatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "编辑部门", httpMethod = "POST")
    @PostMapping({"/update"})
    public ResponseBean update(@Validated @RequestBody DepartmentUpdatePO departmentUpdatePO) {
        departmentBiz.update(departmentUpdatePO);
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "删除部门", httpMethod = "POST")
    @PostMapping({"/delete"})
    public ResponseBean delete(@RequestBody IdPO<Long> idPO) {
        departmentBiz.deleteById(idPO.getId());
        return ResponseBean.success(null);
    }

    @ApiOperation(value = "获取部门树", httpMethod = "POST")
    @PostMapping({"/tree"})
    public ResponseBean<List<DepartmentVO>> tree() {
        return ResponseBean.success(departmentBiz.tree());
    }

    @ApiOperation(value = "设置部门负责人", httpMethod = "POST")
    @PostMapping({"/setHeader"})
    public ResponseBean setHeader(@Validated @RequestBody DepartmentEmployeeHeaderPO headerPO) {
        departmentBiz.setHeader(headerPO);
        return ResponseBean.success();
    }


    @ApiOperation(value = "根据部门ID向上平铺查找部门信息", httpMethod = "GET")
    @GetMapping({"/getDepParentUp"})
    @ApiImplicitParam(name = "departmentId", value = "部门id", required = true)
    public ResponseBean<List<DepartmentVO>> getDepParentUp(@RequestParam  Long departmentId){
        return ResponseBean.success(departmentBiz.getDepParentUp(departmentId));
    }


    @ApiOperation(value = "部门移动", httpMethod = "POST")
    @PostMapping({"/updateMove"})
    public ResponseBean updateMove(@Validated @RequestBody DepartmentUpdateMovePO movePO) {
        return ResponseBean.success(departmentBiz.updateMove(movePO));
    }

	
}