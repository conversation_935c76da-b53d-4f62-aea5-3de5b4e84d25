package com.hishop.wine.controller;

import com.hishop.log.annotation.OperationLog;
import com.hishop.wine.model.vo.address.BaseAddressSimpleVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.hishop.wine.model.po.address.BaseAddressCreatePO;
import com.hishop.wine.model.po.address.BaseAddressUpdatePO;
import com.hishop.wine.model.po.address.BaseAddressQueryPO;
import com.hishop.wine.model.vo.address.BaseAddressVO;
import com.hishop.wine.biz.BaseAddressBiz;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 地址库表 相关接口
 *
 * @author: HuBia<PERSON>
 * @date: 2023-07-17
 */
@Api(value = "BaseAddressController", tags = "【PC-地址库管理】")
@RestController
@RequestMapping("/baseAddress")
public class BaseAddressController {

    @Resource
    private BaseAddressBiz baseAddressBiz;

    @ApiOperation(value = "新增地址", httpMethod = "POST")
    @PostMapping("/pc/create")
    @OperationLog
    public ResponseBean<Long> create(@Valid @RequestBody BaseAddressCreatePO baseAddressCreatePO) {
        return ResponseBean.success(baseAddressBiz.create(baseAddressCreatePO));
    }

    @ApiOperation(value = "编辑地址", httpMethod = "POST")
    @PostMapping("/pc/update")
    @OperationLog
    public ResponseBean<Void> update(@Valid @RequestBody BaseAddressUpdatePO baseAddressUpdatePO) {
        baseAddressBiz.update(baseAddressUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除地址", httpMethod = "POST")
    @PostMapping("/pc/delete")
    @OperationLog
    public ResponseBean<Void> delete(@RequestBody IdPO<Long> idPO) {
        baseAddressBiz.deleteById(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取地址详情", httpMethod = "GET")
    @GetMapping("/pc/detail")
    public ResponseBean<BaseAddressVO> detail(@RequestParam(name = "id") Long id) {
        BaseAddressVO detail = baseAddressBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "获取地址列表(用户下拉选择)", httpMethod = "POST")
    @PostMapping("/pc/list")
    public ResponseBean<List<BaseAddressSimpleVO>> list(@RequestBody BaseAddressQueryPO qryPO) {
        return ResponseBean.success(baseAddressBiz.list(qryPO));
    }

    @ApiOperation(value = "分页查询地址列表", httpMethod = "POST")
    @PostMapping("/pc/pageList")
    public ResponseBean<PageResult<BaseAddressSimpleVO>> pageList(@RequestBody BaseAddressQueryPO pagePO) {
        return ResponseBean.success(baseAddressBiz.pageList(pagePO));
    }
}