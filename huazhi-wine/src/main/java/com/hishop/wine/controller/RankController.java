package com.hishop.wine.controller;

import com.hishop.common.pojo.IdBatchPO;
import com.hishop.common.pojo.IdStatusPo;
import com.hishop.wine.api.RankFeign;
import com.hishop.wine.biz.RankBiz;
import com.hishop.wine.model.po.rank.RankCreatePO;
import com.hishop.wine.model.po.rank.RankQueryPO;
import com.hishop.wine.model.po.rank.RankSelectPO;
import com.hishop.wine.model.po.rank.RankUpdatePO;
import com.hishop.wine.model.vo.rank.RankSelectVO;
import com.hishop.wine.model.vo.rank.RankVO;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

import com.hishop.common.response.ResponseBean;
import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.PageResult;

import javax.annotation.Resource;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;

/**
 * 头衔表 相关接口
 *
 * @author: HuBiao
 * @date: 2023-07-25
 */

@Api(value = "RankController", tags = "【头衔相关】")
@RestController
@RequestMapping("/rank")
public class RankController implements RankFeign {

    @Resource
    private RankBiz rankBiz;

    @ApiOperation(value = "新增头衔", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/create")
    @Override
    public ResponseBean<Long> create(@Valid @RequestBody RankCreatePO rankCreatePO) {
        return ResponseBean.success(rankBiz.create(rankCreatePO));
    }

    @ApiOperation(value = "编辑头衔", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/update")
    @Override
    public ResponseBean<Void> update(@Valid @RequestBody RankUpdatePO rankUpdatePO) {
        rankBiz.update(rankUpdatePO);
        return ResponseBean.success();
    }

    @ApiOperation(value = "删除头衔", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/delete")
    @Override
    public ResponseBean<Void> delete(@RequestBody @Valid IdBatchPO<Long> idPO) {
        rankBiz.deleteByIdS(idPO.getIds());
        return ResponseBean.success();
    }

    @ApiOperation(value = "修改头衔状态", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/status")
    @Override
    public ResponseBean<Void> changeStatus(@RequestBody @Valid IdStatusPo<Long> idPO) {
        rankBiz.changeStatus(idPO.getIds(), idPO.getStatus());
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取头衔详情", httpMethod = "GET", hidden = true)
    @GetMapping("/pc/detail")
    @Override
    public ResponseBean<RankVO> detail(@RequestParam(name = "id") Long id) {
        RankVO detail = rankBiz.detail(id);
        return ResponseBean.success(detail);
    }

    @ApiOperation(value = "获取头衔列表", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/list")
    @Override
    public ResponseBean<List<RankVO>> list(@RequestBody RankQueryPO qryPO) {
        return ResponseBean.success(rankBiz.list(qryPO));
    }

    @ApiOperation(value = "分页获取头衔列表", httpMethod = "POST", hidden = true)
    @PostMapping("/pc/pageList")
    @Override
    public ResponseBean<PageResult<RankVO>> pageList(@RequestBody RankQueryPO pagePO) {
        return ResponseBean.success(rankBiz.pageList(pagePO));
    }

    @ApiOperation(value = "获取头衔下拉", httpMethod = "POST")
    @PostMapping("/pc/listForSelect")
    public ResponseBean<List<RankSelectVO>> listForSelect(@RequestBody RankSelectPO rankSelectPO) {
        return ResponseBean.success(rankBiz.listForSelect(rankSelectPO.getNeedAll()));
    }
}