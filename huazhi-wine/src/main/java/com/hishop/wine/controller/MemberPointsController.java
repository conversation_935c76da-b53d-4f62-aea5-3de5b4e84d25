package com.hishop.wine.controller;

import com.hishop.common.response.PageResult;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.MemberPointsBiz;
import com.hishop.wine.model.po.points.ChangePointsPO;
import com.hishop.wine.model.po.points.ClearExpirePointsPO;
import com.hishop.wine.model.po.points.MemberPointsQueryPO;
import com.hishop.wine.model.vo.points.MemberPointsDetailsVO;
import com.hishop.wine.model.vo.points.MemberPointsSummaryVO;
import com.hishop.wine.model.vo.points.MemberPointsVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.apache.ibatis.annotations.Param;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Date;

/**
* 会员积分 相关接口
* @author: LiGuoQiang
* @date: 2023-06-25
*/

@Api(value = "MemberPointsController",tags="会员积分表相关接口" )
@RestController
@RequestMapping("/memberPoints")
public class MemberPointsController {

    @Resource
    private MemberPointsBiz memberPointsBiz;

    @ApiOperation(value = "分页获取 会员积分 列表", httpMethod = "POST")
    @PostMapping("/pageList")
    public ResponseBean<PageResult<MemberPointsVO>> pageList(@RequestBody MemberPointsQueryPO pagePo) {
        return ResponseBean.success(memberPointsBiz.pageList(pagePo));
    }

    @ApiOperation(value = "获取用户积分汇总", httpMethod = "POST")
    @PostMapping("/summary")
    public ResponseBean<MemberPointsSummaryVO> summary(@RequestBody MemberPointsQueryPO pagePo) {
        MemberPointsSummaryVO vo = memberPointsBiz.summary(pagePo);
        return ResponseBean.success(vo);
    }

    @ApiOperation(value = "分页获取 分页查询会员积分明细", httpMethod = "POST")
    @PostMapping("/pageDetail")
    public ResponseBean<PageResult<MemberPointsDetailsVO>> pageDetail(@RequestBody MemberPointsQueryPO pagePo) {
        return ResponseBean.success(memberPointsBiz.pageDetail(pagePo));
    }

    @ApiOperation(value = "变更积分-可以是手动调整，也可以是接口形式的发放或扣减", httpMethod = "POST")
    @PostMapping("/change")
    public ResponseBean<Void> change(@RequestBody ChangePointsPO pointsPo) {
        memberPointsBiz.change(pointsPo);
        return ResponseBean.success();
    }

    @ApiOperation(value = "获取用户积分，根据身份隔离", httpMethod = "GET")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, dataType = "Long", paramType = "query"),
            @ApiImplicitParam(name = "identityType", value = "身份类型", required = true, dataType = "Integer", paramType = "query")
    })
    @GetMapping("/getUserPoints")
    public ResponseBean<MemberPointsVO> getUserPoints(@RequestParam("userId") Long userId,
                                                      @RequestParam(name = "identityType") Integer identityType) {
        MemberPointsVO vo = memberPointsBiz.getUserPoints(userId, identityType);
        return ResponseBean.success(vo);
    }

    @ApiOperation(value = "根据业务参数获取积分明细", httpMethod = "GET")
    @GetMapping("/getByBiz")
    public ResponseBean<MemberPointsDetailsVO> getByBiz(@RequestParam("bizType") Integer bizType,
                                                        @RequestParam("bizCode") String bizCode) {
        return ResponseBean.success(memberPointsBiz.getByBiz(bizType, bizCode));
    }

    /**
     * 清除过期积分
     * <AUTHOR>
     * @date 2023/8/3
     */
    @ApiOperation(value = "清除过期积分", httpMethod = "POST", hidden = true)
    @PostMapping("/clearExpirePoints")
    public ResponseBean<Void> clearExpirePoints(@RequestBody @Valid ClearExpirePointsPO clearExpirePointsPO) {
        memberPointsBiz.clearExpirePoints(clearExpirePointsPO.getUserList(), clearExpirePointsPO.getDeadlineTime());
        return ResponseBean.success();
    }

	
}