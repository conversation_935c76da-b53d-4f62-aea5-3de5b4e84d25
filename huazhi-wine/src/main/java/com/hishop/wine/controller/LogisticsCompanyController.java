package com.hishop.wine.controller;

import com.hishop.common.pojo.IdPO;
import com.hishop.common.response.ResponseBean;
import com.hishop.wine.biz.LogisticsCompanyBiz;
import com.hishop.wine.model.vo.logistics.LogisticsCompanyVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 物流公司相关接口
 *
 * @author: chenpeng
 * @date: 2023-07-19
 */
@Api(value = "LogisticsCompanyController", tags = "【物流公司相关接口】")
@RestController
@RequestMapping("/logisticsCompany")
public class LogisticsCompanyController {

    @Resource
    private LogisticsCompanyBiz logisticsCompanyBiz;

    @ApiOperation(value = "PC-选中物流公司", httpMethod = "POST")
    @PostMapping("/pc/select")
    public ResponseBean<Void> select(@Valid @RequestBody IdPO<Long> idPO) {
        logisticsCompanyBiz.select(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "pc-删除已选物流公司", httpMethod = "POST")
    @PostMapping("/pc/delete")
    public ResponseBean<Void> delete(@Valid @RequestBody IdPO<Long> idPO) {
        logisticsCompanyBiz.delete(idPO.getId());
        return ResponseBean.success();
    }

    @ApiOperation(value = "pc-可选物流公司列表", httpMethod = "GET")
    @GetMapping("/pc/unSelected")
    public ResponseBean<List<LogisticsCompanyVO>> unSelected() {
        return ResponseBean.success(logisticsCompanyBiz.unSelected());
    }


    @ApiOperation(value = "PC-获取已选中的物流公司列表", httpMethod = "GET")
    @GetMapping("/pc/listLogisticsCompany")
    public ResponseBean<List<LogisticsCompanyVO>> listLogisticsCompany() {
        return ResponseBean.success(logisticsCompanyBiz.listLogisticsCompany());
    }
}