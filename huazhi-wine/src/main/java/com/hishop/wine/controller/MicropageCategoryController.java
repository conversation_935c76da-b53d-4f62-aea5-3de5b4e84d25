package com.hishop.wine.controller;

import cn.hutool.core.util.StrUtil;
import com.hishop.common.response.ResponseBean;
import com.hishop.log.annotation.OperationLog;
import com.hishop.log.enums.OperationType;
import com.hishop.wine.biz.MicropageBiz;
import com.hishop.wine.biz.MicropageCategoryBiz;
import com.hishop.wine.model.po.micropage.MicropageCategoryDeletePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryMovePO;
import com.hishop.wine.model.po.micropage.MicropageCategoryPO;

import com.hishop.wine.model.po.micropage.MicropageCategoryUpdatePO;
import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

@Api(value = "MicropageController",tags="微页面分组相关接口" )
@RestController
@RequestMapping("/micropage/category")
public class MicropageCategoryController {

    @Resource
    private MicropageCategoryBiz micropageCategoryBiz;
    @ApiOperation("PC-创建微页面分组")
    @PostMapping(path = "/pc/create")
    @OperationLog
    public ResponseBean<Boolean> create(@RequestBody @Valid MicropageCategoryPO micropageCategoryPO) {
        boolean result= micropageCategoryBiz.create(micropageCategoryPO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-修改微页面分组")
    @PostMapping(path = "/pc/update")
    @OperationLog
    public ResponseBean<Boolean> update(@RequestBody @Valid MicropageCategoryUpdatePO micropageCategoryUpdatePO) {
        boolean result= micropageCategoryBiz.update(micropageCategoryUpdatePO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-删除微页面分组")
    @PostMapping(path = "/pc/delete")
    @OperationLog
    public ResponseBean<Boolean> delete(@RequestBody @Valid MicropageCategoryDeletePO MicropageCategoryDeletePO) {
        boolean result= micropageCategoryBiz.delete(MicropageCategoryDeletePO);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-获取微页面分组详情")
    @GetMapping(path = "/pc/detail")
    public ResponseBean<MicropageCategoryVO> detail(@RequestParam @ApiParam("微页面分组id") int id) {
        MicropageCategoryVO result= micropageCategoryBiz.detail(id);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-获取微页面分组列表")
    @GetMapping(path = "/pc/list")
    public ResponseBean<List<MicropageCategoryVO>> list(@RequestParam(required = false) @ApiParam("微页面分组名称") String name) {
        List<MicropageCategoryVO> result= micropageCategoryBiz.list(name);
        return ResponseBean.success(result);
    }

    @ApiOperation("PC-获取微页面分组列表-添加分组时使用，只展示level2级以及以上层级")
    @GetMapping(path = "/pc/list/parent")
    public ResponseBean<List<MicropageCategoryVO>> listParent(@RequestParam(required = false) @ApiParam("移动的分组id集合,以逗号分隔") String ids) {
        //如果没有传id 直接返回 1 2 级
        if(StrUtil.isEmpty(ids)){
            List<MicropageCategoryVO> result= micropageCategoryBiz.listParent();
            return ResponseBean.success(result);
        }
        List<Long> cateIds = Arrays.stream(ids.split(","))
                .map(Long::parseLong)
                .collect(Collectors.toList());

        //数组为空 直接返回 1 2 级
        if(CollectionUtils.isEmpty(cateIds)){
            List<MicropageCategoryVO> result= micropageCategoryBiz.listParent();
            return ResponseBean.success(result);
        }

        List<MicropageCategoryVO> categorys =micropageCategoryBiz.getListByIds(cateIds);
        int maxLevel= categorys.stream().mapToInt(MicropageCategoryVO::getLevel).max()
                .orElse(Integer.MIN_VALUE);

        //取出可选的相关分组
        List<MicropageCategoryVO> canSelectCategory =  micropageCategoryBiz.getListLessThanLevel(maxLevel,cateIds);
        return ResponseBean.success(canSelectCategory);
    }

    @ApiOperation("PC-移动微页面分组")
    @PostMapping(path = "/pc/move")
    @OperationLog(operationType = OperationType.UPDATE, primaryKey = "ids")
    public ResponseBean<Boolean> move(@RequestBody @Valid MicropageCategoryMovePO micropageCategoryMovePO) {
        boolean result= micropageCategoryBiz.move(micropageCategoryMovePO);
        return ResponseBean.success(result);
    }

}
