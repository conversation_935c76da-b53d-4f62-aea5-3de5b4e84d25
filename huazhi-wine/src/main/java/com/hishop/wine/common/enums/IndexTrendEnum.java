package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;



/**
 * <AUTHOR>
 * @Date 2024/3/5 17:06
 * @description 经营趋势查询请求参数
 */
@Getter
@AllArgsConstructor
public enum IndexTrendEnum {

    /**
     * 近七天
     */
    SEVEN_DAYS(1, "近七天"),

    /**
     * 近三十日
     */
    THIRTY_DAYS(2, "近三十日"),

    /**
     * 本月
     */
    THIS_MONTH(3, "本月");


    @EnumValue
    private final Integer status;

    private final String desc;
}
