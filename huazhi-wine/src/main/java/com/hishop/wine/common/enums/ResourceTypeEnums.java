package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * 资源类型枚举
 *
 * <AUTHOR>
 * @date : 2023/6/17
 */
@Getter
public enum ResourceTypeEnums {

    /**
     * 模块
     */
    MODULE(0, "模块"),

    /**
     * 目录
     */
    DIRECTORY(1, "目录"),

    /**
     * 菜单
     */
    MENU(2, "菜单"),

    /**
     * 按钮
     */
    BUTTON(3, "按钮");

    /**
     * 资源类型
     */
    private final Integer type;

    /**
     * 资源名称
     */
    private final String name;

    ResourceTypeEnums(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

}
