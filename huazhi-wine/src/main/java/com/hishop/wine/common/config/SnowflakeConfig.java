package com.hishop.wine.common.config;

import cn.hutool.core.lang.generator.SnowflakeGenerator;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * @date 2023/6/28
 */
@Configuration
public class SnowflakeConfig {

    @Bean
    public SnowflakeGenerator snowflakeIdWorker() {
        return new SnowflakeGenerator(0, 0);
    }

}
