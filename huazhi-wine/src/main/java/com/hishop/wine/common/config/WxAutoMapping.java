package com.hishop.wine.common.config;

import lombok.Data;

/**
 * <AUTHOR>
 * @date : 2023/7/3
 */
@Data
public class WxAutoMapping {

    /**
     * 小程序id
     */
    private String appId;

    /**
     * 微信商户id
     */
    private String mchId;

    /**
     * 要打开的小程序版本。正式版为 "release"，体验版为 "trial"，开发版为 "develop"。默认是正式版。
     */
    private String envVersion;

    /**
     * 是否支持服务商模式
     */
    private Boolean isSupportSec;

    /**
     * 服务商商户id
     */
    private String spMchId;

    /**
     * 服务商小程序id
     */
    private String spAppId;

    /**
     * 支付类型
     */
    private String paymentType;

}
