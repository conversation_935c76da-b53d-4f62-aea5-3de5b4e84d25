package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * 文件导入类型
 *
 * <AUTHOR>
 * @description: 文件导入类型
 * @date: 2024/7/5 14:17
 */
@Getter
public enum FileImportStatus {

    /**
     * 导入中
     */
    PROCESSING(1, "导入中"),

    /**
     * 导入成功
     */
    SUCCESS(2, "导入成功"),

    /**
     * 导入失败
     */
    FAIL(3, "导入失败"),

    /**
     * 部分失败
     */
    PARTIAL_FAIL(4, "部分失败"),

    /**
     * 导入异常
     */
    ERROR(5, "导入异常");

    private final Integer type;

    private final String desc;

    FileImportStatus(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static FileImportStatus getFileImportStatusEnum(Integer type) {
        for (FileImportStatus fileImportType : FileImportStatus.values()) {
            if (fileImportType.getType().equals(type)) {
                return fileImportType;
            }
        }
        return null;
    }
}
