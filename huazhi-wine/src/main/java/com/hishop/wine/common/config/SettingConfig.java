package com.hishop.wine.common.config;

import com.hishop.setting.SettingHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * 配置表config
 *
 * <AUTHOR>
 * @date : 2023/7/14
 */
@Configuration
public class SettingConfig {

    @Bean
    public SettingHelper settingHelper() {
        return new SettingHelper("hishop_basic_setting");
    }

}
