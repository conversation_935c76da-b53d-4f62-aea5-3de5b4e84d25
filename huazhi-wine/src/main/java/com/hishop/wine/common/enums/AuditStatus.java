package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * 审核状态
 *
 * <AUTHOR>
 * @description: 文件导入类型
 * @date: 2024/7/5 14:17
 */
@Getter
public enum AuditStatus {

    /**
     * 审核通过
     */
    PASS(1, "审核通过"),

    /**
     * 审核不通过
     */
    REJECT(2, "审核不通过"),

    /**
     * 审核中
     */
    PROCESSING(3, "审核中");

    private final Integer type;

    private final String desc;

    AuditStatus(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static AuditStatus getAuditStatusEnum(Integer type) {
        for (AuditStatus auditStatus : AuditStatus.values()) {
            if (auditStatus.getType().equals(type)) {
                return auditStatus;
            }
        }
        return null;
    }
}
