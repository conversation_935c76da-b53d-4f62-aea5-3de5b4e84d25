package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum GoodsStatus {

    /**
     * 上架
     */
    OFF_SHELF("OFF_SHELF", "下架"),

    /**
     * 下架
     */
    FOR_SALE("FOR_SALE", "销售中");

    @EnumValue
    private final String value;

    private final String desc;
}
