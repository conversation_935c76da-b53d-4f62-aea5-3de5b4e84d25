package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * 产品媒体资源类型枚举
 *
 * <AUTHOR>
 * @date : 2023/6/19
 */
@Getter
public enum ProductMediaTypeEnums {

    /**
     * 产品图片
     */
    PRODUCT_IMG(1, "产品图片"),

    /**
     * 主图视频
     */
    MAIN_VIDEO(2, "主图视频"),

    /**
     * 详情图
     */
    PRODUCT_DETAIL_IMG(3, "详情图");

    /**
     * 媒体类型
     */
    private final Integer type;

    /**
     * 类型名称
     */
    private final String name;

    ProductMediaTypeEnums(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
