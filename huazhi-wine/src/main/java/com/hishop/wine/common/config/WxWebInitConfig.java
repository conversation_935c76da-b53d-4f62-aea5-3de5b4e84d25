package com.hishop.wine.common.config;

import cn.hutool.extra.spring.SpringUtil;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.setting.SystemSettingVO;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedissonConfigImpl;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * 微信网页应用配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Configuration
@Slf4j
public class WxWebInitConfig extends AbsRefreshSetting {

    @Resource
    private RedissonClient redissonClient;
    @Resource
    private BasicSettingBiz basicSettingBiz;

    private static final String KEY_PREFIX = "weixin_web";

    @Bean(name = "wxWebService")
    public WxMpService getWxService() {
        log.info("【初始化微信网页配置】start");
        SystemSettingVO systemSetting = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
        WxMpService wxMpService = new WxMpServiceImpl();
        WxMpRedissonConfigImpl config = new WxMpRedissonConfigImpl(redissonClient, KEY_PREFIX);
        config.setAppId(systemSetting.getAppId());
        config.setSecret(systemSetting.getAppSecret());
        wxMpService.setWxMpConfigStorage(config);
        log.info("【初始化微信网页配置】success", systemSetting);
        return wxMpService;
    }

    @Override
    public void refresh() {
        log.info("【更新微信网页配置】start");
        SystemSettingVO systemSetting = basicSettingBiz.getSetting(BasicSettingEnum.SYSTEM_SETTING);
        WxMpService wxMpService = SpringUtil.getBean(WxMpService.class);
        WxMpRedissonConfigImpl config = new WxMpRedissonConfigImpl(redissonClient, KEY_PREFIX);
        config.setAppId(systemSetting.getAppId());
        config.setSecret(systemSetting.getAppSecret());
        wxMpService.setWxMpConfigStorage(config);
        log.info("【更新微信网页配置】success", systemSetting);
    }

    @Override
    public String getSettingName() {
        return SettingConstants.WX_WEB_SETTING;
    }
}
