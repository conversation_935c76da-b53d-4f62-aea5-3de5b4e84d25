package com.hishop.wine.common.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.WxMaConfig;
import cn.binarywang.wx.miniapp.config.impl.WxMaDefaultConfigImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedissonConfigImpl;
import cn.hutool.extra.spring.SpringUtil;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.wine.constants.SettingConstants;
import com.hishop.wine.repository.entity.MiniApp;
import com.hishop.wine.repository.service.MiniAppService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RedissonClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信配置
 *
 * <AUTHOR>
 * @date : 2023/6/25
 */
@Configuration
@Slf4j
public class WxMaInitConfig extends AbsRefreshSetting {

    @Resource
    private MiniAppService miniAppService;
    @Resource
    private RedissonClient redissonClient;

    private static final String KEY_PREFIX = "weixin_mini";

    @Bean
    public WxMaService wxMaService() {
        log.info("【初始化小程序配置】start");
        WxMaService wxMaService = new WxMaServiceImpl();

        Map<String, WxMaConfig> configMap = getWxConfigs();
        if (CollectionUtils.isEmpty(configMap)) {
            log.info("【初始化小程序配置】fail, 未读取到小程序配置");
            return wxMaService;
        }

        wxMaService.setMultiConfigs(configMap);
        log.info("【初始化小程序配置】success");
        return wxMaService;
    }

    @Override
    public void refresh() {
        log.info("【更新小程序配置】start");
        WxMaService wxMaService = SpringUtil.getBean(WxMaService.class);
        Map<String, WxMaConfig> configMap = getWxConfigs();
        if (!CollectionUtils.isEmpty(configMap)) {
            wxMaService.setMultiConfigs(configMap);
        }
        log.info("【更新小程序配置】success");
    }

    @Override
    public String getSettingName() {
        return SettingConstants.WX_MA_SETTING;
    }

    /**
     * 组装小程序配置信息
     *
     * @return 小程序配置信息
     */
    private Map<String, WxMaConfig> getWxConfigs() {
        List<MiniApp> configs = miniAppService.list();
        return configs.stream()
                .map(a -> {
                    WxMaDefaultConfigImpl config = new WxMaRedissonConfigImpl(redissonClient, KEY_PREFIX);
                    config.setAppid(a.getAppId());
                    config.setSecret(a.getAppSecret());
                    config.setToken(a.getToken());
                    config.setAesKey(a.getAesKey());
                    config.setMsgDataFormat(a.getMsgDataFormat());
                    return config;
                }).collect(Collectors.toMap(WxMaDefaultConfigImpl::getAppid, a -> a, (o, n) -> o));
    }
}
