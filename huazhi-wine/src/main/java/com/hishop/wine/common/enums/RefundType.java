package com.hishop.wine.common.enums;

import cn.hutool.json.JSONArray;
import lombok.Getter;

import java.util.ArrayList;
import java.util.List;

@Getter
public enum RefundType {

    /**
     * 仅退款
     */
    REFUND_ONLY(1,"仅退款"),
    /**
     * 退货退款
     */
    RETURN_REFUND(2, "退货退款"),
    /**
     * 退款
     */
    REFUND(3, "退款");

    private final Integer type;

    private final String desc;

    RefundType(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static String getDescByType(Integer type) {
        for (RefundType refundType : RefundType.values()) {
            if (refundType.getType().equals(type)) {
                return refundType.getDesc();
            }
        }
        return null;
    }

    public static List<String> getRefundTypeStrList(JSONArray refundTypes) {
        List<String> result = new ArrayList<>();
        for(Object refundType : refundTypes) {
            result.add(RefundType.getDescByType((int)refundType));
        }
        return result;

    }
}
