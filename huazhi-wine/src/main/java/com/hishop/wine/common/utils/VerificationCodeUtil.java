package com.hishop.wine.common.utils;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.hishop.common.util.RedisUtil;

/**
 * 校验验证码
 *
 * <AUTHOR>
 * @date : 2023/7/13
 */
public class VerificationCodeUtil {

    public static void setCode(String redisKey, String code) {
        RedisUtil.set(redisKey, code, 60 * 5);
    }

    public static void checkCode(String redisKey, String inputCode) {
        String code = RedisUtil.get(redisKey);
        Assert.isTrue(ObjectUtil.isNotNull(code), "验证码已过期");
        Assert.isTrue(StrUtil.equals(code, inputCode), "验证码错误");
    }

    public static void removeCode(String redisKey) {
        RedisUtil.del(redisKey);
    }
}
