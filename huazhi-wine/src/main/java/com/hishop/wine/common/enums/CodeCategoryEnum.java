package com.hishop.wine.common.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/08/ $
 * @description: 物流码类别
 */
@Getter
public enum CodeCategoryEnum {

    /**
     * 物流码
     */
    CODE(0, "物流码"),
    /**
     * 门店导入
     */
    SCAN_CODE(1, "门店导入");

    private final Integer type;

    private final String desc;

    CodeCategoryEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public static CodeCategoryEnum getCodeCategoryEnum(Integer type) {
        for (CodeCategoryEnum codeCategoryEnum : CodeCategoryEnum.values()) {
            if (codeCategoryEnum.getType().equals(type)) {
                return codeCategoryEnum;
            }
        }
        return null;
    }

}
