package com.hishop.wine.common.config;

import com.hishop.setting.AbsRefreshSetting;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.setting.OutLinkSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 微信外链配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Configuration
@Slf4j
public class WxOutLinkConfig extends AbsRefreshSetting {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    private Map<String, String> linkMap = new HashMap<>();

    @Override
    public void init() {
        log.info("【初始化微信外链配置】start");
        this.linkMap = getLinkMap();
        log.info("【初始化微信外链配置】success, config: {}", linkMap);
    }

    @Override
    public void refresh() {
        log.info("【更新微信外链配置】start");
        this.linkMap = getLinkMap();
        log.info("【更新微信外链配置】success, config: {}", linkMap);
    }

    @Override
    public String getSettingName() {
        return BasicSettingEnum.OUT_LINK_SETTING.name();
    }

    private Map<String, String> getLinkMap() {
        OutLinkSettingVO setting = basicSettingBiz.getSetting(BasicSettingEnum.OUT_LINK_SETTING);
        return setting.getLinks().stream().collect(Collectors.toMap(OutLinkSettingVO.Link::getKey, OutLinkSettingVO.Link::getValue));
    }

    public String getValue(String key) {
        return linkMap.get(key);
    }
}
