package com.hishop.wine.common.utils;

import com.hishop.wine.model.vo.micropage.MicropageCategoryVO;
import com.hishop.wine.repository.dto.MaterialCategoryDTO;
import com.hishop.wine.repository.dto.MaterialCategoryTreeDTO;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class TreeConverter {
    public static List<MicropageCategoryVO> convertToTreeList(List<MicropageCategoryVO> nodeList, int parentId) {
        Map<Long, List<MicropageCategoryVO>> map = new HashMap<>();

        // 将节点按照父级ID分组
        for (MicropageCategoryVO node : nodeList) {
            map.putIfAbsent(node.getParentId(), new ArrayList<>());

            MicropageCategoryVO categoryVO =new MicropageCategoryVO();
            categoryVO.setId(node.getId());
            categoryVO.setName(node.getName());
            categoryVO.setCreateBy(node.getCreateBy());
            categoryVO.setCreateTime(node.getCreateTime());
            categoryVO.setLevel(node.getLevel());
            categoryVO.setUpdateBy(node.getUpdateBy());
            categoryVO.setFileCount(node.getFileCount());
            categoryVO.setParentId(node.getParentId());
            categoryVO.setUpdateTime(node.getUpdateTime());
            map.get(node.getParentId()).add(categoryVO);
        }

        // 递归构建树形结构
        return buildTree(map, parentId);
    }

    private static List<MicropageCategoryVO> buildTree(Map<Long, List<MicropageCategoryVO>> map, long parentId) {
        List<MicropageCategoryVO> treeList = new ArrayList<>();
        List<MicropageCategoryVO> children = map.get(parentId);

        if (children != null) {
            for (MicropageCategoryVO child : children) {
                List<MicropageCategoryVO> subTree = buildTree(map, child.getId());
                child.setChildCategorys(subTree);
                treeList.add(child);
            }
        }

        return treeList;
    }

    public static List<MaterialCategoryTreeDTO> convertMaterialCategoryToTreeList(List<MaterialCategoryTreeDTO> nodeList, int parentId) {
        Map<Long, List<MaterialCategoryTreeDTO>> map = new HashMap<>();

        // 将节点按照父级ID分组
        for (MaterialCategoryTreeDTO node : nodeList) {
            map.putIfAbsent(node.getParentId(), new ArrayList<>());

            MaterialCategoryTreeDTO categoryVO =new MaterialCategoryTreeDTO();
            categoryVO.setId(node.getId());
            categoryVO.setName(node.getName());
            categoryVO.setCreateTime(node.getCreateTime());
            categoryVO.setCreateUser(node.getCreateUser());
            categoryVO.setLevel(node.getLevel());

            categoryVO.setTotal(node.getTotal());
            categoryVO.setParentId(node.getParentId());

            map.get(node.getParentId()).add(categoryVO);
        }

        // 递归构建树形结构
        return buildMaterialCategoryTree(map, parentId);
    }

    private static List<MaterialCategoryTreeDTO> buildMaterialCategoryTree(Map<Long, List<MaterialCategoryTreeDTO>> map, long parentId) {
        List<MaterialCategoryTreeDTO> treeList = new ArrayList<>();
        List<MaterialCategoryTreeDTO> children = map.get(parentId);

        if (children != null) {
            for (MaterialCategoryTreeDTO child : children) {
                List<MaterialCategoryTreeDTO> subTree = buildMaterialCategoryTree(map, child.getId());
                child.setChildren(subTree);
                treeList.add(child);
            }
        }
        return treeList;
    }

}
