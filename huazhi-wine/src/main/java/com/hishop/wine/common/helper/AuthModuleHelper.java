package com.hishop.wine.common.helper;

import cn.hutool.core.util.ReflectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.hishop.common.constants.SystemConstants;
import com.hishop.common.exception.BusinessException;
import com.hishop.common.util.RedisUtil;
import com.hishop.wine.constants.BasicRedisConstants;
import com.hishop.wine.model.vo.login.AuthModuleVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;

/**
 * 授权模块帮助类
 * <AUTHOR>
 * @date 2023/7/14
 */
@Component
public class AuthModuleHelper {

    /**
     * 授权平台域名配置
     */
    @Value("${hishop.auth.domain}")
    private String authDomain;

    //未授权
    private static final String NO_AUTH_SYSTEM = "-1";

    private static final String AUTH_MODULE_STATE = "state";

    public AuthModuleVO getAuthModule() {
        AuthModuleVO authModuleVO = RedisUtil.get(BasicRedisConstants.AUTH_MODULE_KEY);
//        if(authModuleVO != null){
//            return authModuleVO;
//        }
        authModuleVO = new AuthModuleVO();
        String url = "http://ysc.kuaidiantong.cn/valid.ashx?action=jiukeduo&product=11&host=" + authDomain;
        String result = HttpUtil.post(url, "");
        if(StringUtils.isEmpty(result)){
            return authModuleVO;
        }
        //返回示例： {"state":"1","key":"44900549ce33834edda5289441e55262","openPointsMall":"1","openScanMarketing":"1","openFansClub":"1","openFengtanWine":"1","openFeastWine":"1"}
        // 0 代表授权已过期，1 代表授权时间正常 ， -1表示未授权
        JSONObject jsonObject = JSONUtil.parseObj(result);
        if (NO_AUTH_SYSTEM.equals(jsonObject.getStr(AUTH_MODULE_STATE))){
            throw new BusinessException("未授权酒客多系统，请联系客服授权");
        } else if (SystemConstants.NO.equals(jsonObject.getStr(AUTH_MODULE_STATE))) {
            throw new BusinessException("授权已过期，请联系客服授权");
        } else if (SystemConstants.YES.equals(jsonObject.getStr(AUTH_MODULE_STATE))) {
            //反射获取所有的字段
            Field[] fields = ReflectUtil.getFields(authModuleVO.getClass());
            for (Field field : fields){
                String key = StrUtil.format("open{}", StrUtil.upperFirst(field.getName()));
                //判断json中是否包含该字段
                if (jsonObject.containsKey(key)){
                    //设置字段值
                    ReflectUtil.setFieldValue(authModuleVO, field, jsonObject.getStr(key));
                }
            }
        }
        RedisUtil.set(BasicRedisConstants.AUTH_MODULE_KEY, authModuleVO, BasicRedisConstants.AUTH_MODULE_KEY_EXPIRE);
        return authModuleVO;

    }


}
