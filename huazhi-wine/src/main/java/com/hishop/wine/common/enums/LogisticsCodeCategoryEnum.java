package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/09/ $
 * @description:
 */
@Getter
public enum LogisticsCodeCategoryEnum {

    /**
     * 物流码
     */
    CODE(0, "物流码"),
    /**
     * 扫码营销物流码
     */
    CODE_SCAN(1, "扫码营销物流码")
    ;

    private final Integer value;

    private final String desc;

    LogisticsCodeCategoryEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LogisticsCodeCategoryEnum getLogisticsCodeCategoryEnum(Integer value) {
        for (LogisticsCodeCategoryEnum ogisticsCodeCategoryEnum : LogisticsCodeCategoryEnum.values()) {
            if (ogisticsCodeCategoryEnum.getValue().equals(value)) {
                return ogisticsCodeCategoryEnum;
            }
        }
        return null;
    }

}
