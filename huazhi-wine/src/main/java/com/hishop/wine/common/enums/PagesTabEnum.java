package com.hishop.wine.common.enums;

import com.alibaba.fastjson.annotation.JSONType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

/**
 * <AUTHOR>
 * @date : 2023/7/7
 */
@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT)
public enum PagesTabEnum {

    /**
     * 功能页面
     */
    FUNCTION_PAGES(1, "功能页面"),

    /**
     * 抽奖活动
     */
    MICRO_PAGE(2, "微页面"),

    /**
     * 抽奖活动
     */
    EXCHANGE_ACTIVITY(3, "抽奖活动");

    /**
     * tab类型
     */
    private final Integer type;

    /**
     * tab名称
     */
    private final String name;

    PagesTabEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }
}
