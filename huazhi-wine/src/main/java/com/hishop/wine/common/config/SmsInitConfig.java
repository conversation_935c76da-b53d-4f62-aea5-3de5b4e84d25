package com.hishop.wine.common.config;

import cn.hutool.extra.spring.SpringUtil;
import com.hishop.setting.AbsRefreshSetting;
import com.hishop.sms.api.SmsSender;
import com.hishop.sms.syy.SyySmsSender;
import com.hishop.wine.biz.BasicSettingBiz;
import com.hishop.wine.enums.BasicSettingEnum;
import com.hishop.wine.model.vo.setting.SmsSettingVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;


/**
 * 短信配置
 *
 * <AUTHOR>
 * @date : 2023/7/12
 */
@Configuration
@Slf4j
public class SmsInitConfig extends AbsRefreshSetting {

    @Resource
    private BasicSettingBiz basicSettingBiz;

    @Bean
    public SmsSender syySmsSender() {
        log.info("【初始化短信配置】start");
        SmsSettingVO smsSetting = basicSettingBiz.getSetting(BasicSettingEnum.SMS_SETTING);
        SyySmsSender sender = new SyySmsSender(smsSetting.getAppKey(), smsSetting.getAppSecret());
        log.info("【初始化短信配置】success, config: {}", smsSetting);
        return sender;
    }

    @Override
    public void refresh() {
        log.info("【更新短信配置】start");
        SmsSettingVO smsSetting = basicSettingBiz.getSetting(BasicSettingEnum.SMS_SETTING);
        Map<String, String> config = new HashMap<>(2);
        config.put("appKey", smsSetting.getAppKey());
        config.put("appSecret", smsSetting.getAppSecret());
        SpringUtil.getBean(SmsSender.class).updateConfig(config);
        log.info("【更新短信配置】success, config: {}", smsSetting);
    }

    @Override
    public String getSettingName() {
        return BasicSettingEnum.SMS_SETTING.name();
    }
}
