package com.hishop.wine.common.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * <AUTHOR>
 * @Date 2024/07/09/ $
 * @description:
 */
@Getter
public enum LogisticsCodeStatusEnum {

    /**
     * 未使用
     */
    UNUSED(0, "未使用"),
    /**
     * 已使用
     */
    USED(1, "已使用")
    ;

    private final Integer value;

    private final String desc;

    LogisticsCodeStatusEnum(Integer value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static LogisticsCodeStatusEnum getLogisticsCodeStatusEnum(Integer value) {
        for (LogisticsCodeStatusEnum logisticsCodeStatusEnum : LogisticsCodeStatusEnum.values()) {
            if (logisticsCodeStatusEnum.getValue().equals(value)) {
                return logisticsCodeStatusEnum;
            }
        }
        return null;
    }

}
