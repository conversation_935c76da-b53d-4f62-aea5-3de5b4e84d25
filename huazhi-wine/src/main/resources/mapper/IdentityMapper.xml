<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.IdentityMapper">

    <update id="logicDeleteByIds">
        UPDATE hishop_identity
        SET iz_delete = id, update_time = now(), update_by = #{userId}
        WHERE id IN
        <foreach collection="ids" item= "id" separator="," open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="getPullNewSummary" resultType="com.hishop.wine.repository.dto.PullNewSummaryDTO">
        SELECT
            register_channel,
            <choose>
                <when test="param.totalDimension != null and param.totalDimension == 1">
                    inviter_user_id,
                </when>
                <when test="param.totalDimension != null and param.totalDimension == 2">
                    register_biz_code `biz_code`,
                </when>
                <otherwise>
                    inviter_user_id,
                    register_biz_code `biz_code`,
                </otherwise>
            </choose>
            count(DISTINCT user_id) `number`
        FROM
            hishop_identity
        <where>
            <if test="param.registerChannel != null and param.registerChannel != ''">
                AND register_channel = #{param.registerChannel}
            </if>
            <if test="param.bizCodeList != null and param.bizCodeList.size() > 0">
                AND register_biz_code IN
                <foreach collection="param.bizCodeList" item="bizCode" open="(" close=")" separator=",">
                    #{bizCode}
                </foreach>
            </if>
            <if test="param.inviterUserIdList != null and param.inviterUserIdList.size() > 0">
                AND inviter_user_id IN
                <foreach collection="param.inviterUserIdList" item="inviterUserId" open="(" close=")" separator=",">
                    #{inviterUserId}
                </foreach>
            </if>
            <if test="param.registerTime != null">
                AND create_time &gt;= #{param.registerTime}
            </if>
        </where>
        <choose>
            <when test="param.totalDimension != null and param.totalDimension == 1">
                GROUP BY register_channel, inviter_user_id
            </when>
            <when test="param.totalDimension != null and param.totalDimension == 2">
                GROUP BY register_channel, register_biz_code
            </when>
            <otherwise>
                GROUP BY register_channel, register_biz_code, inviter_user_id
            </otherwise>
        </choose>
    </select>

</mapper>