<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.ModuleBusinessMapper">

    <resultMap type="com.hishop.wine.repository.entity.ModuleBusiness" id="ModuleBusinessMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="moduleCode" column="module_code" jdbcType="VARCHAR"/>
        <result property="moduleName" column="module_name" jdbcType="VARCHAR"/>
        <result property="businessName" column="business_name" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

