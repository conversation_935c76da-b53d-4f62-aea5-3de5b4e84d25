<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.TagsMapper">

    <select id="pageList" resultType="com.hishop.wine.repository.entity.Tags">
        SELECT
            t.*,
            count( ut.id ) userNum
        FROM
            hishop_tags t
        LEFT JOIN hishop_user_tags ut ON t.id = ut.tag_id
        <where>
            <if test="param.tagName != null and param.tagName != ''">
                AND t.tag_name LIKE CONCAT('%', #{param.tagName}, '%')
            </if>
        </where>
        GROUP BY
            t.id
        ORDER BY userNum DESC, t.id ASC
    </select>

    <select id="getMyList" resultType="com.hishop.wine.model.vo.tags.TagsVO">
        SELECT t.* FROM hishop_tags t
        LEFT JOIN hishop_user_tags ut ON t.id = ut.tag_id
        WHERE ut.user_id = #{userId}
    </select>

    <select id="getTopList" resultType="com.hishop.wine.model.vo.tags.TagsVO">
        SELECT a.id, a.tag_name, a.userNum FROM (SELECT t.id, t.tag_name, count(ut.id) as userNum FROM hishop_tags t
        LEFT JOIN hishop_user_tags ut ON t.id = ut.tag_id
        <where>
            <if test="tagName != null and tagName != ''">
                AND t.tag_name LIKE CONCAT('%', #{tagName}, '%')
            </if>
            <if test="tagIds.size() > 0">
            AND (ut.tag_id IS NULL OR
                ut.tag_id NOT IN
                <foreach collection="tagIds" item="tagId" open="(" separator="," close=")">
                    #{tagId}
                </foreach>
                )
            </if>
        </where>
        GROUP BY t.id, t.tag_name) a ORDER BY a.userNum DESC, a.id ASC
        LIMIT 0, #{num}
    </select>

</mapper>