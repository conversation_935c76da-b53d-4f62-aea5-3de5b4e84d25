<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.TransactionMapper">


    <update id="resetRefundData">
        UPDATE hishop_transaction t
        SET t.refund_amount = ( SELECT * FROM (SELECT SUM( t2.amount ) FROM hishop_transaction t2 WHERE t2.org_transaction_id = #{transactionId} AND t2.`status` != 3) temp ),
            t.refund_status = ( CASE t.refund_amount WHEN 0 THEN 1 WHEN t.amount > t.refund_amount THEN 2 ELSE 3 END )
        WHERE
            t.id = #{transactionId}
    </update>

    <select id="getTransactionTotal" resultType="com.hishop.wine.repository.dto.transaction.TransactionTotalDto">
        SELECT count(1) AS payOrderCount, count(DISTINCT user_id) AS payUserCount
        , IFNULL(SUM(amount), '0') AS payOrderAmount
        FROM hishop_transaction
        WHERE transaction_type = 'PAY'
        AND `status` = 2
        AND finish_time >= #{startTime}
        AND finish_time &lt; #{endTime}
    </select>

    <select id="queryTransactionTotalList" resultType="com.hishop.wine.repository.dto.transaction.TransactionTotalDto">
        SELECT DATE_FORMAT(finish_time, '%Y-%m-%d') AS dateTime, count(1) AS payOrderCount
             , count(DISTINCT user_id) AS payUserCount
             , IFNULL(SUM(amount), '0') AS payOrderAmount
        FROM hishop_transaction
        WHERE transaction_type = 'PAY'
          AND `status` = 2
          AND finish_time >= #{startTime}
          AND finish_time &lt; #{endTime}
        GROUP BY DATE_FORMAT(finish_time, '%Y-%m-%d')
    </select>
</mapper>