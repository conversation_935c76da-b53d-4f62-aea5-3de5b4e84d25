<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MiniUserMapper">

    <resultMap type="com.hishop.wine.repository.entity.MiniUser" id="MiniUserMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="userId" column="user_id" jdbcType="INTEGER"/>
        <result property="nickName" column="nick_name" jdbcType="VARCHAR"/>
        <result property="avatarUrl" column="avatar_url" jdbcType="VARCHAR"/>
        <result property="appId" column="app_id" jdbcType="VARCHAR"/>
        <result property="registerModuleCode" column="register_module_code" jdbcType="VARCHAR"/>
        <result property="unionId" column="union_id" jdbcType="VARCHAR"/>
        <result property="openId" column="open_id" jdbcType="VARCHAR"/>
        <result property="sessionKey" column="session_key" jdbcType="VARCHAR"/>
        <result property="izBlacklist" column="iz_blacklist" jdbcType="BOOLEAN"/>
        <result property="blacklistDate" column="blacklist_date" jdbcType="TIMESTAMP"/>
        <result property="blacklistOperateId" column="blacklist_operate_id" jdbcType="INTEGER"/>
        <result property="blacklistOperateName" column="blacklist_operate_name" jdbcType="VARCHAR"/>
        <result property="blacklistOperatePhone" column="blacklist_operate_phone" jdbcType="VARCHAR"/>
        <result property="userNo" column="user_no" jdbcType="VARCHAR"/>
        <result property="remarkName" column="remark_name" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="izDelete" column="iz_delete" jdbcType="BOOLEAN"/>
    </resultMap>

    <select id="getMiniUserByUserIdAndModuleCode" resultType="com.hishop.wine.model.vo.basic.MiniUserVO">
        SELECT
            hmu.id,
            hmu.user_id,
            hmu.nick_name,
            hmu.avatar_url,
            hmu.app_id,
            hmu.register_module_code,
            hmu.union_id,
            hmu.open_id,
            hmu.session_key,
            hmu.iz_blacklist,
            hmu.blacklist_date,
            hmu.blacklist_operate_id,
            hmu.blacklist_operate_name,
            hmu.blacklist_operate_phone,
            hmu.create_by,
            hmu.update_by,
            hmu.create_time,
            hmu.update_time,
            hmu.iz_delete,
            hmu.remark_name,
            hu.mobile,
            hu.biz_user_code,
            hu.biz_user_name,
            hu.biz_phone
        FROM
            hishop_mini_user hmu
                LEFT JOIN hishop_module hm ON hmu.app_id = hm.app_id
                LEFT JOIN hishop_user hu ON hmu.user_id = hu.id
        WHERE
            hmu.iz_delete = 0 AND hmu.user_id = #{userId} AND hm.module_code = #{moduleCode}
    </select>

    <select id="getUserDetail" resultType="com.hishop.wine.model.vo.basic.MiniUserVO">
        SELECT
            hmu.id,
            hmu.user_id,
            hmu.nick_name,
            hmu.avatar_url,
            hmu.app_id,
            hmu.register_module_code,
            hmu.union_id,
            hmu.open_id,
            hmu.session_key,
            hmu.iz_blacklist,
            hmu.blacklist_date,
            hmu.blacklist_operate_id,
            hmu.blacklist_operate_name,
            hmu.blacklist_operate_phone,
            hmu.create_by,
            hmu.update_by,
            hmu.create_time,
            hmu.update_time,
            hmu.iz_delete,
            hmu.remark_name,
            hu.mobile
        FROM
            hishop_mini_user hmu
                LEFT JOIN hishop_user hu ON hmu.user_id = hu.id
        WHERE
            hmu.iz_delete = 0 AND hmu.id = #{id}
    </select>

    <select id="queryPage" resultType="com.hishop.wine.model.vo.basic.MiniUserVO">
        SELECT
            hmu.id,
            hmu.user_id,
            hmu.nick_name,
            hmu.avatar_url,
            hmu.app_id,
            hmu.register_module_code,
            hmu.union_id,
            hmu.open_id,
            hmu.session_key,
            hmu.iz_blacklist,
            hmu.blacklist_date,
            hmu.blacklist_operate_id,
            hmu.blacklist_operate_name,
            hmu.blacklist_operate_phone,
            hmu.create_by,
            hmu.update_by,
            hmu.create_time,
            hmu.update_time,
            hmu.iz_delete,
            hmu.remark_name,
            hu.mobile,
            hu.biz_user_code,
            hu.biz_user_name,
            hu.biz_phone
        FROM
            hishop_mini_user hmu
        LEFT JOIN hishop_user hu ON hmu.user_id = hu.id
        WHERE
            hmu.iz_delete = 0
        <if test="param.nickName != null and param.nickName != ''">
            AND hmu.nick_name LIKE CONCAT('%',#{param.nickName},'%')
        </if>
        <if test="param.bizUserCode != null and param.bizUserCode != ''">
            AND hu.biz_user_code LIKE CONCAT('%',#{param.bizUserCode},'%')
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            AND hu.mobile LIKE CONCAT('%',#{param.mobile},'%')
        </if>
        <if test="param.tagIds != null and param.tagIds.size > 0">
            AND hmu.user_id IN (
            SELECT hut.user_id FROM hishop_user_tags hut WHERE hut.tag_id IN
            <foreach collection="param.tagIds" item= "id" separator="," open="(" close=")">
                #{id}
            </foreach>
            )
        </if>
        <if test="param.sortSql != null and param.sortSql != ''">
            ${param.sortSql}
        </if>

    </select>

    <select id="blacklistPage" resultType="com.hishop.wine.model.vo.minUser.MiniUserBlacklistVo">
        SELECT
        hmu.id,
        hmu.user_id,
        hmu.nick_name,
        hmu.avatar_url,
        hmu.app_id,
        hmu.register_module_code,
        hmu.union_id,
        hmu.open_id,
        hmu.session_key,
        hmu.iz_blacklist,
        hmu.blacklist_date,
        hmu.blacklist_operate_id,
        hmu.blacklist_operate_name,
        hmu.blacklist_operate_phone,
        hmu.create_by,
        hmu.update_by,
        hmu.create_time,
        hmu.update_time,
        hmu.iz_delete,
        hmu.remark_name,
        hu.mobile
        FROM
        hishop_mini_user hmu
        LEFT JOIN hishop_user hu ON hmu.user_id = hu.id
        WHERE
        hmu.iz_delete = 0 AND hmu.iz_blacklist = 1
        <if test="param.nickName != null and param.nickName != ''">
            AND hmu.nick_name LIKE CONCAT('%',#{param.nickName},'%')
        </if>
        <if test="param.mobile != null and param.mobile != ''">
            AND hu.mobile LIKE CONCAT('%',#{param.mobile},'%')
        </if>
        <if test="param.startTime != null">
            AND hmu.blacklist_date >= #{param.startTime}
        </if>
        <if test="param.endTime != null">
            AND hmu.blacklist_date &lt;= #{param.endTime}
        </if>
        <if test="param.sortSql != null and param.sortSql != ''">
            ${param.sortSql}
        </if>
    </select>

    <select id="getMiniUserCount" resultType="java.lang.Long">
        SELECT count(DISTINCT t1.mobile)
        FROM hishop_mini_user t
        JOIN hishop_user t1 on t.user_id = t1.id
        WHERE t1.iz_delete = 0 and t.iz_delete = 0
    </select>
</mapper>

