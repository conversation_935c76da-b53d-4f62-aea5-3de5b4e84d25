<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.ModuleMapper">

    <select id="listModuleWxParam" resultType="com.hishop.wine.repository.dto.module.ModuleWxParamDTO">
        SELECT
            m.module_code,
            m.app_id,
            a.env_version,
            s.setting_value ->> '$.mchId' `mchId`,
            s.setting_value ->> '$.spAppId' `spAppId`,
            s.setting_value ->> '$.spMchId' `spMchId`,
            s.payment_type
        FROM
            hishop_module m
        LEFT JOIN hishop_mini_app a ON m.app_id = a.app_id
        LEFT JOIN hishop_mini_app_payment ap ON a.app_id = ap.app_id
        LEFT JOIN hishop_payment_setting s ON ap.payment_id = s.id
        WHERE s.payment_type != 'OFFLINE'
    </select>

</mapper>