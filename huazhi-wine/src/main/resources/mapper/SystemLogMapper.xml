<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.SystemLogMapper">

    <select id="qryPage" resultType="com.hishop.wine.repository.entity.SystemLog">
        SELECT
            *
        FROM
            hishop_system_log
        <where>
            <if test="param.moduleCode != null and param.moduleCode != ''">
                AND module_code = #{param.moduleCode}
            </if>
            <if test="param.businessDesc != null and param.businessDesc != ''">
                AND business_desc LIKE CONCAT('%', #{param.businessDesc}, '%')
            </if>
            <if test="param.operationName != null and param.operationName != ''">
                AND operation_name LIKE CONCAT('%', #{param.operationName}, '%')
            </if>
            <if test="param.operationUsername != null and param.operationUsername != ''">
                AND operator_username LIKE CONCAT('%', #{param.operationUsername}, '%')
            </if>
            <if test="param.startTime != null">
                AND create_time &gt;= #{param.startTime}
            </if>
            <if test="param.endTime != null">
                AND create_time &lt;= #{param.endTime}
            </if>
        </where>
        ORDER BY id DESC
    </select>

</mapper>