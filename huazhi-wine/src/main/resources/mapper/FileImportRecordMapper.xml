<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.FileImportRecordMapper">

    <select id="queryFileImportCodePageList" resultType="com.hishop.wine.model.vo.fileImport.FileImportRecordVo">
        SELECT DISTINCT t.`name`, t.id
        FROM hishop_file_import_record t
                 JOIN hishop_logistics_code t1 on t.id = t1.file_import_id
        WHERE t.iz_delete = 0
          and t.import_status in (2,4)
          and t.import_type in ('LOGISTICS_CODE', 'LOGISTICS_CODE_SCAN')
          and t1.iz_delete = 0
          <if test="param.productCode != null and param.productCode !=''">
              and t1.product_code = #{param.productCode}
          </if>

    </select>
</mapper>

