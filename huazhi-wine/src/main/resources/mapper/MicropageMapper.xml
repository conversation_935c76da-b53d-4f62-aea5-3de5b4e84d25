<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MicropageMapper">

    <select id="countFile" resultType="long">
        SELECT
            COUNT(*)
        FROM
            hishop_micropage
        WHERE
            iz_delete = 0
        <if test="status != null or status == 0">
            AND status = #{status}
        </if>
        <if test="categoryId != null or categoryId == 0">
            AND category_id = #{categoryId}
        </if>
    </select>

    <update id="updateVisitCount">
        <foreach collection="param.entrySet()" item="value" index="key" separator=";">
            UPDATE hishop_micropage m
            SET m.pv = m.pv + #{value},
            m.uv = (SELECT COUNT(v.user_id) FROM hishop_micropage_visit v WHERE v.micropage_id = #{key})
            WHERE
            m.id = #{key}
        </foreach>
    </update>
</mapper>