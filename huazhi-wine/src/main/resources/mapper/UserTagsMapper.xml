<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.UserTagsMapper">

    <select id="userTagsCount" resultType="com.hishop.wine.repository.dto.TagsCountDTO">
        SELECT t.user_id, COUNT(t.id) num FROM hishop_user_tags t
        WHERE 1=1
          AND t.user_id in
        <foreach collection="userSet" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
        GROUP BY t.user_id
    </select>
</mapper>