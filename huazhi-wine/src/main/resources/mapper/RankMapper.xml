<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.RankMapper">

    <sql id="list_sql">
        SELECT
            r.*,
            count(u.id) `relateUserNum`
        FROM
            hishop_rank r
        LEFT JOIN hishop_user u ON r.id = u.rank_id
        <where>
            <if test="param.moduleCode != null and param.moduleCode != ''">
                AND r.module_code = #{param.moduleCode}
            </if>
            <if test="param.rankName != null and param.rankName != ''">
                AND r.rank_name LIKE CONCAT('%', #{param.rankName}, '%')
            </if>
            AND r.iz_delete = 0
        </where>
        GROUP BY
        r.id
    </sql>

    <select id="qryPage" resultType="com.hishop.wine.repository.entity.Rank">
       <include refid="list_sql"/>
    </select>

    <select id="qryList" resultType="com.hishop.wine.repository.entity.Rank">
        <include refid="list_sql"/>
    </select>

</mapper>