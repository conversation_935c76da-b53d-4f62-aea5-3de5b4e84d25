<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.RoleMapper">

    <select id="listRoleIncludeUserNum" resultType="com.hishop.wine.repository.entity.Role">
        SELECT
            r.*,
            count(i.id) `num`
        FROM
            hishop_role r
        LEFT JOIN hishop_identity i ON r.id = i.role_id AND i.iz_delete = 0 AND i.identity_type = 1
        WHERE r.iz_delete = 0
        AND r.status = 1
        GROUP BY r.id
    </select>

</mapper>