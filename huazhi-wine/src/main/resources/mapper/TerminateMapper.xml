<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.TerminateMapper">

    <select id="queryTerminatePageList" resultType="com.hishop.wine.model.vo.terminate.TerminateVo">
        SELECT t.id, t.code, t.name, t.province_id, t.province_name
        , t.city_id, t.city_name, t.district_name, t.district_id, t.address
        , t.iz_enable, t1.real_name,t.phone, t.duty_name, t2.duty_name as dealerName
        , t2.phone as dealerPhone, t1.real_name as businessUserName
        , t1.mobile as businessUserPhone,t.audit_status as audit_status_type
        FROM hishop_terminate t
        LEFT JOIN hishop_user t1 ON t.business_user_id = t1.id
        LEFT JOIN hishop_dealer t2 ON t.dealer_id = t2.id
        WHERE t.iz_delete = 0
        <if test="param.dutyNameOrPhone != null and param.dutyNameOrPhone != ''">
            AND (t.phone like CONCAT('%', #{param.dutyNameOrPhone}, '%') OR t.duty_name like CONCAT('%', #{param.dutyNameOrPhone}, '%'))
        </if>
        <if test="param.terminateCodeOrName != null and param.terminateCodeOrName != ''">
            AND (t.code like CONCAT('%', #{param.terminateCodeOrName}, '%') OR t.name like CONCAT('%', #{param.terminateCodeOrName}, '%'))
        </if>
        <if test="param.izEnable != null ">
            AND t.iz_enable = #{param.izEnable}
        </if>
        <if test="param.source != null ">
            AND t.source = #{param.source}
        </if>
        <if test="param.auditStatusList != null and param.auditStatusList.size() > 0 ">
            AND t.audit_status in
            <foreach collection="param.auditStatusList" item= "item" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <if test="param.provinceId != null ">
            AND t.province_id = #{param.provinceId}
        </if>
        <if test="param.cityId != null ">
            AND t.city_id = #{param.cityId}
        </if>
        <if test="param.districtId != null ">
            AND t.district_id = #{param.districtId}
        </if>
        <if test="param.businessUserId != null ">
            AND t.business_user_id = #{param.businessUserId}
        </if>
        <if test="param.dealerId != null ">
            AND t.dealer_id = #{param.dealerId}
        </if>
        ORDER BY t.create_time DESC
    </select>

</mapper>

