<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.LogisticsCodeBottleMapper">

    <resultMap type="com.hishop.wine.repository.entity.LogisticsCodeBottle" id="LogisticsCodeBottleMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="codeBottle" column="code_bottle" jdbcType="VARCHAR"/>
        <result property="logisticsCodeId" column="logistics_code_id" jdbcType="INTEGER"/>
        <result property="izDelete" column="iz_delete" jdbcType="INTEGER"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

