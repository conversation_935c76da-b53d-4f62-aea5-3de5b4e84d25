<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.DistrictMapper">

    <update id="updateBatchSomeColumn">
        <foreach collection="districtList" item= "district" separator=";">
            UPDATE `hishop_district`
            SET
            `name` = #{district.name},
            `full_name` = #{district.fullName},
            `parent_id` = #{district.parentId},
            `parent_ids` = #{district.parentIds},
            `lng` = #{district.lng},
            `lat` = #{district.lat},
            `level` = #{district.level},
            `has_child` = #{district.hasChild},
            `update_by` = #{district.updateBy},
            `update_time` = #{district.updateTime}
            WHERE
            `id` = #{district.id}
        </foreach>
    </update>

    <update id="updateChildName">
        UPDATE hishop_district SET full_name = REPLACE(full_name, #{oldFullName}, #{newFullName}) WHERE FIND_IN_SET(#{id}, parent_ids)
    </update>

</mapper>