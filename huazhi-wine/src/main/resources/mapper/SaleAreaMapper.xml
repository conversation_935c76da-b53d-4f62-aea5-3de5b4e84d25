<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.SaleAreaMapper">

    <resultMap type="com.hishop.wine.repository.entity.SaleArea" id="SaleAreaMap">
        <result property="id" column="id" jdbcType="INTEGER"/>
        <result property="saleDimId" column="sale_dim_id" jdbcType="INTEGER"/>
        <result property="areaName" column="area_name" jdbcType="VARCHAR"/>
        <result property="areaCode" column="area_code" jdbcType="VARCHAR"/>
        <result property="parentId" column="parent_id" jdbcType="VARCHAR"/>
        <result property="fullCode" column="full_code" jdbcType="VARCHAR"/>
        <result property="izDelete" column="iz_delete" jdbcType="VARCHAR"/>
        <result property="createBy" column="create_by" jdbcType="INTEGER"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="INTEGER"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

</mapper>

