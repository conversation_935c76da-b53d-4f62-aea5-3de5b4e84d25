<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.PaymentSettingMapper">

    <select id="listAll" resultType="com.hishop.wine.repository.dto.payment.PaymentSettingDTO">
        SELECT
            s.id,
            s.payment_name,
            s.payment_type,
            IFNULL(GROUP_CONCAT(m.app_name), '-') `relateMiniNames`
        FROM
            hishop_payment_setting s
        LEFT JOIN hishop_mini_app_payment ap ON s.id = ap.payment_id
        LEFT JOIN hishop_mini_app m ON ap.app_id = m.app_id
        GROUP BY s.id
    </select>

    <select id="listForOfflineSelect" resultType="com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO">
        SELECT
            ap.payment_id,
            s.offline_channel,
            s.setting_value ->> '$.beneficiaryName' `beneficiaryName`,
            s.setting_value ->> '$.beneficiaryAccount' `beneficiaryAccount`,
            s.setting_value ->> '$.bankCardNumber' `bankCardNumber`,
            s.setting_value ->> '$.qrCode' `qrCode`,
            s.setting_value ->> '$.bankName' `bankName`
        FROM
            hishop_module m
        JOIN hishop_mini_app a ON m.app_id = a.app_id
        JOIN hishop_mini_app_payment ap ON a.app_id = ap.app_id
        JOIN hishop_payment_setting s ON ap.payment_id = s.id and s.payment_type = 'OFFLINE'
        WHERE m.module_code = #{moduleCode} and s.iz_special = 0
    </select>

    <select id="listForOfflineSelectAll" resultType="com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO">
        SELECT
            ap.payment_id,
            s.offline_channel,
            s.setting_value ->> '$.beneficiaryName' `beneficiaryName`,
            s.setting_value ->> '$.beneficiaryAccount' `beneficiaryAccount`,
            s.setting_value ->> '$.bankCardNumber' `bankCardNumber`,
            s.setting_value ->> '$.qrCode' `qrCode`,
            s.setting_value ->> '$.bankName' `bankName`
        FROM
            hishop_module m
            JOIN hishop_mini_app a ON m.app_id = a.app_id
            JOIN hishop_mini_app_payment ap ON a.app_id = ap.app_id
            JOIN hishop_payment_setting s ON ap.payment_id = s.id and s.payment_type = 'OFFLINE'
        WHERE m.module_code = #{moduleCode}
    </select>

    <select id="listForOfflineSelectBySpecialCode" resultType="com.hishop.wine.repository.dto.payment.PaymentSettingOfflineDTO">
        SELECT
            ap.payment_id,
            s.offline_channel,
            s.setting_value ->> '$.beneficiaryName' `beneficiaryName`,
            s.setting_value ->> '$.beneficiaryAccount' `beneficiaryAccount`,
            s.setting_value ->> '$.bankCardNumber' `bankCardNumber`,
            s.setting_value ->> '$.qrCode' `qrCode`,
            s.setting_value ->> '$.bankName' `bankName`
        FROM
            hishop_module m
            JOIN hishop_mini_app a ON m.app_id = a.app_id
            JOIN hishop_mini_app_payment ap ON a.app_id = ap.app_id
            JOIN hishop_payment_setting s ON ap.payment_id = s.id and s.payment_type = 'OFFLINE'
        WHERE m.module_code = #{moduleCode} and s.special_code = #{specialCode}
    </select>


</mapper>