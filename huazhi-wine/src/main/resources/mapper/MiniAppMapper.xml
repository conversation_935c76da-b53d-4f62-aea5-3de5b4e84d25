<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MiniAppMapper">

    <select id="listMiniApp" resultType="com.hishop.wine.repository.dto.miniApp.MiniAppDTO">
        SELECT
            a.id,
            a.app_id,
            a.app_name,
            GROUP_CONCAT(m.module_name SEPARATOR ';') moduleNames,
            a.setting_module_code,
            a.env_version
        FROM
            hishop_mini_app a
        LEFT JOIN hishop_module m ON a.app_id = m.app_id
        GROUP BY a.app_id
    </select>

    <select id="getBindModuleNames" resultType="string">
        SELECT IFNULL(GROUP_CONCAT(module_name separator '，'), '') FROM hishop_module WHERE app_id = #{appId} and module_code != 'basic_system'
    </select>

    <select id="listMiniAppModule" resultType="com.hishop.wine.repository.dto.miniApp.MiniAppModuleDTO">
        SELECT
            a.id,
            a.app_id,
            a.app_name,
            m.module_code,
            m.module_name
        FROM
            hishop_mini_app a
        LEFT JOIN hishop_module m ON a.app_id = m.app_id
    </select>
</mapper>