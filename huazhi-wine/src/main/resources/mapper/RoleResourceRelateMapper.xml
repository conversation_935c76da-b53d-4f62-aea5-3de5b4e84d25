<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.RoleResourceRelateMapper">

    <select id="listResourceIdsByRoleId" resultType="long">
        SELECT resource_id FROM hishop_role_resource WHERE role_id = #{roleId}
    </select>

    <select id="checkResourceAuth" resultType="integer">
        SELECT
            count(r.id)
        FROM
            hishop_resource r
        INNER JOIN hishop_role_resource rr ON r.id = rr.resource_id
        WHERE rr.role_id = #{roleId}
        AND FIND_IN_SET(#{resourceId}, r.parent_ids)
    </select>

</mapper>