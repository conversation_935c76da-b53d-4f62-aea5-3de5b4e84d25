<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hishop.wine.repository.dao.MicropageVisitMapper">

    <insert id="saveVisitLog">
        <foreach collection="visitList" item="item" index="index" separator=";">
        INSERT INTO hishop_micropage_visit ( micropage_id, user_id, create_time, update_time)
        VALUES (#{item.micropageId}, #{item.userId}, #{item.createTime}, #{item.updateTime})
        ON DUPLICATE KEY UPDATE update_time = #{item.updateTime}, visit_count = visit_count + 1
        </foreach>
    </insert>

</mapper>