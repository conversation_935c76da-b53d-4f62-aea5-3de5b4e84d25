server:
  # 开启压缩
  compression:
    enabled: true
    min-response-size: 2048
  #上下文路径
  servlet:
    context-path: /wine
  port: 80
spring:
  application:
    name: wine
  profiles:
    active: test
  main:
    allow-bean-definition-overriding: true
  datasource:
    url: jdbc:mysql://${MYSQL_SERVER}:${MYSQL_PORT:3306}/${MYSQL_SCHEMA:hishop_wine_basic}?allowMultiQueries=true&useSSL=false&useUnicode=true&characterEncoding=UTF-8&autoReconnect=true&zeroDateTimeBehavior=convertToNull&useJDBCCompliantTimezoneShift=true&useLegacyDatetimeCode=false&serverTimezone=GMT%2B8&nullCatalogMeansCurrent=true
    username: ${MYSQL_USERNAME}
    password: ${MYSQL_PASSWORD}
    driver-class-name: com.mysql.cj.jdbc.Driver
    type: com.zaxxer.hikari.HikariDataSource
    hikari:
      minimum-idle: 0
      maximum-pool-size: 20
      idle-timeout: 10000
      auto-commit: true
      connection-test-query: SELECT 1
  redis:
    host: ${REDIS_SERVER}
    port: ${REDIS_PORT}
    database: ${REDIS_DATABASE}
  #文件上传设置
  servlet:
    multipart:
      max-file-size: 100MB
      max-request-size: 100MB
      enabled: true
# mybaits-plus配置
mybatis-plus:
  configuration:
    default-enum-type-handler: org.apache.ibatis.type.EnumOrdinalTypeHandler
swagger:
  knife4j:
    enable: true
    apiInfo:
      title: 酒客多-基础系统接口文档
      description: 酒客多-基础系统接口文档
      version: v1.0
      apiUrl: http://127.0.0.1:8080/wine/doc.html
      baseApiPkg: com.hishop.wine.controller
      groupName: hishopBasic
      contactEmail: <EMAIL>
      contactName: hishop
rocketmq:
  name-server: ${ROCKETMQ_SERVER_ADDRESS}
  producer:
    group: ${ROCKETMQ_PRODUCER_GROUP}
hishop:
  wx:
    notify:
      pay:
        url: ${HISHOP_WX_NOTIFY_PAY_URL}
      refund:
        url: ${HISHOP_WX_NOTIFY_REFUND_URL}
  email:
    host: ${HISHOP_EMAIL_HOST}
    port: ${HISHOP_EMAIL_PORT}
    username: ${HISHOP_EMAIL_USERNAME}
    password: ${HISHOP_EMAIL_PASSWORD}
  auth:
    domain: ${HISHOP_AUTH_DOMAIN}
  h5:
    domain: ${HISHOP_H5_DOMAIN}
  default:
    path: ${HISHOP_DEFAULT_PATH}
  setting:
    mode: ${HISHOP_SETTING_MODE}
  jiukeduo:
    website: ${HISHOP_JIUKEDUO_WEBSITE}
nfs:
  obs:
    accessKeyId: ${NFS_OBS_ACCESS_KEY_ID}
    accessKeySecret: ${NFS_OBS_ACCESS_KEY_SECRET}
    bucketName: ${NFS_OBS_BUCKET_NAME}
    endPoint: ${NFS_OBS_END_POINT}
    domain: ${NFS_OBS_DOMAIN}
    region: ${NFS_OBS_REGION}
  mpc:
    productId: ${NFS_MPC_PRODUCT_ID}
    endpoint: ${NFS_MPC_END_POINT}
    templateId: ${NFS_MPC_TEMPLATE_ID}
    audioTemplateId: ${NFS_MPC_AUDIO_TEMPLATE_ID}
wx:
  mini:
    config: ${WX_MINI_CONFIG}
    tokenUrl: ${WX_MINI_TOKEN_URL}
    uploadUrl: ${WX_MINI_UPLOAD_URL}
feign:
  url:
    fengtan: ${FEIGN_URL_FENGTAN}