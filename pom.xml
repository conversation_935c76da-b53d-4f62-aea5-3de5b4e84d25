<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <artifactId>huazhi-common-dependincies</artifactId>
        <groupId>com.hishop</groupId>
        <version>1.0.1-SNAPSHOT</version>
    </parent>

    <artifactId>huazhi-basic-system</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>


    <modules>
        <module>huazhi-api</module>
        <module>huazhi-wine</module>
    </modules>

    <dependencyManagement>
<!--        <dependencies>-->
<!--            <dependency>-->
<!--                <groupId>com.hishop</groupId>-->
<!--                <artifactId>hishop-bom</artifactId>-->
<!--                <version>${project.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--            <dependency>-->
<!--                <groupId>org.springframework.cloud</groupId>-->
<!--                <artifactId>spring-cloud-dependencies</artifactId>-->
<!--                <version>${spring-cloud.version}</version>-->
<!--                <type>pom</type>-->
<!--                <scope>import</scope>-->
<!--            </dependency>-->
<!--        </dependencies>-->
    </dependencyManagement>

    <distributionManagement>
        <snapshotRepository>
            <id>maven-snapshots</id>
            <url>http://124.71.221.117:8081/repository/maven-snapshots/</url>
        </snapshotRepository>
    </distributionManagement>



</project>
